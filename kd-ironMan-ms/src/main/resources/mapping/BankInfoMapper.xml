<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.BankInfoDao">
	
	<insert id="insertSelective" parameterType="kd.entity.BankInfo">
		<include refid="mapping.BankInfoMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.BankInfo">
		<include refid="mapping.BankInfoMapper.updateByPrimaryKeySelective" />
	</update>
	
	
</mapper>