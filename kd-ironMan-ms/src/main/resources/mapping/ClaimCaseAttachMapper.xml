<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.ClaimCaseAttachDao">
	
	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseAttach">
		<include refid="mapping.ClaimCaseAttachMapper.insertSelective" />
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseAttach">
		<include refid="mapping.ClaimCaseAttachMapper.updateByPrimaryKeySelective" />
	</update>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseAttachMapper.deleteByPrimaryKey"/>
	</delete>

	<select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseAttachMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseAttachMapper.selectByPrimaryKey" />
	</select>

</mapper>