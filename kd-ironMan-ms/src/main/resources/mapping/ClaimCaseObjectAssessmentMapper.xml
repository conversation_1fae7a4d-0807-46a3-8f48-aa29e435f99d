<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.ClaimCaseObjectAssessmentDao">

	<select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseObjectAssessmentMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseObjectAssessmentMapper.selectByPrimaryKey" />
	</select>

	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseObjectAssessment">
		<include refid="mapping.ClaimCaseObjectAssessmentMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseObjectAssessment">
		<include refid="mapping.ClaimCaseObjectAssessmentMapper.updateByPrimaryKeySelective" />
	</update>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseObjectAssessmentMapper.deleteByPrimaryKey" />
	</delete>

</mapper>