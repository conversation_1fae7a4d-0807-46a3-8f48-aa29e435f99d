<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.ClaimCaseObjectDao">

	<select id="selectByPrimaryKey" resultMap="mapping.ClaimCaseObjectMapper.BaseResultMap" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseObjectMapper.selectByPrimaryKey" />
	</select>

	<insert id="insertSelective" parameterType="kd.entity.ClaimCaseObject">
		<include refid="mapping.ClaimCaseObjectMapper.insertSelective" />
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.ClaimCaseObject">
		<include refid="mapping.ClaimCaseObjectMapper.updateByPrimaryKeySelective" />
	</update>

	<update id="updateByPrimaryKeySelectiveAuditerCanNull" parameterType="kd.entity.ClaimCaseObject">
		update t_claim_case_object
		<set >
			<if test="insCode != null" >
				ins_code = #{insCode,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseId != null" >
				claim_case_id = #{claimCaseId,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseNo != null" >
				claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
			</if>
			<if test="insClaimCaseNo != null" >
				ins_claim_case_no = #{insClaimCaseNo,jdbcType=VARCHAR},
			</if>
			<if test="policyNo != null" >
				policy_no = #{policyNo,jdbcType=VARCHAR},
			</if>
			<if test="type != null" >
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="category != null" >
				category = #{category,jdbcType=INTEGER},
			</if>
			<if test="name != null" >
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="treatName != null" >
				treat_name = #{treatName,jdbcType=VARCHAR},
			</if>
			<if test="treatIdType != null" >
				treat_id_type = #{treatIdType,jdbcType=VARCHAR},
			</if>
			<if test="treatIdNum != null" >
				treat_id_num = #{treatIdNum,jdbcType=VARCHAR},
			</if>
			<if test="gender != null" >
				gender = #{gender,jdbcType=VARCHAR},
			</if>
			<if test="age != null" >
				age = #{age,jdbcType=INTEGER},
			</if>
			<if test="mobile != null" >
				mobile = #{mobile,jdbcType=VARCHAR},
			</if>
			<if test="accidentType != null" >
				accident_type = #{accidentType,jdbcType=VARCHAR},
			</if>
			<if test="accidentLiability != null" >
				accident_liability = #{accidentLiability,jdbcType=VARCHAR},
			</if>
			<if test="accidentProportion != null" >
				accident_proportion = #{accidentProportion,jdbcType=DECIMAL},
			</if>
			<if test="lossAssessmentSum != null" >
				loss_assessment_sum = #{lossAssessmentSum,jdbcType=DECIMAL},
			</if>
			<if test="nuclearLossSum != null" >
				nuclear_loss_sum = #{nuclearLossSum,jdbcType=DECIMAL},
			</if>
			<if test="injuredArea != null" >
				injured_area = #{injuredArea,jdbcType=VARCHAR},
			</if>
			<if test="injuredDesc != null" >
				injured_desc = #{injuredDesc,jdbcType=VARCHAR},
			</if>
			<if test="workAddress != null" >
				work_address = #{workAddress,jdbcType=VARCHAR},
			</if>
			<if test="hospitalName != null" >
				hospital_name = #{hospitalName,jdbcType=VARCHAR},
			</if>
			<if test="homeAddress != null" >
				home_address = #{homeAddress,jdbcType=VARCHAR},
			</if>
			<if test="carNumber != null" >
				car_number = #{carNumber,jdbcType=VARCHAR},
			</if>
			<if test="carModel != null" >
				car_model = #{carModel,jdbcType=VARCHAR},
			</if>
			<if test="carEncoding != null" >
				car_encoding = #{carEncoding,jdbcType=VARCHAR},
			</if>
			<if test="firstRegistrationTime != null" >
				first_registration_time = #{firstRegistrationTime,jdbcType=TIMESTAMP},
			</if>
			<if test="lossAssessmentTime != null" >
				loss_assessment_time = #{lossAssessmentTime,jdbcType=TIMESTAMP},
			</if>
			<if test="repairFactory != null" >
				repair_factory = #{repairFactory,jdbcType=VARCHAR},
			</if>
			<if test="isServiceShop != null" >
				is_service_shop = #{isServiceShop,jdbcType=VARCHAR},
			</if>
			<if test="residualValue != null" >
				residual_value = #{residualValue,jdbcType=DECIMAL},
			</if>
			<if test="residualNuclearLossValue != null" >
				residual_nuclear_loss_value = #{residualNuclearLossValue,jdbcType=DECIMAL},
			</if>
			<if test="estimatedOverallLoss != null" >
				estimated_overall_loss = #{estimatedOverallLoss,jdbcType=DECIMAL},
			</if>
			<if test="estimatedApprovedMoney != null" >
				estimated_approved_money = #{estimatedApprovedMoney,jdbcType=DECIMAL},
			</if>
			<if test="verifyAmout != null" >
				verify_amout = #{verifyAmout,jdbcType=DECIMAL},
			</if>
			<if test="verifyDetail != null" >
				verify_detail = #{verifyDetail,jdbcType=VARCHAR},
			</if>
			<if test="dutyRate != null" >
				duty_rate = #{dutyRate,jdbcType=DECIMAL},
			</if>
			<if test="deductFee != null" >
				deduct_fee = #{deductFee,jdbcType=DECIMAL},
			</if>
			<if test="label != null" >
				label = #{label,jdbcType=VARCHAR},
			</if>

			auditer = #{auditer,jdbcType=VARCHAR},

			<if test="checkAuditer != null" >
				check_auditer = #{checkAuditer,jdbcType=VARCHAR},
			</if>
			<if test="insAuditer != null" >
				ins_auditer = #{insAuditer,jdbcType=VARCHAR},
			</if>
			<if test="bankCard != null" >
				bank_card = #{bankCard,jdbcType=VARCHAR},
			</if>
			<if test="bankName != null" >
				bank_name = #{bankName,jdbcType=VARCHAR},
			</if>
			<if test="bankAccount != null" >
				bank_account = #{bankAccount,jdbcType=VARCHAR},
			</if>
			<if test="bankInfoId != null" >
				bank_info_id = #{bankInfoId,jdbcType=VARCHAR},
			</if>
			<if test="isCaseClosed != null" >
				is_case_closed = #{isCaseClosed,jdbcType=INTEGER},
			</if>
			<if test="auditerShareTime != null" >
				auditer_share_time = #{auditerShareTime,jdbcType=TIMESTAMP},
			</if>
			<if test="checkEndTime != null" >
				check_end_time = #{checkEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="reason != null" >
				reason = #{reason,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="creator != null" >
				creator = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyTime != null" >
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>

	<update id="updateByPrimaryKeySelectiveCheckAuditerCanNull" parameterType="kd.entity.ClaimCaseObject">
		update t_claim_case_object
		<set >
			<if test="insCode != null" >
				ins_code = #{insCode,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseId != null" >
				claim_case_id = #{claimCaseId,jdbcType=VARCHAR},
			</if>
			<if test="claimCaseNo != null" >
				claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
			</if>
			<if test="insClaimCaseNo != null" >
				ins_claim_case_no = #{insClaimCaseNo,jdbcType=VARCHAR},
			</if>
			<if test="policyNo != null" >
				policy_no = #{policyNo,jdbcType=VARCHAR},
			</if>
			<if test="type != null" >
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="category != null" >
				category = #{category,jdbcType=INTEGER},
			</if>
			<if test="name != null" >
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="treatName != null" >
				treat_name = #{treatName,jdbcType=VARCHAR},
			</if>
			<if test="treatIdType != null" >
				treat_id_type = #{treatIdType,jdbcType=VARCHAR},
			</if>
			<if test="treatIdNum != null" >
				treat_id_num = #{treatIdNum,jdbcType=VARCHAR},
			</if>
			<if test="gender != null" >
				gender = #{gender,jdbcType=VARCHAR},
			</if>
			<if test="age != null" >
				age = #{age,jdbcType=INTEGER},
			</if>
			<if test="mobile != null" >
				mobile = #{mobile,jdbcType=VARCHAR},
			</if>
			<if test="accidentType != null" >
				accident_type = #{accidentType,jdbcType=VARCHAR},
			</if>
			<if test="accidentLiability != null" >
				accident_liability = #{accidentLiability,jdbcType=VARCHAR},
			</if>
			<if test="accidentProportion != null" >
				accident_proportion = #{accidentProportion,jdbcType=DECIMAL},
			</if>
			<if test="lossAssessmentSum != null" >
				loss_assessment_sum = #{lossAssessmentSum,jdbcType=DECIMAL},
			</if>
			<if test="nuclearLossSum != null" >
				nuclear_loss_sum = #{nuclearLossSum,jdbcType=DECIMAL},
			</if>
			<if test="injuredArea != null" >
				injured_area = #{injuredArea,jdbcType=VARCHAR},
			</if>
			<if test="injuredDesc != null" >
				injured_desc = #{injuredDesc,jdbcType=VARCHAR},
			</if>
			<if test="workAddress != null" >
				work_address = #{workAddress,jdbcType=VARCHAR},
			</if>
			<if test="hospitalName != null" >
				hospital_name = #{hospitalName,jdbcType=VARCHAR},
			</if>
			<if test="homeAddress != null" >
				home_address = #{homeAddress,jdbcType=VARCHAR},
			</if>
			<if test="carNumber != null" >
				car_number = #{carNumber,jdbcType=VARCHAR},
			</if>
			<if test="carModel != null" >
				car_model = #{carModel,jdbcType=VARCHAR},
			</if>
			<if test="carEncoding != null" >
				car_encoding = #{carEncoding,jdbcType=VARCHAR},
			</if>
			<if test="firstRegistrationTime != null" >
				first_registration_time = #{firstRegistrationTime,jdbcType=TIMESTAMP},
			</if>
			<if test="lossAssessmentTime != null" >
				loss_assessment_time = #{lossAssessmentTime,jdbcType=TIMESTAMP},
			</if>
			<if test="repairFactory != null" >
				repair_factory = #{repairFactory,jdbcType=VARCHAR},
			</if>
			<if test="isServiceShop != null" >
				is_service_shop = #{isServiceShop,jdbcType=VARCHAR},
			</if>
			<if test="residualValue != null" >
				residual_value = #{residualValue,jdbcType=DECIMAL},
			</if>
			<if test="residualNuclearLossValue != null" >
				residual_nuclear_loss_value = #{residualNuclearLossValue,jdbcType=DECIMAL},
			</if>
			<if test="estimatedOverallLoss != null" >
				estimated_overall_loss = #{estimatedOverallLoss,jdbcType=DECIMAL},
			</if>
			<if test="estimatedApprovedMoney != null" >
				estimated_approved_money = #{estimatedApprovedMoney,jdbcType=DECIMAL},
			</if>
			<if test="verifyAmout != null" >
				verify_amout = #{verifyAmout,jdbcType=DECIMAL},
			</if>
			<if test="verifyDetail != null" >
				verify_detail = #{verifyDetail,jdbcType=VARCHAR},
			</if>
			<if test="dutyRate != null" >
				duty_rate = #{dutyRate,jdbcType=DECIMAL},
			</if>
			<if test="deductFee != null" >
				deduct_fee = #{deductFee,jdbcType=DECIMAL},
			</if>
			<if test="label != null" >
				label = #{label,jdbcType=VARCHAR},
			</if>
			<if test="auditer != null" >
				auditer = #{auditer,jdbcType=VARCHAR},
			</if>

			check_auditer = #{checkAuditer,jdbcType=VARCHAR},

			<if test="insAuditer != null" >
				ins_auditer = #{insAuditer,jdbcType=VARCHAR},
			</if>
			<if test="bankCard != null" >
				bank_card = #{bankCard,jdbcType=VARCHAR},
			</if>
			<if test="bankName != null" >
				bank_name = #{bankName,jdbcType=VARCHAR},
			</if>
			<if test="bankAccount != null" >
				bank_account = #{bankAccount,jdbcType=VARCHAR},
			</if>
			<if test="bankInfoId != null" >
				bank_info_id = #{bankInfoId,jdbcType=VARCHAR},
			</if>
			<if test="isCaseClosed != null" >
				is_case_closed = #{isCaseClosed,jdbcType=INTEGER},
			</if>
			<if test="auditerShareTime != null" >
				auditer_share_time = #{auditerShareTime,jdbcType=TIMESTAMP},
			</if>
			<if test="checkEndTime != null" >
				check_end_time = #{checkEndTime,jdbcType=TIMESTAMP},
			</if>
			<if test="reason != null" >
				reason = #{reason,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="creator != null" >
				creator = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>


	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ClaimCaseObjectMapper.deleteByPrimaryKey" />
	</delete>

	<update id="closeObjectByClaimCaseId" parameterType="java.lang.String">
		update t_claim_case_object
		set is_case_closed = 1
		where claim_case_id = #{claimCaseId,jdbcType=VARCHAR}
	</update>


	<update id="clearCheckAudit" parameterType="java.lang.String">
		update t_claim_case_object
		<set >
			<if test="checkAuditer != null" >
				check_auditer = null,
			</if>
			<if test="insAuditer != null" >
				ins_auditer = null,
			</if>
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>


</mapper>