<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.ConfigDao">
	
	<select id="selectByPrimaryKey" resultMap="mapping.ConfigMapper.BaseResultMap"
		parameterType="java.lang.String">
		<include refid="mapping.ConfigMapper.selectByPrimaryKey" />
	</select>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ConfigMapper.deleteByPrimaryKey" />
	</delete>
	
	<insert id="insertSelective" parameterType="kd.entity.Config">
		<include refid="mapping.ConfigMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Config">
		<include refid="mapping.ConfigMapper.updateByPrimaryKeySelective" />
	</update>
	
	<select id="findAllList" resultMap="mapping.ConfigMapper.BaseResultMap"
		parameterType="java.util.Map">
		select
		<include refid="mapping.ConfigMapper.Base_Column_List" />
		from t_config a
		where 1=1
		<if test="ckey != null and ckey != ''">
			and a.ckey like CONCAT('%', #{ckey,jdbcType=VARCHAR} ,'%')
		</if>
		<if test="cvalue != null and cvalue != ''">
			and a.cvalue like CONCAT('%', #{cvalue,jdbcType=VARCHAR} ,'%')
		</if>
		order by ckey
	</select>
	
</mapper>