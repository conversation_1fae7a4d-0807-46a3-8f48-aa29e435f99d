<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.LossAdjustingDao">

  <insert id="insertSelective" parameterType="kd.entity.LossAdjusting">
    <include refid="mapping.LossAdjustingMapper.insertSelective" />
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="kd.entity.LossAdjusting">
    <include refid="mapping.LossAdjustingMapper.updateByPrimaryKeySelective" />
  </update>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <include refid="mapping.LossAdjustingMapper.deleteByPrimaryKey" />
  </delete>

</mapper>