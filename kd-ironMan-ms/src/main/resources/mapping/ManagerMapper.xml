<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.ManagerDao">
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ManagerMapper.deleteByPrimaryKey" />
	</delete>
	
	<insert id="insertSelective" parameterType="kd.entity.Manager">
		<include refid="mapping.ManagerMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Manager">
		<include refid="mapping.ManagerMapper.updateByPrimaryKeySelective" />
	</update>

	<select id="selectByNameAndPassword" parameterType="java.lang.String" resultMap="mapping.ManagerMapper.BaseResultMap">
		select
		<include refid="mapping.ManagerMapper.Base_Column_List" />
		from t_manager a
		where a.user_name = #{userName,jdbcType=VARCHAR} and a.password = #{password,jdbcType=VARCHAR}
	</select>
	
	<select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="mapping.ManagerMapper.BaseResultMap">
		<include refid="mapping.ManagerMapper.selectByPrimaryKey"></include>
	</select>

	<select id="findAllList" parameterType="java.util.Map" resultMap="mapping.ManagerMapper.BaseResultMap">
		select
		<include refid="mapping.ManagerMapper.Base_Column_List" />
		from t_manager a
		where 1=1
		<if test="userName != null and userName !=''"  >
			 AND a.user_name like concat('%', #{userName,jdbcType=VARCHAR}, '%')
		</if>
		order by a.create_time desc
		limit #{pageNum,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
	</select>

	<select id="findAllCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		select
		count(1)
		from t_manager a
		where 1 = 1
		<if test="userName != null and userName !=''">
			AND a.user_name like concat('%', #{userName,jdbcType=VARCHAR}, '%')
		</if>
	</select>

	<select id="findByManagerIdList" parameterType="java.util.List" resultMap="mapping.ManagerMapper.BaseResultMap">
		select
		<include refid="mapping.ManagerMapper.Base_Column_List" />
		from t_manager a
		where a.id in (
		<foreach collection="list" item="id" separator=",">
			#{id,jdbcType=VARCHAR}
		</foreach>
		)
		order by a.create_time desc
	</select>

	<select id="existManagerUserName" parameterType="java.lang.String" resultType="java.lang.Integer">
		select
		count(1)
		from t_manager a
		where user_name = #{userName, jdbcType=VARCHAR}
		<if test="id != null and id !=''">
			AND a.id !=  #{id, jdbcType=VARCHAR}
		</if>
	</select>

	<select id="selectByRealName" parameterType="java.lang.String" resultMap="mapping.ManagerMapper.BaseResultMap">
		select
		<include refid="mapping.ManagerMapper.Base_Column_List" />
		from t_manager a
		where a.real_name = #{realName,jdbcType=VARCHAR} and a.status = 1
		limit 1
	</select>


	<select id="selectByUserName" parameterType="java.lang.String" resultMap="mapping.ManagerMapper.BaseResultMap">
		select
		<include refid="mapping.ManagerMapper.Base_Column_List" />
		from t_manager a
		where a.user_name = #{userName,jdbcType=VARCHAR} and a.status = 1
		limit 1
	</select>

</mapper>