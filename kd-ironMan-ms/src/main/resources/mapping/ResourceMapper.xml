<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kd.ironMan.dao.ResourceDao">
	
	<resultMap id="ResourceGrantMap" type="kd.ironMan.vo.ResourceGrant" extends="mapping.ResourceMapper.BaseResultMap">
		<result column="selected" property="selected" jdbcType="INTEGER" />
	</resultMap>
	
	<select id="selectByPrimaryKey" resultMap="mapping.ResourceMapper.BaseResultMap"
		parameterType="java.lang.String">
		<include refid="mapping.ResourceMapper.selectByPrimaryKey" />
	</select>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		<include refid="mapping.ResourceMapper.deleteByPrimaryKey" />
	</delete>
	
	<insert id="insertSelective" parameterType="kd.entity.Resource">
		<include refid="mapping.ResourceMapper.insertSelective" />
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="kd.entity.Resource">
		<include refid="mapping.ResourceMapper.updateByPrimaryKeySelective" />
	</update>
	
	<select id="findAllList" resultMap="mapping.ResourceMapper.BaseResultMap"
		parameterType="java.util.Map">
		select
			<include refid="mapping.ResourceMapper.Base_Column_List" />
		from t_resource a
		where 1=1
			<if test="typeEnd != null">
				<![CDATA[ and a.type <= #{typeEnd} ]]>
			</if>
			<if test="platform != null">
				<![CDATA[ and a.platform = #{platform} ]]>
			</if>
		order by IFNULL(a.parent_id, ''), a.idx
	</select>
	
	<select id="findResourceChildId" resultType="java.lang.String"
		parameterType="java.lang.String">
		select getResourceChildList(#{id})
	</select>

	<select id="findManagerResourceGrantAllList" resultMap="ResourceGrantMap"
		parameterType="map">
		SELECT
			<include refid="mapping.ResourceMapper.Base_Column_List" />,
			(
				SELECT
					count(1)
				FROM
					tr_role_resource
				WHERE
					resource_id = a.id
				AND role_id = #{roleId}
			) AS selected
		FROM
			t_resource a
		WHERE a.platform = #{platform}
		order by IFNULL(a.parent_id, ''), a.idx
	</select>
	
	<select id="findAuthResourceListByManagerId" resultMap="mapping.ResourceMapper.BaseResultMap"
		parameterType="java.lang.String">
		select
			<include refid="mapping.ResourceMapper.Base_Column_List" />
		from t_resource a
		left join tr_role_resource rr on a.id = rr.resource_id
		left join t_role r on rr.role_id = r.id
		left join tr_manager_role mr on r.id = mr.role_id
		left join t_manager m on mr.manager_id = m.id
		where m.id = #{managerId}
			and a.platform = 0
		order by a.type, IFNULL(a.parent_id, ''), a.idx
	</select>
	
	<select id="existResourceName" resultType="java.lang.Integer"
		parameterType="map">
		select
		count(1)
		from t_resource a
		where a.name = #{name}
		<if test="id != null">
			and a.id != #{id}
		</if>
		and a.platform = #{platform}
	</select>

	<select id="findAllByParentId" resultMap="mapping.ResourceMapper.BaseResultMap" parameterType="java.util.List">
		select
		<include refid="mapping.ResourceMapper.Base_Column_List" />
		from t_resource a
		where a.parent_id in (
			<foreach collection="parentIdList" item="parentId" separator=",">
				#{parentId,jdbcType=VARCHAR}
			</foreach>
		)
	</select>
	
</mapper>