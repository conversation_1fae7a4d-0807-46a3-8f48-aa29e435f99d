/*! X-editable - v1.4.6 
* In-place editing with Twitter Bootstrap, jQuery UI or pure jQuery
* http://github.com/vitalets/x-editable
* Copyright (c) 2013 <PERSON><PERSON><PERSON>tapov; Licensed MIT 
* Improved by Keenthemes for Bootstrap 3.0 support */
/**
Form with single input element, two buttons and two states: normal/loading.
Applied as jQuery method to DIV tag (not to form tag!). This is because form can be in loading state when spinner shown.
Editableform is linked with one of input types, e.g. 'text', 'select' etc.

@class editableform
@uses text
@uses textarea
**/
!function(t){"use strict";var e=function(e,i){this.options=t.extend({},t.fn.editableform.defaults,i),this.$div=t(e),this.options.scope||(this.options.scope=this)};e.prototype={constructor:e,initInput:function(){this.input=this.options.input,this.value=this.input.str2value(this.options.value)},initTemplate:function(){this.$form=t(t.fn.editableform.template)},initButtons:function(){var e=this.$form.find(".editable-buttons");e.append(t.fn.editableform.buttons),"bottom"===this.options.showbuttons&&e.addClass("editable-buttons-bottom")},render:function(){this.$loading=t(t.fn.editableform.loading),this.$div.empty().append(this.$loading),this.initTemplate(),this.options.showbuttons?this.initButtons():this.$form.find(".editable-buttons").remove(),this.showLoading(),this.isSaving=!1,this.$div.triggerHandler("rendering"),this.initInput(),this.input.prerender(),this.$form.find("div.editable-input").append(this.input.$tpl),this.$div.append(this.$form),t.when(this.input.render()).then(t.proxy(function(){if(this.options.showbuttons||this.input.autosubmit(),this.$form.find(".editable-cancel").click(t.proxy(this.cancel,this)),this.input.error)this.error(this.input.error),this.$form.find(".editable-submit").attr("disabled",!0),this.input.$input.attr("disabled",!0),this.$form.submit(function(t){t.preventDefault()});else{this.error(!1),this.input.$input.removeAttr("disabled"),this.$form.find(".editable-submit").removeAttr("disabled");var e=null===this.value||void 0===this.value||""===this.value?this.options.defaultValue:this.value;this.input.value2input(e),this.$form.submit(t.proxy(this.submit,this))}this.$div.triggerHandler("rendered"),this.showForm(),this.input.postrender&&this.input.postrender()},this))},cancel:function(){this.$div.triggerHandler("cancel")},showLoading:function(){var t,e;this.$form?(t=this.$form.outerWidth(),e=this.$form.outerHeight(),t&&this.$loading.width(t),e&&this.$loading.height(e),this.$form.hide()):(t=this.$loading.parent().width(),t&&this.$loading.width(t)),this.$loading.show()},showForm:function(t){this.$loading.hide(),this.$form.show(),t!==!1&&this.input.activate(),this.$div.triggerHandler("show")},error:function(e){var i,n=this.$form.find(".form-group"),o=this.$form.find(".editable-error-block");if(e===!1)n.removeClass(t.fn.editableform.errorGroupClass),o.removeClass(t.fn.editableform.errorBlockClass).empty().hide();else{if(e){i=e.split("\n");for(var r=0;r<i.length;r++)i[r]=t("<div>").text(i[r]).html();e=i.join("<br>")}n.addClass(t.fn.editableform.errorGroupClass),o.addClass(t.fn.editableform.errorBlockClass).html(e).show()}},submit:function(e){e.stopPropagation(),e.preventDefault();var i,n=this.input.input2value();if(i=this.validate(n))return this.error(i),this.showForm(),void 0;if(!this.options.savenochange&&this.input.value2str(n)==this.input.value2str(this.value))return this.$div.triggerHandler("nochange"),void 0;var o=this.input.value2submit(n);this.isSaving=!0,t.when(this.save(o)).done(t.proxy(function(t){this.isSaving=!1;var e="function"==typeof this.options.success?this.options.success.call(this.options.scope,t,n):null;return e===!1?(this.error(!1),this.showForm(!1),void 0):"string"==typeof e?(this.error(e),this.showForm(),void 0):(e&&"object"==typeof e&&e.hasOwnProperty("newValue")&&(n=e.newValue),this.error(!1),this.value=n,this.$div.triggerHandler("save",{newValue:n,submitValue:o,response:t}),void 0)},this)).fail(t.proxy(function(t){this.isSaving=!1;var e;e="function"==typeof this.options.error?this.options.error.call(this.options.scope,t,n):"string"==typeof t?t:t.responseText||t.statusText||"Unknown error!",this.error(e),this.showForm()},this))},save:function(e){this.options.pk=t.fn.editableutils.tryParseJson(this.options.pk,!0);var i,n="function"==typeof this.options.pk?this.options.pk.call(this.options.scope):this.options.pk,o=!!("function"==typeof this.options.url||this.options.url&&("always"===this.options.send||"auto"===this.options.send&&null!==n&&void 0!==n));return o?(this.showLoading(),i={name:this.options.name||"",value:e,pk:n},"function"==typeof this.options.params?i=this.options.params.call(this.options.scope,i):(this.options.params=t.fn.editableutils.tryParseJson(this.options.params,!0),t.extend(i,this.options.params)),"function"==typeof this.options.url?this.options.url.call(this.options.scope,i):t.ajax(t.extend({url:this.options.url,data:i,type:"POST"},this.options.ajaxOptions))):void 0},validate:function(t){return void 0===t&&(t=this.value),"function"==typeof this.options.validate?this.options.validate.call(this.options.scope,t):void 0},option:function(t,e){t in this.options&&(this.options[t]=e),"value"===t&&this.setValue(e)},setValue:function(t,e){this.value=e?this.input.str2value(t):t,this.$form&&this.$form.is(":visible")&&this.input.value2input(this.value)}},t.fn.editableform=function(i){var n=arguments;return this.each(function(){var o=t(this),r=o.data("editableform"),s="object"==typeof i&&i;r||o.data("editableform",r=new e(this,s)),"string"==typeof i&&r[i].apply(r,Array.prototype.slice.call(n,1))})},t.fn.editableform.Constructor=e,t.fn.editableform.defaults={type:"text",url:null,params:null,name:null,pk:null,value:null,defaultValue:null,send:"auto",validate:null,success:null,error:null,ajaxOptions:null,showbuttons:!0,scope:null,savenochange:!1},t.fn.editableform.template='<form class="form-inline editableform"><div class="form-group"><div><div class="editable-input"></div><div class="editable-buttons"></div></div><div class="editable-error-block"></div></div></form>',t.fn.editableform.loading='<div class="editableform-loading"></div>',t.fn.editableform.buttons='<button type="submit" class="editable-submit">ok</button><button type="button" class="editable-cancel">cancel</button>',t.fn.editableform.errorGroupClass="has-error",t.fn.editableform.errorBlockClass="editable-error"}(window.jQuery),function(t){"use strict";t.fn.editableutils={inherit:function(t,e){var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype},setCursorPosition:function(t,e){if(t.setSelectionRange)t.setSelectionRange(e,e);else if(t.createTextRange){var i=t.createTextRange();i.collapse(!0),i.moveEnd("character",e),i.moveStart("character",e),i.select()}},tryParseJson:function(t,e){if("string"==typeof t&&t.length&&t.match(/^[\{\[].*[\}\]]$/))if(e)try{t=new Function("return "+t)()}catch(i){}finally{return t}else t=new Function("return "+t)();return t},sliceObj:function(e,i,n){var o,r,s={};if(!t.isArray(i)||!i.length)return s;for(var a=0;a<i.length;a++)o=i[a],e.hasOwnProperty(o)&&(s[o]=e[o]),n!==!0&&(r=o.toLowerCase(),e.hasOwnProperty(r)&&(s[o]=e[r]));return s},getConfigData:function(e){var i={};return t.each(e.data(),function(t,e){("object"!=typeof e||e&&"object"==typeof e&&(e.constructor===Object||e.constructor===Array))&&(i[t]=e)}),i},objectKeys:function(t){if(Object.keys)return Object.keys(t);if(t!==Object(t))throw new TypeError("Object.keys called on a non-object");var e,i=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&i.push(e);return i},escape:function(e){return t("<div>").text(e).html()},itemsByValue:function(e,i,n){if(!i||null===e)return[];if("function"!=typeof n){var o=n||"value";n=function(t){return t[o]}}var r=t.isArray(e),s=[],a=this;return t.each(i,function(i,o){if(o.children)s=s.concat(a.itemsByValue(e,o.children,n));else if(r)t.grep(e,function(t){return t==(o&&"object"==typeof o?n(o):o)}).length&&s.push(o);else{var l=o&&"object"==typeof o?n(o):o;e==l&&s.push(o)}}),s},createInput:function(e){var i,n,o,r=e.type;return"date"===r&&("inline"===e.mode?t.fn.editabletypes.datefield?r="datefield":t.fn.editabletypes.dateuifield&&(r="dateuifield"):t.fn.editabletypes.date?r="date":t.fn.editabletypes.dateui&&(r="dateui"),"date"!==r||t.fn.editabletypes.date||(r="combodate")),"datetime"===r&&"inline"===e.mode&&(r="datetimefield"),"wysihtml5"!==r||t.fn.editabletypes[r]||(r="textarea"),"function"==typeof t.fn.editabletypes[r]?(i=t.fn.editabletypes[r],n=this.sliceObj(e,this.objectKeys(i.defaults)),o=new i(n)):(t.error("Unknown type: "+r),!1)},supportsTransitions:function(){var t=document.body||document.documentElement,e=t.style,i="transition",n=["Moz","Webkit","Khtml","O","ms"];if("string"==typeof e[i])return!0;i=i.charAt(0).toUpperCase()+i.substr(1);for(var o=0;o<n.length;o++)if("string"==typeof e[n[o]+i])return!0;return!1}}}(window.jQuery),function(t){"use strict";var e=function(t,e){this.init(t,e)},i=function(t,e){this.init(t,e)};e.prototype={containerName:null,containerDataName:null,innerCss:null,containerClass:"editable-container editable-popup",init:function(i,n){this.$element=t(i),this.options=t.extend({},t.fn.editableContainer.defaults,n),this.splitOptions(),this.formOptions.scope=this.$element[0],this.initContainer(),this.delayedHide=!1,this.$element.on("destroyed",t.proxy(function(){this.destroy()},this)),t(document).data("editable-handlers-attached")||(t(document).on("keyup.editable",function(e){27===e.which&&t(".editable-open").editableContainer("hide")}),t(document).on("click.editable",function(i){var n,o=t(i.target),r=[".editable-container",".ui-datepicker-header",".datepicker",".modal-backdrop",".bootstrap-wysihtml5-insert-image-modal",".bootstrap-wysihtml5-insert-link-modal"];if(t.contains(document.documentElement,i.target)&&!o.is(document)){for(n=0;n<r.length;n++)if(o.is(r[n])||o.parents(r[n]).length)return;e.prototype.closeOthers(i.target)}}),t(document).data("editable-handlers-attached",!0))},splitOptions:function(){if(this.containerOptions={},this.formOptions={},!t.fn[this.containerName])throw new Error(this.containerName+" not found. Have you included corresponding js file?");var e=t.fn[this.containerName].defaults;for(var i in this.options)i in e?this.containerOptions[i]=this.options[i]:this.formOptions[i]=this.options[i]},tip:function(){return this.container()?this.container().$tip:null},container:function(){var t;return this.containerDataName&&(t=this.$element.data(this.containerDataName))?t:t=this.$element.data(this.containerName)},call:function(){this.$element[this.containerName].apply(this.$element,arguments)},initContainer:function(){this.call(this.containerOptions)},renderForm:function(){this.$form.editableform(this.formOptions).on({save:t.proxy(this.save,this),nochange:t.proxy(function(){this.hide("nochange")},this),cancel:t.proxy(function(){this.hide("cancel")},this),show:t.proxy(function(){this.delayedHide?(this.hide(this.delayedHide.reason),this.delayedHide=!1):this.setPosition()},this),rendering:t.proxy(this.setPosition,this),resize:t.proxy(this.setPosition,this),rendered:t.proxy(function(){this.$element.triggerHandler("shown",t(this.options.scope).data("editable"))},this)}).editableform("render")},show:function(e){this.$element.addClass("editable-open"),e!==!1&&this.closeOthers(this.$element[0]),this.innerShow(),this.tip().addClass(this.containerClass),this.$form,this.$form=t("<div>"),this.tip().is(this.innerCss)?this.tip().append(this.$form):this.tip().find(this.innerCss).append(this.$form),this.renderForm()},hide:function(t){if(this.tip()&&this.tip().is(":visible")&&this.$element.hasClass("editable-open")){if(this.$form.data("editableform").isSaving)return this.delayedHide={reason:t},void 0;this.delayedHide=!1,this.$element.removeClass("editable-open"),this.innerHide(),this.$element.triggerHandler("hidden",t||"manual")}},innerShow:function(){},innerHide:function(){},toggle:function(t){this.container()&&this.tip()&&this.tip().is(":visible")?this.hide():this.show(t)},setPosition:function(){},save:function(t,e){this.$element.triggerHandler("save",e),this.hide("save")},option:function(t,e){this.options[t]=e,t in this.containerOptions?(this.containerOptions[t]=e,this.setContainerOption(t,e)):(this.formOptions[t]=e,this.$form&&this.$form.editableform("option",t,e))},setContainerOption:function(t,e){this.call("option",t,e)},destroy:function(){this.hide(),this.innerDestroy(),this.$element.off("destroyed"),this.$element.removeData("editableContainer")},innerDestroy:function(){},closeOthers:function(e){t(".editable-open").each(function(i,n){if(n!==e&&!t(n).find(e).length){var o=t(n),r=o.data("editableContainer");r&&("cancel"===r.options.onblur?o.data("editableContainer").hide("onblur"):"submit"===r.options.onblur&&o.data("editableContainer").tip().find("form").submit())}})},activate:function(){this.tip&&this.tip().is(":visible")&&this.$form&&this.$form.data("editableform").input.activate()}},t.fn.editableContainer=function(n){var o=arguments;return this.each(function(){var r=t(this),s="editableContainer",a=r.data(s),l="object"==typeof n&&n,c="inline"===l.mode?i:e;a||r.data(s,a=new c(this,l)),"string"==typeof n&&a[n].apply(a,Array.prototype.slice.call(o,1))})},t.fn.editableContainer.Popup=e,t.fn.editableContainer.Inline=i,t.fn.editableContainer.defaults={value:null,placement:"top",autohide:!0,onblur:"cancel",anim:!1,mode:"popup"},jQuery.event.special.destroyed={remove:function(t){t.handler&&t.handler()}}}(window.jQuery),function(t){"use strict";t.extend(t.fn.editableContainer.Inline.prototype,t.fn.editableContainer.Popup.prototype,{containerName:"editableform",innerCss:".editable-inline",containerClass:"editable-container editable-inline",initContainer:function(){this.$tip=t("<span></span>"),this.options.anim||(this.options.anim=0)},splitOptions:function(){this.containerOptions={},this.formOptions=this.options},tip:function(){return this.$tip},innerShow:function(){this.$element.hide(),this.tip().insertAfter(this.$element).show()},innerHide:function(){this.$tip.hide(this.options.anim,t.proxy(function(){this.$element.show(),this.innerDestroy()},this))},innerDestroy:function(){this.tip()&&this.tip().empty().remove()}})}(window.jQuery),function(t){"use strict";var e=function(e,i){this.$element=t(e),this.options=t.extend({},t.fn.editable.defaults,i,t.fn.editableutils.getConfigData(this.$element)),this.options.selector?this.initLive():this.init(),this.options.highlight&&!t.fn.editableutils.supportsTransitions()&&(this.options.highlight=!1)};e.prototype={constructor:e,init:function(){var e,i=!1;if(this.options.name=this.options.name||this.$element.attr("id"),this.options.scope=this.$element[0],this.input=t.fn.editableutils.createInput(this.options),this.input){switch(void 0===this.options.value||null===this.options.value?(this.value=this.input.html2value(t.trim(this.$element.html())),i=!0):(this.options.value=t.fn.editableutils.tryParseJson(this.options.value,!0),this.value="string"==typeof this.options.value?this.input.str2value(this.options.value):this.options.value),this.$element.addClass("editable"),"textarea"===this.input.type&&this.$element.addClass("editable-pre-wrapped"),"manual"!==this.options.toggle?(this.$element.addClass("editable-click"),this.$element.on(this.options.toggle+".editable",t.proxy(function(t){if(this.options.disabled||t.preventDefault(),"mouseenter"===this.options.toggle)this.show();else{var e="click"!==this.options.toggle;this.toggle(e)}},this))):this.$element.attr("tabindex",-1),"function"==typeof this.options.display&&(this.options.autotext="always"),this.options.autotext){case"always":e=!0;break;case"auto":e=!t.trim(this.$element.text()).length&&null!==this.value&&void 0!==this.value&&!i;break;default:e=!1}t.when(e?this.render():!0).then(t.proxy(function(){this.options.disabled?this.disable():this.enable(),this.$element.triggerHandler("init",this)},this))}},initLive:function(){var e=this.options.selector;this.options.selector=!1,this.options.autotext="never",this.$element.on(this.options.toggle+".editable",e,t.proxy(function(e){var i=t(e.target);i.data("editable")||(i.hasClass(this.options.emptyclass)&&i.empty(),i.editable(this.options).trigger(e))},this))},render:function(t){return this.options.display!==!1?this.input.value2htmlFinal?this.input.value2html(this.value,this.$element[0],this.options.display,t):"function"==typeof this.options.display?this.options.display.call(this.$element[0],this.value,t):this.input.value2html(this.value,this.$element[0]):void 0},enable:function(){this.options.disabled=!1,this.$element.removeClass("editable-disabled"),this.handleEmpty(this.isEmpty),"manual"!==this.options.toggle&&"-1"===this.$element.attr("tabindex")&&this.$element.removeAttr("tabindex")},disable:function(){this.options.disabled=!0,this.hide(),this.$element.addClass("editable-disabled"),this.handleEmpty(this.isEmpty),this.$element.attr("tabindex",-1)},toggleDisabled:function(){this.options.disabled?this.enable():this.disable()},option:function(e,i){return e&&"object"==typeof e?(t.each(e,t.proxy(function(e,i){this.option(t.trim(e),i)},this)),void 0):(this.options[e]=i,"disabled"===e?i?this.disable():this.enable():("value"===e&&this.setValue(i),this.container&&this.container.option(e,i),this.input.option&&this.input.option(e,i),void 0))},handleEmpty:function(e){this.options.display!==!1&&(this.isEmpty=void 0!==e?e:""===t.trim(this.$element.html())?!0:""!==t.trim(this.$element.text())?!1:!this.$element.height()||!this.$element.width(),this.options.disabled?this.isEmpty&&(this.$element.empty(),this.options.emptyclass&&this.$element.removeClass(this.options.emptyclass)):this.isEmpty?(this.$element.html(this.options.emptytext),this.options.emptyclass&&this.$element.addClass(this.options.emptyclass)):this.options.emptyclass&&this.$element.removeClass(this.options.emptyclass))},show:function(e){if(!this.options.disabled){if(this.container){if(this.container.tip().is(":visible"))return}else{var i=t.extend({},this.options,{value:this.value,input:this.input});this.$element.editableContainer(i),this.$element.on("save.internal",t.proxy(this.save,this)),this.container=this.$element.data("editableContainer")}this.container.show(e)}},hide:function(){this.container&&this.container.hide()},toggle:function(t){this.container&&this.container.tip().is(":visible")?this.hide():this.show(t)},save:function(t,e){if(this.options.unsavedclass){var i=!1;i=i||"function"==typeof this.options.url,i=i||this.options.display===!1,i=i||void 0!==e.response,i=i||this.options.savenochange&&this.input.value2str(this.value)!==this.input.value2str(e.newValue),i?this.$element.removeClass(this.options.unsavedclass):this.$element.addClass(this.options.unsavedclass)}if(this.options.highlight){var n=this.$element,o=n.css("background-color");n.css("background-color",this.options.highlight),setTimeout(function(){"transparent"===o&&(o=""),n.css("background-color",o),n.addClass("editable-bg-transition"),setTimeout(function(){n.removeClass("editable-bg-transition")},1700)},10)}this.setValue(e.newValue,!1,e.response)},validate:function(){return"function"==typeof this.options.validate?this.options.validate.call(this,this.value):void 0},setValue:function(e,i,n){this.value=i?this.input.str2value(e):e,this.container&&this.container.option("value",this.value),t.when(this.render(n)).then(t.proxy(function(){this.handleEmpty()},this))},activate:function(){this.container&&this.container.activate()},destroy:function(){this.disable(),this.container&&this.container.destroy(),this.input.destroy(),"manual"!==this.options.toggle&&(this.$element.removeClass("editable-click"),this.$element.off(this.options.toggle+".editable")),this.$element.off("save.internal"),this.$element.removeClass("editable editable-open editable-disabled"),this.$element.removeData("editable")}},t.fn.editable=function(i){var n={},o=arguments,r="editable";switch(i){case"validate":return this.each(function(){var e,i=t(this),o=i.data(r);o&&(e=o.validate())&&(n[o.options.name]=e)}),n;case"getValue":return 2===arguments.length&&arguments[1]===!0?n=this.eq(0).data(r).value:this.each(function(){var e=t(this),i=e.data(r);i&&void 0!==i.value&&null!==i.value&&(n[i.options.name]=i.input.value2submit(i.value))}),n;case"submit":var s,a=arguments[1]||{},l=this,c=this.editable("validate");return t.isEmptyObject(c)?(s=this.editable("getValue"),a.data&&t.extend(s,a.data),t.ajax(t.extend({url:a.url,data:s,type:"POST"},a.ajaxOptions)).success(function(t){"function"==typeof a.success&&a.success.call(l,t,a)}).error(function(){"function"==typeof a.error&&a.error.apply(l,arguments)})):"function"==typeof a.error&&a.error.call(l,c),this}return this.each(function(){var n=t(this),s=n.data(r),a="object"==typeof i&&i;return a&&a.selector?(s=new e(this,a),void 0):(s||n.data(r,s=new e(this,a)),"string"==typeof i&&s[i].apply(s,Array.prototype.slice.call(o,1)),void 0)})},t.fn.editable.defaults={type:"text",disabled:!1,toggle:"click",emptytext:"Empty",autotext:"auto",value:null,display:null,emptyclass:"editable-empty",unsavedclass:"editable-unsaved",selector:null,highlight:"#FFFF80"}}(window.jQuery),function(t){"use strict";t.fn.editabletypes={};var e=function(){};e.prototype={init:function(e,i,n){this.type=e,this.options=t.extend({},n,i)},prerender:function(){this.$tpl=t(this.options.tpl),this.$input=this.$tpl,this.$clear=null,this.error=null},render:function(){},value2html:function(e,i){t(i).text(t.trim(e))},html2value:function(e){return t("<div>").html(e).text()},value2str:function(t){return t},str2value:function(t){return t},value2submit:function(t){return t},value2input:function(t){this.$input.val(t)},input2value:function(){return this.$input.val()},activate:function(){this.$input.is(":visible")&&this.$input.focus()},clear:function(){this.$input.val(null)},escape:function(e){return t("<div>").text(e).html()},autosubmit:function(){},destroy:function(){},setClass:function(){this.options.inputclass&&this.$input.addClass(this.options.inputclass)},setAttr:function(t){void 0!==this.options[t]&&null!==this.options[t]&&this.$input.attr(t,this.options[t])},option:function(t,e){this.options[t]=e}},e.defaults={tpl:"",inputclass:"input-medium",scope:null,showbuttons:!0},t.extend(t.fn.editabletypes,{abstractinput:e})}(window.jQuery),function(t){"use strict";var e=function(){};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){var e=t.Deferred();return this.error=null,this.onSourceReady(function(){this.renderList(),e.resolve()},function(){this.error=this.options.sourceError,e.resolve()}),e.promise()},html2value:function(){return null},value2html:function(e,i,n,o){var r=t.Deferred(),s=function(){"function"==typeof n?n.call(i,e,this.sourceData,o):this.value2htmlFinal(e,i),r.resolve()};return null===e?s.call(this):this.onSourceReady(s,function(){r.resolve()}),r.promise()},onSourceReady:function(e,i){var n;if(t.isFunction(this.options.source)?(n=this.options.source.call(this.options.scope),this.sourceData=null):n=this.options.source,this.options.sourceCache&&t.isArray(this.sourceData))return e.call(this),void 0;try{n=t.fn.editableutils.tryParseJson(n,!1)}catch(o){return i.call(this),void 0}if("string"==typeof n){if(this.options.sourceCache){var r,s=n;if(t(document).data(s)||t(document).data(s,{}),r=t(document).data(s),r.loading===!1&&r.sourceData)return this.sourceData=r.sourceData,this.doPrepend(),e.call(this),void 0;if(r.loading===!0)return r.callbacks.push(t.proxy(function(){this.sourceData=r.sourceData,this.doPrepend(),e.call(this)},this)),r.err_callbacks.push(t.proxy(i,this)),void 0;r.loading=!0,r.callbacks=[],r.err_callbacks=[]}t.ajax({url:n,type:"get",cache:!1,dataType:"json",success:t.proxy(function(n){r&&(r.loading=!1),this.sourceData=this.makeArray(n),t.isArray(this.sourceData)?(r&&(r.sourceData=this.sourceData,t.each(r.callbacks,function(){this.call()})),this.doPrepend(),e.call(this)):(i.call(this),r&&t.each(r.err_callbacks,function(){this.call()}))},this),error:t.proxy(function(){i.call(this),r&&(r.loading=!1,t.each(r.err_callbacks,function(){this.call()}))},this)})}else this.sourceData=this.makeArray(n),t.isArray(this.sourceData)?(this.doPrepend(),e.call(this)):i.call(this)},doPrepend:function(){null!==this.options.prepend&&void 0!==this.options.prepend&&(t.isArray(this.prependData)||(t.isFunction(this.options.prepend)&&(this.options.prepend=this.options.prepend.call(this.options.scope)),this.options.prepend=t.fn.editableutils.tryParseJson(this.options.prepend,!0),"string"==typeof this.options.prepend&&(this.options.prepend={"":this.options.prepend}),this.prependData=this.makeArray(this.options.prepend)),t.isArray(this.prependData)&&t.isArray(this.sourceData)&&(this.sourceData=this.prependData.concat(this.sourceData)))},renderList:function(){},value2htmlFinal:function(){},makeArray:function(e){var i,n,o,r,s=[];if(!e||"string"==typeof e)return null;if(t.isArray(e)){r=function(t,e){return n={value:t,text:e},i++>=2?!1:void 0};for(var a=0;a<e.length;a++)o=e[a],"object"==typeof o?(i=0,t.each(o,r),1===i?s.push(n):i>1&&(o.children&&(o.children=this.makeArray(o.children)),s.push(o))):s.push({value:o,text:o})}else t.each(e,function(t,e){s.push({value:t,text:e})});return s},option:function(t,e){this.options[t]=e,"source"===t&&(this.sourceData=null),"prepend"===t&&(this.prependData=null)}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{source:null,prepend:!1,sourceError:"Error when loading list",sourceCache:!0}),t.fn.editabletypes.list=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("text",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.renderClear(),this.setClass(),this.setAttr("placeholder")},activate:function(){this.$input.is(":visible")&&(this.$input.focus(),t.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length),this.toggleClear&&this.toggleClear())},renderClear:function(){this.options.clear&&(this.$clear=t('<span class="editable-clear-x"></span>'),this.$input.after(this.$clear).css("padding-right",24).keyup(t.proxy(function(e){if(!~t.inArray(e.keyCode,[40,38,9,13,27])){clearTimeout(this.t);var i=this;this.t=setTimeout(function(){i.toggleClear(e)},100)}},this)).parent().css("position","relative"),this.$clear.click(t.proxy(this.clear,this)))},postrender:function(){},toggleClear:function(){if(this.$clear){var t=this.$input.val().length,e=this.$clear.is(":visible");t&&!e&&this.$clear.show(),!t&&e&&this.$clear.hide()}},clear:function(){this.$clear.hide(),this.$input.val("").focus()}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',placeholder:null,clear:!0}),t.fn.editabletypes.text=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("textarea",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass(),this.setAttr("placeholder"),this.setAttr("rows"),this.$input.keydown(function(e){e.ctrlKey&&13===e.which&&t(this).closest("form").submit()})},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:"<textarea></textarea>",inputclass:"input-large",placeholder:null,rows:7}),t.fn.editabletypes.textarea=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("select",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){this.$input.empty();var e=function(i,n){var o;if(t.isArray(n))for(var r=0;r<n.length;r++)o={},n[r].children?(o.label=n[r].text,i.append(e(t("<optgroup>",o),n[r].children))):(o.value=n[r].value,n[r].disabled&&(o.disabled=!0),i.append(t("<option>",o).text(n[r].text)));return i};e(this.$input,this.sourceData),this.setClass(),this.$input.on("keydown.editable",function(e){13===e.which&&t(this).closest("form").submit()})},value2htmlFinal:function(e,i){var n="",o=t.fn.editableutils.itemsByValue(e,this.sourceData);o.length&&(n=o[0].text),t(i).text(n)},autosubmit:function(){this.$input.off("keydown.editable").on("change.editable",function(){t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:'<select class="form-control"></select>'}),t.fn.editabletypes.select=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("checklist",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){var e;if(this.$tpl.empty(),t.isArray(this.sourceData)){for(var i=0;i<this.sourceData.length;i++)e=t("<label>").append(t("<input>",{type:"checkbox",value:this.sourceData[i].value})).append(t("<span>").text(" "+this.sourceData[i].text)),t("<div>").append(e).appendTo(this.$tpl);this.$input=this.$tpl.find('input[type="checkbox"]'),this.setClass()}},value2str:function(e){return t.isArray(e)?e.sort().join(t.trim(this.options.separator)):""},str2value:function(e){var i,n=null;return"string"==typeof e&&e.length?(i=new RegExp("\\s*"+t.trim(this.options.separator)+"\\s*"),n=e.split(i)):n=t.isArray(e)?e:[e],n},value2input:function(e){this.$input.prop("checked",!1),t.isArray(e)&&e.length&&this.$input.each(function(i,n){var o=t(n);t.each(e,function(t,e){o.val()==e&&o.prop("checked",!0)})})},input2value:function(){var e=[];return this.$input.filter(":checked").each(function(i,n){e.push(t(n).val())}),e},value2htmlFinal:function(e,i){var n=[],o=t.fn.editableutils.itemsByValue(e,this.sourceData);o.length?(t.each(o,function(e,i){n.push(t.fn.editableutils.escape(i.text))}),t(i).html(n.join("<br>"))):t(i).empty()},activate:function(){this.$input.first().focus()},autosubmit:function(){this.$input.on("keydown",function(e){13===e.which&&t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:'<div class="editable-checklist"></div>',inputclass:null,separator:","}),t.fn.editabletypes.checklist=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("password",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),t.extend(e.prototype,{value2html:function(e,i){e?t(i).text("[hidden]"):t(i).empty()},html2value:function(){return null}}),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="password">'}),t.fn.editabletypes.password=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("email",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="email">'}),t.fn.editabletypes.email=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("url",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="url">'}),t.fn.editabletypes.url=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("tel",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="tel">'}),t.fn.editabletypes.tel=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("number",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),t.extend(e.prototype,{render:function(){e.superclass.render.call(this),this.setAttr("min"),this.setAttr("max"),this.setAttr("step")},postrender:function(){this.$clear&&this.$clear.css({right:24})}}),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="number">',inputclass:"form-control input-small",min:null,max:null,step:null}),t.fn.editabletypes.number=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("range",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.number),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.filter("input"),this.setClass(),this.setAttr("min"),this.setAttr("max"),this.setAttr("step"),this.$input.on("input",function(){t(this).siblings("output").text(t(this).val())})},activate:function(){this.$input.focus()}}),e.defaults=t.extend({},t.fn.editabletypes.number.defaults,{tpl:'<input type="range"><output style="width: 30px; display: inline-block"></output>',inputclass:"form-control input-medium"}),t.fn.editabletypes.range=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("time",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass()}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="time">'}),t.fn.editabletypes.time=e
}(window.jQuery),function(t){"use strict";var e=function(i){if(this.init("select2",i,e.defaults),i.select2=i.select2||{},this.sourceData=null,i.placeholder&&(i.select2.placeholder=i.placeholder),!i.select2.tags&&i.source){var n=i.source;t.isFunction(i.source)&&(n=i.source.call(i.scope)),"string"==typeof n?(i.select2.ajax=i.select2.ajax||{},i.select2.ajax.data||(i.select2.ajax.data=function(t){return{query:t}}),i.select2.ajax.results||(i.select2.ajax.results=function(t){return{results:t}}),i.select2.ajax.url=n):(this.sourceData=this.convertSource(n),i.select2.data=this.sourceData)}if(this.options.select2=t.extend({},e.defaults.select2,i.select2),this.isMultiple=this.options.select2.tags||this.options.select2.multiple,this.isRemote="ajax"in this.options.select2,this.idFunc=this.options.select2.id,"function"!=typeof this.idFunc){var o=this.idFunc||"id";this.idFunc=function(t){return t[o]}}this.formatSelection=this.options.select2.formatSelection,"function"!=typeof this.formatSelection&&(this.formatSelection=function(t){return t.text})};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass(),this.$input.select2(this.options.select2),this.isRemote&&this.$input.on("select2-loaded",t.proxy(function(t){this.sourceData=t.items.results},this)),this.isMultiple&&this.$input.on("change",function(){t(this).closest("form").parent().triggerHandler("resize")})},value2html:function(e,i){var n,o="",r=this;this.options.select2.tags?n=e:this.sourceData&&(n=t.fn.editableutils.itemsByValue(e,this.sourceData,this.idFunc)),t.isArray(n)?(o=[],t.each(n,function(t,e){o.push(e&&"object"==typeof e?r.formatSelection(e):e)})):n&&(o=r.formatSelection(n)),o=t.isArray(o)?o.join(this.options.viewseparator):o,t(i).text(o)},html2value:function(t){return this.options.select2.tags?this.str2value(t,this.options.viewseparator):null},value2input:function(e){if(this.$input.val(e).trigger("change",!0),this.isRemote&&!this.isMultiple&&!this.options.select2.initSelection){var i=this.options.select2.id,n=this.options.select2.formatSelection;if(!i&&!n){var o={id:e,text:t(this.options.scope).text()};this.$input.select2("data",o)}}},input2value:function(){return this.$input.select2("val")},str2value:function(e,i){if("string"!=typeof e||!this.isMultiple)return e;i=i||this.options.select2.separator||t.fn.select2.defaults.separator;var n,o,r;if(null===e||e.length<1)return null;for(n=e.split(i),o=0,r=n.length;r>o;o+=1)n[o]=t.trim(n[o]);return n},autosubmit:function(){this.$input.on("change",function(e,i){i||t(this).closest("form").submit()})},convertSource:function(e){if(t.isArray(e)&&e.length&&void 0!==e[0].value)for(var i=0;i<e.length;i++)void 0!==e[i].value&&(e[i].id=e[i].value,delete e[i].value);return e},destroy:function(){this.$input.data("select2")&&this.$input.select2("destroy")}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="hidden">',select2:null,placeholder:null,source:null,viewseparator:", "}),t.fn.editabletypes.select2=e}(window.jQuery),function(t){var e=function(e,i){return this.$element=t(e),this.$element.is("input")?(this.options=t.extend({},t.fn.combodate.defaults,i,this.$element.data()),this.init(),void 0):(t.error("Combodate should be applied to INPUT element"),void 0)};e.prototype={constructor:e,init:function(){this.map={day:["D","date"],month:["M","month"],year:["Y","year"],hour:["[Hh]","hours"],minute:["m","minutes"],second:["s","seconds"],ampm:["[Aa]",""]},this.$widget=t('<span class="combodate"></span>').html(this.getTemplate()),this.initCombos(),this.$widget.on("change","select",t.proxy(function(){this.$element.val(this.getValue())},this)),this.$widget.find("select").css("width","auto"),this.$element.hide().after(this.$widget),this.setValue(this.$element.val()||this.options.value)},getTemplate:function(){var e=this.options.template;return t.each(this.map,function(t,i){i=i[0];var n=new RegExp(i+"+"),o=i.length>1?i.substring(1,2):i;e=e.replace(n,"{"+o+"}")}),e=e.replace(/ /g,"&nbsp;"),t.each(this.map,function(t,i){i=i[0];var n=i.length>1?i.substring(1,2):i;e=e.replace("{"+n+"}",'<select class="form-control '+t+'"></select>')}),e},initCombos:function(){var e=this;t.each(this.map,function(t){var i,n,o=e.$widget.find("."+t);o.length&&(e["$"+t]=o,i="fill"+t.charAt(0).toUpperCase()+t.slice(1),n=e[i](),e["$"+t].html(e.renderItems(n)))})},initItems:function(t){var e,i=[];if("name"===this.options.firstItem){e=moment.relativeTime||moment.langData()._relativeTime;var n="function"==typeof e[t]?e[t](1,!0,t,!1):e[t];n=n.split(" ").reverse()[0],i.push(["",n])}else"empty"===this.options.firstItem&&i.push(["",""]);return i},renderItems:function(t){for(var e=[],i=0;i<t.length;i++)e.push('<option value="'+t[i][0]+'">'+t[i][1]+"</option>");return e.join("\n")},fillDay:function(){var t,e,i=this.initItems("d"),n=-1!==this.options.template.indexOf("DD");for(e=1;31>=e;e++)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillMonth:function(){var t,e,i=this.initItems("M"),n=-1!==this.options.template.indexOf("MMMM"),o=-1!==this.options.template.indexOf("MMM"),r=-1!==this.options.template.indexOf("MM");for(e=0;11>=e;e++)t=n?moment().date(1).month(e).format("MMMM"):o?moment().date(1).month(e).format("MMM"):r?this.leadZero(e+1):e+1,i.push([e,t]);return i},fillYear:function(){var t,e,i=[],n=-1!==this.options.template.indexOf("YYYY");for(e=this.options.maxYear;e>=this.options.minYear;e--)t=n?e:(e+"").substring(2),i[this.options.yearDescending?"push":"unshift"]([e,t]);return i=this.initItems("y").concat(i)},fillHour:function(){var t,e,i=this.initItems("h"),n=-1!==this.options.template.indexOf("h"),o=(-1!==this.options.template.indexOf("H"),-1!==this.options.template.toLowerCase().indexOf("hh")),r=n?1:0,s=n?12:23;for(e=r;s>=e;e++)t=o?this.leadZero(e):e,i.push([e,t]);return i},fillMinute:function(){var t,e,i=this.initItems("m"),n=-1!==this.options.template.indexOf("mm");for(e=0;59>=e;e+=this.options.minuteStep)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillSecond:function(){var t,e,i=this.initItems("s"),n=-1!==this.options.template.indexOf("ss");for(e=0;59>=e;e+=this.options.secondStep)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillAmpm:function(){var t=-1!==this.options.template.indexOf("a"),e=(-1!==this.options.template.indexOf("A"),[["am",t?"am":"AM"],["pm",t?"pm":"PM"]]);return e},getValue:function(e){var i,n={},o=this,r=!1;return t.each(this.map,function(t){if("ampm"!==t){var e="day"===t?1:0;return n[t]=o["$"+t]?parseInt(o["$"+t].val(),10):e,isNaN(n[t])?(r=!0,!1):void 0}}),r?"":(this.$ampm&&(n.hour=12===n.hour?"am"===this.$ampm.val()?0:12:"am"===this.$ampm.val()?n.hour:n.hour+12),i=moment([n.year,n.month,n.day,n.hour,n.minute,n.second]),this.highlight(i),e=void 0===e?this.options.format:e,null===e?i.isValid()?i:null:i.isValid()?i.format(e):"")},setValue:function(e){function i(e,i){var n={};return e.children("option").each(function(e,o){var r,s=t(o).attr("value");""!==s&&(r=Math.abs(s-i),("undefined"==typeof n.distance||r<n.distance)&&(n={value:s,distance:r}))}),n.value}if(e){var n="string"==typeof e?moment(e,this.options.format):moment(e),o=this,r={};n.isValid()&&(t.each(this.map,function(t,e){"ampm"!==t&&(r[t]=n[e[1]]())}),this.$ampm&&(r.hour>=12?(r.ampm="pm",r.hour>12&&(r.hour-=12)):(r.ampm="am",0===r.hour&&(r.hour=12))),t.each(r,function(t,e){o["$"+t]&&("minute"===t&&o.options.minuteStep>1&&o.options.roundTime&&(e=i(o["$"+t],e)),"second"===t&&o.options.secondStep>1&&o.options.roundTime&&(e=i(o["$"+t],e)),o["$"+t].val(e))}),this.$element.val(n.format(this.options.format)))}},highlight:function(t){t.isValid()?this.options.errorClass?this.$widget.removeClass(this.options.errorClass):this.$widget.find("select").css("border-color",this.borderColor):this.options.errorClass?this.$widget.addClass(this.options.errorClass):(this.borderColor||(this.borderColor=this.$widget.find("select").css("border-color")),this.$widget.find("select").css("border-color","red"))},leadZero:function(t){return 9>=t?"0"+t:t},destroy:function(){this.$widget.remove(),this.$element.removeData("combodate").show()}},t.fn.combodate=function(i){var n,o=Array.apply(null,arguments);return o.shift(),"getValue"===i&&this.length&&(n=this.eq(0).data("combodate"))?n.getValue.apply(n,o):this.each(function(){var n=t(this),r=n.data("combodate"),s="object"==typeof i&&i;r||n.data("combodate",r=new e(this,s)),"string"==typeof i&&"function"==typeof r[i]&&r[i].apply(r,o)})},t.fn.combodate.defaults={format:"DD-MM-YYYY HH:mm",template:"D / MMM / YYYY   H : mm",value:null,minYear:1970,maxYear:2015,yearDescending:!0,minuteStep:5,secondStep:1,firstItem:"empty",errorClass:"has-error",roundTime:!0}}(window.jQuery),function(t){"use strict";var e=function(i){this.init("combodate",i,e.defaults),this.options.viewformat||(this.options.viewformat=this.options.format),i.combodate=t.fn.editableutils.tryParseJson(i.combodate,!0),this.options.combodate=t.extend({},e.defaults.combodate,i.combodate,{format:this.options.format,template:this.options.template})};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.$input.combodate(this.options.combodate)},value2html:function(e,i){var n=e?e.format(this.options.viewformat):"";t(i).text(n)},html2value:function(t){return t?moment(t,this.options.viewformat):null},value2str:function(t){return t?t.format(this.options.format):""},str2value:function(t){return t?moment(t,this.options.format):null},value2submit:function(t){return this.value2str(t)},value2input:function(t){this.$input.combodate("setValue",t)},input2value:function(){return this.$input.combodate("getValue",null)},activate:function(){this.$input.siblings(".combodate").find("select").eq(0).focus()},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text" class="form-control">',inputclass:null,format:"YYYY-MM-DD",viewformat:null,template:"D / MMM / YYYY",combodate:null}),t.fn.editabletypes.combodate=e}(window.jQuery),function(t){"use strict";t.extend(t.fn.editableform.Constructor.prototype,{initTemplate:function(){this.$form=t(t.fn.editableform.template),this.$form.find(".editable-error-block").addClass("help-block")}}),t.fn.editableform.buttons='<button type="submit" class="btn blue editable-submit"><i class="fa fa-check"></i></button><button type="button" class="btn default editable-cancel"><i class="fa fa-times"></i></button>',t.fn.editableform.errorGroupClass="has-error",t.fn.editableform.errorBlockClass=null}(window.jQuery),function(t){"use strict";t.fn.popover.defaults=t.fn.popover.Constructor.DEFAULTS,t.extend(t.fn.editableContainer.Popup.prototype,{containerName:"popover",containerDataName:"bs.popover",innerCss:t.fn.popover&&t(t.fn.popover.defaults.template).find("p").length?".popover-content p":".popover-content",initContainer:function(){t.extend(this.containerOptions,{trigger:"manual",selector:!1,content:" ",template:t.fn.popover.defaults.template});var e;this.$element.data("template")&&(e=this.$element.data("template"),this.$element.removeData("template")),this.call(this.containerOptions),e&&this.$element.data("template",e)},innerShow:function(){this.call("show")},innerHide:function(){this.call("hide")},innerDestroy:function(){this.call("destroy")},setContainerOption:function(t,e){this.container().options[t]=e},setPosition:function(){!function(){var e,i,n,o,r,s,a,l,c,h,d=this.tip();switch(r="function"==typeof this.options.placement?this.options.placement.call(this,d[0],this.$element[0]):this.options.placement,e=/in/.test(r),d.removeClass("top right bottom left").css({top:0,left:0,display:"block"}),i=this.getPosition(e),n=d[0].offsetWidth,o=d[0].offsetHeight,r=e?r.split(" ")[1]:r,l={top:i.top+i.height,left:i.left+i.width/2-n/2},a={top:i.top-o,left:i.left+i.width/2-n/2},c={top:i.top+i.height/2-o/2,left:i.left-n},h={top:i.top+i.height/2-o/2,left:i.left+i.width},r){case"bottom":l.top+o>t(window).scrollTop()+t(window).height()&&(r=a.top>t(window).scrollTop()?"top":h.left+n<t(window).scrollLeft()+t(window).width()?"right":c.left>t(window).scrollLeft()?"left":"right");break;case"top":a.top<t(window).scrollTop()&&(r=l.top+o<t(window).scrollTop()+t(window).height()?"bottom":h.left+n<t(window).scrollLeft()+t(window).width()?"right":c.left>t(window).scrollLeft()?"left":"right");break;case"left":c.left<t(window).scrollLeft()&&(r=h.left+n<t(window).scrollLeft()+t(window).width()?"right":a.top>t(window).scrollTop()?"top":a.top>t(window).scrollTop()?"bottom":"right");break;case"right":h.left+n>t(window).scrollLeft()+t(window).width()&&(c.left>t(window).scrollLeft()?r="left":a.top>t(window).scrollTop()?r="top":a.top>t(window).scrollTop()&&(r="bottom"))}switch(r){case"bottom":s=l;break;case"top":s=a;break;case"left":s=c;break;case"right":s=h}d.offset(s).addClass(r).addClass("in")}.call(this.container())}})}(window.jQuery),function(t){function e(){return new Date(Date.UTC.apply(Date,arguments))}function i(e,i){var n,o=t(e).data(),r={},s=new RegExp("^"+i.toLowerCase()+"([A-Z])"),i=new RegExp("^"+i.toLowerCase());for(var a in o)i.test(a)&&(n=a.replace(s,function(t,e){return e.toLowerCase()}),r[n]=o[a]);return r}function n(e){var i={};if(h[e]||(e=e.split("-")[0],h[e])){var n=h[e];return t.each(c,function(t,e){e in n&&(i[e]=n[e])}),i}}var o=function(e,i){this._process_options(i),this.element=t(e),this.isInline=!1,this.isInput=this.element.is("input"),this.component=this.element.is(".date")?this.element.find(".add-on, .btn"):!1,this.hasInput=this.component&&this.element.find("input").length,this.component&&0===this.component.length&&(this.component=!1),this.picker=t(d.template),this._buildEvents(),this._attachEvents(),this.isInline?this.picker.addClass("datepicker-inline").appendTo(this.element):this.picker.addClass("datepicker-dropdown dropdown-menu"),this.o.rtl&&(this.picker.addClass("datepicker-rtl"),this.picker.find(".prev i, .next i").toggleClass("fa-angle-left fa-angle-right")),this.viewMode=this.o.startView,this.o.calendarWeeks&&this.picker.find("tfoot th.today").attr("colspan",function(t,e){return parseInt(e)+1}),this._allow_update=!1,this.setStartDate(this.o.startDate),this.setEndDate(this.o.endDate),this.setDaysOfWeekDisabled(this.o.daysOfWeekDisabled),this.fillDow(),this.fillMonths(),this._allow_update=!0,this.update(),this.showMode(),this.isInline&&this.show()};o.prototype={constructor:o,_process_options:function(e){this._o=t.extend({},this._o,e);var i=this.o=t.extend({},this._o),n=i.language;switch(h[n]||(n=n.split("-")[0],h[n]||(n=l.language)),i.language=n,i.startView){case 2:case"decade":i.startView=2;break;case 1:case"year":i.startView=1;break;default:i.startView=0}switch(i.minViewMode){case 1:case"months":i.minViewMode=1;break;case 2:case"years":i.minViewMode=2;break;default:i.minViewMode=0}i.startView=Math.max(i.startView,i.minViewMode),i.weekStart%=7,i.weekEnd=(i.weekStart+6)%7;var o=d.parseFormat(i.format);i.startDate!==-1/0&&(i.startDate=d.parseDate(i.startDate,o,i.language)),1/0!==i.endDate&&(i.endDate=d.parseDate(i.endDate,o,i.language)),i.daysOfWeekDisabled=i.daysOfWeekDisabled||[],t.isArray(i.daysOfWeekDisabled)||(i.daysOfWeekDisabled=i.daysOfWeekDisabled.split(/[,\s]*/)),i.daysOfWeekDisabled=t.map(i.daysOfWeekDisabled,function(t){return parseInt(t,10)})},_events:[],_secondaryEvents:[],_applyEvents:function(t){for(var e,i,n=0;n<t.length;n++)e=t[n][0],i=t[n][1],e.on(i)},_unapplyEvents:function(t){for(var e,i,n=0;n<t.length;n++)e=t[n][0],i=t[n][1],e.off(i)},_buildEvents:function(){this.isInput?this._events=[[this.element,{focus:t.proxy(this.show,this),keyup:t.proxy(this.update,this),keydown:t.proxy(this.keydown,this)}]]:this.component&&this.hasInput?this._events=[[this.element.find("input"),{focus:t.proxy(this.show,this),keyup:t.proxy(this.update,this),keydown:t.proxy(this.keydown,this)}],[this.component,{click:t.proxy(this.show,this)}]]:this.element.is("div")?this.isInline=!0:this._events=[[this.element,{click:t.proxy(this.show,this)}]],this._secondaryEvents=[[this.picker,{click:t.proxy(this.click,this)}],[t(window),{resize:t.proxy(this.place,this)}],[t(document),{mousedown:t.proxy(function(t){this.element.is(t.target)||this.element.find(t.target).size()||this.picker.is(t.target)||this.picker.find(t.target).size()||this.hide()},this)}]]},_attachEvents:function(){this._detachEvents(),this._applyEvents(this._events)},_detachEvents:function(){this._unapplyEvents(this._events)},_attachSecondaryEvents:function(){this._detachSecondaryEvents(),this._applyEvents(this._secondaryEvents)},_detachSecondaryEvents:function(){this._unapplyEvents(this._secondaryEvents)},_trigger:function(e,i){var n=i||this.date,o=new Date(n.getTime()+6e4*n.getTimezoneOffset());this.element.trigger({type:e,date:o,format:t.proxy(function(t){var e=t||this.o.format;return d.formatDate(n,e,this.o.language)},this)})},show:function(t){this.isInline||this.picker.appendTo("body"),this.picker.show(),this.height=this.component?this.component.outerHeight():this.element.outerHeight(),this.place(),this._attachSecondaryEvents(),t&&t.preventDefault(),this._trigger("show")},hide:function(){this.isInline||this.picker.is(":visible")&&(this.picker.hide().detach(),this._detachSecondaryEvents(),this.viewMode=this.o.startView,this.showMode(),this.o.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())&&this.setValue(),this._trigger("hide"))},remove:function(){this.hide(),this._detachEvents(),this._detachSecondaryEvents(),this.picker.remove(),delete this.element.data().datepicker,this.isInput||delete this.element.data().date},getDate:function(){var t=this.getUTCDate();return new Date(t.getTime()+6e4*t.getTimezoneOffset())},getUTCDate:function(){return this.date},setDate:function(t){this.setUTCDate(new Date(t.getTime()-6e4*t.getTimezoneOffset()))},setUTCDate:function(t){this.date=t,this.setValue()},setValue:function(){var t=this.getFormattedDate();this.isInput?this.element.val(t):this.component&&this.element.find("input").val(t)},getFormattedDate:function(t){return void 0===t&&(t=this.o.format),d.formatDate(this.date,t,this.o.language)},setStartDate:function(t){this._process_options({startDate:t}),this.update(),this.updateNavArrows()},setEndDate:function(t){this._process_options({endDate:t}),this.update(),this.updateNavArrows()},setDaysOfWeekDisabled:function(t){this._process_options({daysOfWeekDisabled:t}),this.update(),this.updateNavArrows()},place:function(){if(!this.isInline){var e=parseInt(this.element.parents().filter(function(){return"auto"!=t(this).css("z-index")}).first().css("z-index"))+10,i=this.component?this.component.parent().offset():this.element.offset(),n=this.component?this.component.outerHeight(!0):this.element.outerHeight(!0);this.picker.css({top:i.top+n,left:i.left,zIndex:e})}},_allow_update:!0,update:function(){if(this._allow_update){var t,e=!1;arguments&&arguments.length&&("string"==typeof arguments[0]||arguments[0]instanceof Date)?(t=arguments[0],e=!0):(t=this.isInput?this.element.val():this.element.data("date")||this.element.find("input").val(),delete this.element.data().date),this.date=d.parseDate(t,this.o.format,this.o.language),e&&this.setValue(),this.viewDate=this.date<this.o.startDate?new Date(this.o.startDate):this.date>this.o.endDate?new Date(this.o.endDate):new Date(this.date),this.fill()}},fillDow:function(){var t=this.o.weekStart,e="<tr>";if(this.o.calendarWeeks){var i='<th class="cw">&nbsp;</th>';e+=i,this.picker.find(".datepicker-days thead tr:first-child").prepend(i)}for(;t<this.o.weekStart+7;)e+='<th class="dow">'+h[this.o.language].daysMin[t++%7]+"</th>";e+="</tr>",this.picker.find(".datepicker-days thead").append(e)},fillMonths:function(){for(var t="",e=0;12>e;)t+='<span class="month">'+h[this.o.language].monthsShort[e++]+"</span>";this.picker.find(".datepicker-months td").html(t)},setRange:function(e){e&&e.length?this.range=t.map(e,function(t){return t.valueOf()}):delete this.range,this.fill()},getClassNames:function(e){var i=[],n=this.viewDate.getUTCFullYear(),o=this.viewDate.getUTCMonth(),r=this.date.valueOf(),s=new Date;return e.getUTCFullYear()<n||e.getUTCFullYear()==n&&e.getUTCMonth()<o?i.push("old"):(e.getUTCFullYear()>n||e.getUTCFullYear()==n&&e.getUTCMonth()>o)&&i.push("new"),this.o.todayHighlight&&e.getUTCFullYear()==s.getFullYear()&&e.getUTCMonth()==s.getMonth()&&e.getUTCDate()==s.getDate()&&i.push("today"),r&&e.valueOf()==r&&i.push("active"),(e.valueOf()<this.o.startDate||e.valueOf()>this.o.endDate||-1!==t.inArray(e.getUTCDay(),this.o.daysOfWeekDisabled))&&i.push("disabled"),this.range&&(e>this.range[0]&&e<this.range[this.range.length-1]&&i.push("range"),-1!=t.inArray(e.valueOf(),this.range)&&i.push("selected")),i},fill:function(){var i,n=new Date(this.viewDate),o=n.getUTCFullYear(),r=n.getUTCMonth(),s=this.o.startDate!==-1/0?this.o.startDate.getUTCFullYear():-1/0,a=this.o.startDate!==-1/0?this.o.startDate.getUTCMonth():-1/0,l=1/0!==this.o.endDate?this.o.endDate.getUTCFullYear():1/0,c=1/0!==this.o.endDate?this.o.endDate.getUTCMonth():1/0;this.date&&this.date.valueOf(),this.picker.find(".datepicker-days thead th.datepicker-switch").text(h[this.o.language].months[r]+" "+o),this.picker.find("tfoot th.today").text(h[this.o.language].today).toggle(this.o.todayBtn!==!1),this.picker.find("tfoot th.clear").text(h[this.o.language].clear).toggle(this.o.clearBtn!==!1),this.updateNavArrows(),this.fillMonths();var u=e(o,r-1,28,0,0,0,0),p=d.getDaysInMonth(u.getUTCFullYear(),u.getUTCMonth());u.setUTCDate(p),u.setUTCDate(p-(u.getUTCDay()-this.o.weekStart+7)%7);var f=new Date(u);f.setUTCDate(f.getUTCDate()+42),f=f.valueOf();for(var g,m=[];u.valueOf()<f;){if(u.getUTCDay()==this.o.weekStart&&(m.push("<tr>"),this.o.calendarWeeks)){var v=new Date(+u+864e5*((this.o.weekStart-u.getUTCDay()-7)%7)),y=new Date(+v+864e5*((11-v.getUTCDay())%7)),b=new Date(+(b=e(y.getUTCFullYear(),0,1))+864e5*((11-b.getUTCDay())%7)),x=(y-b)/864e5/7+1;m.push('<td class="cw">'+x+"</td>")}g=this.getClassNames(u),g.push("day");var w=this.o.beforeShowDay(u);void 0===w?w={}:"boolean"==typeof w?w={enabled:w}:"string"==typeof w&&(w={classes:w}),w.enabled===!1&&g.push("disabled"),w.classes&&(g=g.concat(w.classes.split(/\s+/))),w.tooltip&&(i=w.tooltip),g=t.unique(g),m.push('<td class="'+g.join(" ")+'"'+(i?' title="'+i+'"':"")+">"+u.getUTCDate()+"</td>"),u.getUTCDay()==this.o.weekEnd&&m.push("</tr>"),u.setUTCDate(u.getUTCDate()+1)}this.picker.find(".datepicker-days tbody").empty().append(m.join(""));var _=this.date&&this.date.getUTCFullYear(),C=this.picker.find(".datepicker-months").find("th:eq(1)").text(o).end().find("span").removeClass("active");_&&_==o&&C.eq(this.date.getUTCMonth()).addClass("active"),(s>o||o>l)&&C.addClass("disabled"),o==s&&C.slice(0,a).addClass("disabled"),o==l&&C.slice(c+1).addClass("disabled"),m="",o=10*parseInt(o/10,10);var S=this.picker.find(".datepicker-years").find("th:eq(1)").text(o+"-"+(o+9)).end().find("td");o-=1;for(var $=-1;11>$;$++)m+='<span class="year'+(-1==$?" old":10==$?" new":"")+(_==o?" active":"")+(s>o||o>l?" disabled":"")+'">'+o+"</span>",o+=1;S.html(m)},updateNavArrows:function(){if(this._allow_update){var t=new Date(this.viewDate),e=t.getUTCFullYear(),i=t.getUTCMonth();switch(this.viewMode){case 0:this.o.startDate!==-1/0&&e<=this.o.startDate.getUTCFullYear()&&i<=this.o.startDate.getUTCMonth()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),1/0!==this.o.endDate&&e>=this.o.endDate.getUTCFullYear()&&i>=this.o.endDate.getUTCMonth()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 1:case 2:this.o.startDate!==-1/0&&e<=this.o.startDate.getUTCFullYear()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),1/0!==this.o.endDate&&e>=this.o.endDate.getUTCFullYear()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"})}}},click:function(i){i.preventDefault();var n=t(i.target).closest("span, td, th");if(1==n.length)switch(n[0].nodeName.toLowerCase()){case"th":switch(n[0].className){case"datepicker-switch":this.showMode(1);break;case"prev":case"next":var o=d.modes[this.viewMode].navStep*("prev"==n[0].className?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveMonth(this.viewDate,o);break;case 1:case 2:this.viewDate=this.moveYear(this.viewDate,o)}this.fill();break;case"today":var r=new Date;r=e(r.getFullYear(),r.getMonth(),r.getDate(),0,0,0),this.showMode(-2);var s="linked"==this.o.todayBtn?null:"view";this._setDate(r,s);break;case"clear":var a;this.isInput?a=this.element:this.component&&(a=this.element.find("input")),a&&a.val("").change(),this._trigger("changeDate"),this.update(),this.o.autoclose&&this.hide()}break;case"span":if(!n.is(".disabled")){if(this.viewDate.setUTCDate(1),n.is(".month")){var l=1,c=n.parent().find("span").index(n),h=this.viewDate.getUTCFullYear();this.viewDate.setUTCMonth(c),this._trigger("changeMonth",this.viewDate),1===this.o.minViewMode&&this._setDate(e(h,c,l,0,0,0,0))}else{var h=parseInt(n.text(),10)||0,l=1,c=0;this.viewDate.setUTCFullYear(h),this._trigger("changeYear",this.viewDate),2===this.o.minViewMode&&this._setDate(e(h,c,l,0,0,0,0))}this.showMode(-1),this.fill()}break;case"td":if(n.is(".day")&&!n.is(".disabled")){var l=parseInt(n.text(),10)||1,h=this.viewDate.getUTCFullYear(),c=this.viewDate.getUTCMonth();n.is(".old")?0===c?(c=11,h-=1):c-=1:n.is(".new")&&(11==c?(c=0,h+=1):c+=1),this._setDate(e(h,c,l,0,0,0,0))}}},_setDate:function(t,e){e&&"date"!=e||(this.date=new Date(t)),e&&"view"!=e||(this.viewDate=new Date(t)),this.fill(),this.setValue(),this._trigger("changeDate");var i;this.isInput?i=this.element:this.component&&(i=this.element.find("input")),i&&(i.change(),!this.o.autoclose||e&&"date"!=e||this.hide())},moveMonth:function(t,e){if(!e)return t;var i,n,o=new Date(t.valueOf()),r=o.getUTCDate(),s=o.getUTCMonth(),a=Math.abs(e);if(e=e>0?1:-1,1==a)n=-1==e?function(){return o.getUTCMonth()==s}:function(){return o.getUTCMonth()!=i},i=s+e,o.setUTCMonth(i),(0>i||i>11)&&(i=(i+12)%12);else{for(var l=0;a>l;l++)o=this.moveMonth(o,e);i=o.getUTCMonth(),o.setUTCDate(r),n=function(){return i!=o.getUTCMonth()}}for(;n();)o.setUTCDate(--r),o.setUTCMonth(i);return o},moveYear:function(t,e){return this.moveMonth(t,12*e)},dateWithinRange:function(t){return t>=this.o.startDate&&t<=this.o.endDate},keydown:function(t){if(this.picker.is(":not(:visible)"))return 27==t.keyCode&&this.show(),void 0;var e,i,n,o=!1;switch(t.keyCode){case 27:this.hide(),t.preventDefault();break;case 37:case 39:if(!this.o.keyboardNavigation)break;e=37==t.keyCode?-1:1,t.ctrlKey?(i=this.moveYear(this.date,e),n=this.moveYear(this.viewDate,e)):t.shiftKey?(i=this.moveMonth(this.date,e),n=this.moveMonth(this.viewDate,e)):(i=new Date(this.date),i.setUTCDate(this.date.getUTCDate()+e),n=new Date(this.viewDate),n.setUTCDate(this.viewDate.getUTCDate()+e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=n,this.setValue(),this.update(),t.preventDefault(),o=!0);break;case 38:case 40:if(!this.o.keyboardNavigation)break;e=38==t.keyCode?-1:1,t.ctrlKey?(i=this.moveYear(this.date,e),n=this.moveYear(this.viewDate,e)):t.shiftKey?(i=this.moveMonth(this.date,e),n=this.moveMonth(this.viewDate,e)):(i=new Date(this.date),i.setUTCDate(this.date.getUTCDate()+7*e),n=new Date(this.viewDate),n.setUTCDate(this.viewDate.getUTCDate()+7*e)),this.dateWithinRange(i)&&(this.date=i,this.viewDate=n,this.setValue(),this.update(),t.preventDefault(),o=!0);break;case 13:this.hide(),t.preventDefault();break;case 9:this.hide()}if(o){this._trigger("changeDate");var r;this.isInput?r=this.element:this.component&&(r=this.element.find("input")),r&&r.change()}},showMode:function(t){t&&(this.viewMode=Math.max(this.o.minViewMode,Math.min(2,this.viewMode+t))),this.picker.find(">div").hide().filter(".datepicker-"+d.modes[this.viewMode].clsName).css("display","block"),this.updateNavArrows()}};var r=function(e,i){this.element=t(e),this.inputs=t.map(i.inputs,function(t){return t.jquery?t[0]:t}),delete i.inputs,t(this.inputs).datepicker(i).bind("changeDate",t.proxy(this.dateUpdated,this)),this.pickers=t.map(this.inputs,function(e){return t(e).data("datepicker")}),this.updateDates()};r.prototype={updateDates:function(){this.dates=t.map(this.pickers,function(t){return t.date}),this.updateRanges()},updateRanges:function(){var e=t.map(this.dates,function(t){return t.valueOf()});t.each(this.pickers,function(t,i){i.setRange(e)})},dateUpdated:function(e){var i=t(e.target).data("datepicker"),n=i.getUTCDate(),o=t.inArray(e.target,this.inputs),r=this.inputs.length;if(-1!=o){if(n<this.dates[o])for(;o>=0&&n<this.dates[o];)this.pickers[o--].setUTCDate(n);else if(n>this.dates[o])for(;r>o&&n>this.dates[o];)this.pickers[o++].setUTCDate(n);this.updateDates()}},remove:function(){t.map(this.pickers,function(t){t.remove()}),delete this.element.data().datepicker}};var s=t.fn.datepicker,a=t.fn.datepicker=function(e){var s=Array.apply(null,arguments);s.shift();var a;return this.each(function(){var c=t(this),h=c.data("datepicker"),d="object"==typeof e&&e;if(!h){var u=i(this,"date"),p=t.extend({},l,u,d),f=n(p.language),g=t.extend({},l,f,u,d);if(c.is(".input-daterange")||g.inputs){var m={inputs:g.inputs||c.find("input").toArray()};c.data("datepicker",h=new r(this,t.extend(g,m)))}else c.data("datepicker",h=new o(this,g))}return"string"==typeof e&&"function"==typeof h[e]&&(a=h[e].apply(h,s),void 0!==a)?!1:void 0}),void 0!==a?a:this},l=t.fn.datepicker.defaults={autoclose:!1,beforeShowDay:t.noop,calendarWeeks:!1,clearBtn:!1,daysOfWeekDisabled:[],endDate:1/0,forceParse:!0,format:"mm/dd/yyyy",keyboardNavigation:!0,language:"en",minViewMode:0,rtl:!1,startDate:-1/0,startView:0,todayBtn:!1,todayHighlight:!1,weekStart:0},c=t.fn.datepicker.locale_opts=["format","rtl","weekStart"];t.fn.datepicker.Constructor=o;var h=t.fn.datepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear"}},d={modes:[{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(t){return 0===t%4&&0!==t%100||0===t%400},getDaysInMonth:function(t,e){return[31,d.isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]},validParts:/dd?|DD?|mm?|MM?|yy(?:yy)?/g,nonpunctuation:/[^ -\/:-@\[\u3400-\u9fff-`{-~\t\n\r]+/g,parseFormat:function(t){var e=t.replace(this.validParts,"\0").split("\0"),i=t.match(this.validParts);if(!e||!e.length||!i||0===i.length)throw new Error("Invalid date format.");return{separators:e,parts:i}},parseDate:function(i,n,r){if(i instanceof Date)return i;if("string"==typeof n&&(n=d.parseFormat(n)),/^[\-+]\d+[dmwy]([\s,]+[\-+]\d+[dmwy])*$/.test(i)){var s,a,l=/([\-+]\d+)([dmwy])/,c=i.match(/([\-+]\d+)([dmwy])/g);i=new Date;for(var u=0;u<c.length;u++)switch(s=l.exec(c[u]),a=parseInt(s[1]),s[2]){case"d":i.setUTCDate(i.getUTCDate()+a);break;case"m":i=o.prototype.moveMonth.call(o.prototype,i,a);break;case"w":i.setUTCDate(i.getUTCDate()+7*a);break;case"y":i=o.prototype.moveYear.call(o.prototype,i,a)}return e(i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate(),0,0,0)}var p,f,s,c=i&&i.match(this.nonpunctuation)||[],i=new Date,g={},m=["yyyy","yy","M","MM","m","mm","d","dd"],v={yyyy:function(t,e){return t.setUTCFullYear(e)},yy:function(t,e){return t.setUTCFullYear(2e3+e)},m:function(t,e){for(e-=1;0>e;)e+=12;for(e%=12,t.setUTCMonth(e);t.getUTCMonth()!=e;)t.setUTCDate(t.getUTCDate()-1);return t},d:function(t,e){return t.setUTCDate(e)}};v.M=v.MM=v.mm=v.m,v.dd=v.d,i=e(i.getFullYear(),i.getMonth(),i.getDate(),0,0,0);var y=n.parts.slice();if(c.length!=y.length&&(y=t(y).filter(function(e,i){return-1!==t.inArray(i,m)
}).toArray()),c.length==y.length){for(var u=0,b=y.length;b>u;u++){if(p=parseInt(c[u],10),s=y[u],isNaN(p))switch(s){case"MM":f=t(h[r].months).filter(function(){var t=this.slice(0,c[u].length),e=c[u].slice(0,t.length);return t==e}),p=t.inArray(f[0],h[r].months)+1;break;case"M":f=t(h[r].monthsShort).filter(function(){var t=this.slice(0,c[u].length),e=c[u].slice(0,t.length);return t==e}),p=t.inArray(f[0],h[r].monthsShort)+1}g[s]=p}for(var x,u=0;u<m.length;u++)x=m[u],x in g&&!isNaN(g[x])&&v[x](i,g[x])}return i},formatDate:function(e,i,n){"string"==typeof i&&(i=d.parseFormat(i));var o={d:e.getUTCDate(),D:h[n].daysShort[e.getUTCDay()],DD:h[n].days[e.getUTCDay()],m:e.getUTCMonth()+1,M:h[n].monthsShort[e.getUTCMonth()],MM:h[n].months[e.getUTCMonth()],yy:e.getUTCFullYear().toString().substring(2),yyyy:e.getUTCFullYear()};o.dd=(o.d<10?"0":"")+o.d,o.mm=(o.m<10?"0":"")+o.m;for(var e=[],r=t.extend([],i.separators),s=0,a=i.parts.length;a>=s;s++)r.length&&e.push(r.shift()),e.push(o[i.parts[s]]);return e.join("")},headTemplate:'<thead><tr><th class="prev"><i class="fa fa-angle-left"/></th><th colspan="5" class="datepicker-switch"></th><th class="next"><i class="fa fa-angle-right"/></th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};d.template='<div class="datepicker"><div class="datepicker-days"><table class=" table-condensed">'+d.headTemplate+"<tbody></tbody>"+d.footTemplate+"</table>"+"</div>"+'<div class="datepicker-months">'+'<table class="table-condensed">'+d.headTemplate+d.contTemplate+d.footTemplate+"</table>"+"</div>"+'<div class="datepicker-years">'+'<table class="table-condensed">'+d.headTemplate+d.contTemplate+d.footTemplate+"</table>"+"</div>"+"</div>",t.fn.datepicker.DPGlobal=d,t.fn.datepicker.noConflict=function(){return t.fn.datepicker=s,this},t(document).on("focus.datepicker.data-api click.datepicker.data-api",'[data-provide="datepicker"]',function(e){var i=t(this);i.data("datepicker")||(e.preventDefault(),a.call(i,"show"))}),t(function(){a.call(t('[data-provide="datepicker-inline"]'))})}(window.jQuery),function(t){"use strict";t.fn.bdatepicker=t.fn.datepicker.noConflict(),t.fn.datepicker||(t.fn.datepicker=t.fn.bdatepicker);var e=function(t){this.init("date",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{initPicker:function(e,i){this.options.viewformat||(this.options.viewformat=this.options.format),e.datepicker=t.fn.editableutils.tryParseJson(e.datepicker,!0),this.options.datepicker=t.extend({},i.datepicker,e.datepicker,{format:this.options.viewformat}),this.options.datepicker.language=this.options.datepicker.language||"en",this.dpg=t.fn.bdatepicker.DPGlobal,this.parsedFormat=this.dpg.parseFormat(this.options.format),this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat)},render:function(){this.$input.bdatepicker(this.options.datepicker),this.options.clear&&(this.$clear=t('<a href="#"></a>').html(this.options.clear).click(t.proxy(function(t){t.preventDefault(),t.stopPropagation(),this.clear()},this)),this.$tpl.parent().append(t('<div class="editable-clear">').append(this.$clear)))},value2html:function(t,i){var n=t?this.dpg.formatDate(t,this.parsedViewFormat,this.options.datepicker.language):"";e.superclass.value2html(n,i)},html2value:function(t){return this.parseDate(t,this.parsedViewFormat)},value2str:function(t){return t?this.dpg.formatDate(t,this.parsedFormat,this.options.datepicker.language):""},str2value:function(t){return this.parseDate(t,this.parsedFormat)},value2submit:function(t){return this.value2str(t)},value2input:function(t){this.$input.bdatepicker("update",t)},input2value:function(){return this.$input.data("datepicker").date},activate:function(){},clear:function(){this.$input.data("datepicker").date=null,this.$input.find(".active").removeClass("active"),this.options.showbuttons||this.$input.closest("form").submit()},autosubmit:function(){this.$input.on("mouseup",".day",function(e){if(!t(e.currentTarget).is(".old")&&!t(e.currentTarget).is(".new")){var i=t(this).closest("form");setTimeout(function(){i.submit()},200)}})},parseDate:function(t,e){var i,n=null;return t&&(n=this.dpg.parseDate(t,e,this.options.datepicker.language),"string"==typeof t&&(i=this.dpg.formatDate(n,e,this.options.datepicker.language),t!==i&&(n=null))),n}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd",viewformat:null,datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:!1},clear:'clear <i class="icon-remove fa fa-times" style="font-size:12px"></i>'}),t.fn.editabletypes.date=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("datefield",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.date),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.find("input"),this.setClass(),this.setAttr("placeholder"),this.$tpl.bdatepicker(this.options.datepicker),this.$input.off("focus keydown"),this.$input.keyup(t.proxy(function(){this.$tpl.removeData("date"),this.$tpl.bdatepicker("update")},this))},value2input:function(t){this.$input.val(t?this.dpg.formatDate(t,this.parsedViewFormat,this.options.datepicker.language):""),this.$tpl.bdatepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.date.defaults,{tpl:'<div class="input-group input-group-fixed date"><input type="text" readonly class="form-control input-medium pull-left"/><span class="input-group-btn pull-left"><button class="btn default" type="button"><i class="fa fa-calendar"></i></button></span></div>',inputclass:"form-control input-medium",datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:!0}}),t.fn.editabletypes.datefield=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("datetime",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{initPicker:function(e,i){this.options.viewformat||(this.options.viewformat=this.options.format),e.datetimepicker=t.fn.editableutils.tryParseJson(e.datetimepicker,!0),this.options.datetimepicker=t.extend({},i.datetimepicker,e.datetimepicker,{format:this.options.viewformat}),this.options.datetimepicker.language=this.options.datetimepicker.language||"en",this.dpg=t.fn.datetimepicker.DPGlobal,this.parsedFormat=this.dpg.parseFormat(this.options.format,this.options.formatType),this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat,this.options.formatType)},render:function(){this.$input.datetimepicker(this.options.datetimepicker),this.$input.on("changeMode",function(){var e=t(this).closest("form").parent();setTimeout(function(){e.triggerHandler("resize")},0)}),this.options.clear&&(this.$clear=t('<a href="#"></a>').html(this.options.clear).click(t.proxy(function(t){t.preventDefault(),t.stopPropagation(),this.clear()},this)),this.$tpl.parent().append(t('<div class="editable-clear">').append(this.$clear)))},value2html:function(t,i){var n=t?this.dpg.formatDate(this.toUTC(t),this.parsedViewFormat,this.options.datetimepicker.language,this.options.formatType):"";return i?(e.superclass.value2html(n,i),void 0):n},html2value:function(t){var e=this.parseDate(t,this.parsedViewFormat);return e?this.fromUTC(e):null},value2str:function(t){return t?this.dpg.formatDate(this.toUTC(t),this.parsedFormat,this.options.datetimepicker.language,this.options.formatType):""},str2value:function(t){var e=this.parseDate(t,this.parsedFormat);return e?this.fromUTC(e):null},value2submit:function(t){return this.value2str(t)},value2input:function(t){t&&this.$input.data("datetimepicker").setDate(t)},input2value:function(){var t=this.$input.data("datetimepicker");return t.date?t.getDate():null},activate:function(){},clear:function(){this.$input.data("datetimepicker").date=null,this.$input.find(".active").removeClass("active"),this.options.showbuttons||this.$input.closest("form").submit()},autosubmit:function(){this.$input.on("mouseup",".minute",function(){var e=t(this).closest("form");setTimeout(function(){e.submit()},200)})},toUTC:function(t){return t?new Date(t.valueOf()-6e4*t.getTimezoneOffset()):t},fromUTC:function(t){return t?new Date(t.valueOf()+6e4*t.getTimezoneOffset()):t},parseDate:function(t,e){var i,n=null;return t&&(n=this.dpg.parseDate(t,e,this.options.datetimepicker.language,this.options.formatType),"string"==typeof t&&(i=this.dpg.formatDate(n,e,this.options.datetimepicker.language,this.options.formatType),t!==i&&(n=null))),n}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd hh:ii",formatType:"standard",viewformat:null,datetimepicker:{todayHighlight:!1,autoclose:!1},clear:'clear <i class="icon-remove fa fa-times" style="font-size:12px"></i>'}),t.fn.editabletypes.datetime=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("datetimefield",t,e.defaults),this.initPicker(t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.datetime),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.find("input"),this.setClass(),this.setAttr("placeholder"),this.$tpl.datetimepicker(this.options.datetimepicker),this.$input.off("focus keydown"),this.$input.keyup(t.proxy(function(){this.$tpl.removeData("date"),this.$tpl.datetimepicker("update")},this))},value2input:function(t){this.$input.val(this.value2html(t)),this.$tpl.datetimepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.datetime.defaults,{tpl:'<div class="input-group input-group-fixed date"><input type="text" readonly class="form-control input-medium pull-left"/><span class="input-group-btn pull-left"><button class="btn date-set default" type="button"><i class="fa fa-calendar"></i></button></span></div>',inputclass:"form-control input-medium",datetimepicker:{todayHighlight:!1,autoclose:!0}}),t.fn.editabletypes.datetimefield=e}(window.jQuery),function(t){"use strict";var e=function(i){this.init("typeahead",i,e.defaults),this.options.typeahead=t.extend({},e.defaults.typeahead,{matcher:this.matcher,sorter:this.sorter,highlighter:this.highlighter,updater:this.updater},i.typeahead)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){this.$input=this.$tpl.is("input")?this.$tpl:this.$tpl.find('input[type="text"]'),this.options.typeahead.source=this.sourceData,this.$input.typeahead(this.options.typeahead);var e=this.$input.data("typeahead");e.render=t.proxy(this.typeaheadRender,e),e.select=t.proxy(this.typeaheadSelect,e),e.move=t.proxy(this.typeaheadMove,e),this.renderClear(),this.setClass(),this.setAttr("placeholder")},value2htmlFinal:function(e,i){if(this.getIsObjects()){var n=t.fn.editableutils.itemsByValue(e,this.sourceData);t(i).text(n.length?n[0].text:"")}else t(i).text(e)},html2value:function(t){return t?t:null},value2input:function(e){if(this.getIsObjects()){var i=t.fn.editableutils.itemsByValue(e,this.sourceData);this.$input.data("value",e).val(i.length?i[0].text:"")}else this.$input.val(e)},input2value:function(){if(this.getIsObjects()){var e=this.$input.data("value"),i=t.fn.editableutils.itemsByValue(e,this.sourceData);return i.length&&i[0].text.toLowerCase()===this.$input.val().toLowerCase()?e:null}return this.$input.val()},getIsObjects:function(){if(void 0===this.isObjects){this.isObjects=!1;for(var t=0;t<this.sourceData.length;t++)if(this.sourceData[t].value!==this.sourceData[t].text){this.isObjects=!0;break}}return this.isObjects},activate:t.fn.editabletypes.text.prototype.activate,renderClear:t.fn.editabletypes.text.prototype.renderClear,postrender:t.fn.editabletypes.text.prototype.postrender,toggleClear:t.fn.editabletypes.text.prototype.toggleClear,clear:function(){t.fn.editabletypes.text.prototype.clear.call(this),this.$input.data("value","")},matcher:function(e){return t.fn.typeahead.Constructor.prototype.matcher.call(this,e.text)},sorter:function(t){for(var e,i,n=[],o=[],r=[];e=t.shift();)i=e.text,i.toLowerCase().indexOf(this.query.toLowerCase())?~i.indexOf(this.query)?o.push(e):r.push(e):n.push(e);return n.concat(o,r)},highlighter:function(e){return t.fn.typeahead.Constructor.prototype.highlighter.call(this,e.text)},updater:function(t){return this.$element.data("value",t.value),t.text},typeaheadRender:function(e){var i=this;return e=t(e).map(function(e,n){return e=t(i.options.item).data("item",n),e.find("a").html(i.highlighter(n)),e[0]}),this.options.autoSelect&&e.first().addClass("active"),this.$menu.html(e),this},typeaheadSelect:function(){var t=this.$menu.find(".active").data("item");return(this.options.autoSelect||t)&&this.$element.val(this.updater(t)).change(),this.hide()},typeaheadMove:function(t){if(this.shown){switch(t.keyCode){case 9:case 13:case 27:if(!this.$menu.find(".active").length)return;t.preventDefault();break;case 38:t.preventDefault(),this.prev();break;case 40:t.preventDefault(),this.next()}t.stopPropagation()}}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:'<input type="text">',typeahead:null,clear:!0}),t.fn.editabletypes.typeahead=e}(window.jQuery);