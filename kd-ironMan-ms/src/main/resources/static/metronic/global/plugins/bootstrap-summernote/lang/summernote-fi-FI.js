(function ($) {
  $.extend($.summernote.lang, {
    'fi-FI': {
      font: {
        bold: 'Lihavoitu',
        italic: 'Kursiivi',
        underline: 'Alleviivaa',
        clear: 'Tyhje<PERSON><PERSON> muotoilu',
        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        strikethrough: 'Y<PERSON><PERSON><PERSON><PERSON>',
        size: 'Kir<PERSON><PERSON><PERSON>'
      },
      image: {
        image: '<PERSON><PERSON>',
        insert: 'Lisää kuva',
        resizeFull: '<PERSON><PERSON> leveys',
        resizeHalf: 'Puolikas leveys',
        resizeQuarter: 'Neljäsosa leveys',
        floatLeft: '<PERSON><PERSON>ita vasemmalle',
        floatRight: 'Sijoita oikealle',
        floatNone: 'Ei sijoitusta',
        dragImageHere: 'Vedä kuva tähän',
        selectFromFiles: '<PERSON><PERSON>e tiedostoista',
        url: 'URL-osoitteen mukaan',
        remove: 'Poista kuva'
      },
      link: {
        link: '<PERSON><PERSON>',
        insert: '<PERSON><PERSON><PERSON><PERSON> linkki',
        unlink: '<PERSON><PERSON> linkki',
        edit: 'Muokkaa',
        textToDisplay: '<PERSON>äytettäv<PERSON> teksti',
        url: 'Linkin URL-osoite?',
        openInNewWindow: 'Avaa uudessa ikkunassa'
      },
      table: {
        table: 'Taulukko'
      },
      hr: {
        insert: 'Lisää vaakaviiva'
      },
      style: {
        style: 'Tyyli',
        normal: 'Normaali',
        blockquote: 'Lainaus',
        pre: 'Koodi',
        h1: 'Otsikko 1',
        h2: 'Otsikko 2',
        h3: 'Otsikko 3',
        h4: 'Otsikko 4',
        h5: 'Otsikko 5',
        h6: 'Otsikko 6'
      },
      lists: {
        unordered: 'Luettelomerkitty luettelo',
        ordered: 'Numeroitu luettelo'
      },
      options: {
        help: 'Ohje',
        fullscreen: 'Koko näyttö',
        codeview: 'HTML-näkymä'
      },
      paragraph: {
        paragraph: 'Kappale',
        outdent: 'Pienennä sisennystä',
        indent: 'Suurenna sisennystä',
        left: 'Tasaus vasemmalle',
        center: 'Keskitä',
        right: 'Tasaus oikealle',
        justify: 'Tasaa'
      },
      color: {
        recent: 'Viimeisin väri',
        more: 'Lisää värejä',
        background: 'Taustaväri',
        foreground: 'Tekstin väri',
        transparent: 'Läpinäkyvä',
        setTransparent: 'Aseta läpinäkyväksi',
        reset: 'Palauta',
        resetToDefault: 'Palauta oletusarvoksi'
      },
      shortcut: {
        shortcuts: 'Pikanäppäimet',
        close: 'Sulje',
        textFormatting: 'Tekstin muotoilu',
        action: 'Toiminto',
        paragraphFormatting: 'Kappaleen muotoilu',
        documentStyle: 'Asiakirjan tyyli'
      },
      history: {
        undo: 'Kumoa',
        redo: 'Toista'
      }
    }
  });
})(jQuery);
