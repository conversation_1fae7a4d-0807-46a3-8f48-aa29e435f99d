(function ($) {
  $.extend($.summernote.lang, {
    'hu-HU': {
      font: {
        bold: 'Félkövér',
        italic: 'Dőlt',
        underline: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        clear: '<PERSON><PERSON><PERSON><PERSON> törlése',
        height: '<PERSON><PERSON><PERSON><PERSON>',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        strikethrough: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        size: '<PERSON><PERSON><PERSON><PERSON>'
      },
      image: {
        image: '<PERSON>é<PERSON>',
        insert: '<PERSON>ép beszúr<PERSON>a',
        resizeFull: 'Átméretezés teljes méretre',
        resizeHalf: 'Átméretezés felére',
        resizeQuarter: 'Átméretezés negyedére',
        floatLeft: 'Igaz<PERSON><PERSON>ás balra',
        floatRight: 'I<PERSON>z<PERSON><PERSON><PERSON> jobbra',
        floatNone: 'Igazítás törlése',
        dragImageHere: 'Ide húzhatod a képet',
        selectFromFiles: 'Fájlok kiválasztása',
        url: 'Kép URL címe',
        remove: '<PERSON>ép törl<PERSON>e'
      },
      link: {
        link: 'Hivat<PERSON><PERSON><PERSON>',
        insert: '<PERSON><PERSON><PERSON><PERSON><PERSON> beszúr<PERSON>',
        unlink: '<PERSON><PERSON><PERSON><PERSON>ás megszüntetése',
        edit: '<PERSON>zerkesztés',
        textToDisplay: 'Megjelenítendő szöveg',
        url: 'Milyen URL címre hivatkozzon?',
        openInNewWindow: 'Megnyitás új ablakban'
      },
      table: {
        table: 'Táblázat'
      },
      hr: {
        insert: 'Elválasztó vonal beszúrása'
      },
      style: {
        style: 'Stílus',
        normal: 'Normál',
        blockquote: 'Idézet',
        pre: 'Kód',
        h1: 'Fejléc 1',
        h2: 'Fejléc 2',
        h3: 'Fejléc 3',
        h4: 'Fejléc 4',
        h5: 'Fejléc 5',
        h6: 'Fejléc 6'
      },
      lists: {
        unordered: 'Listajeles lista',
        ordered: 'Számozott lista'
      },
      options: {
        help: 'Súgó',
        fullscreen: 'Teljes képernyő',
        codeview: 'Kód nézet'
      },
      paragraph: {
        paragraph: 'Bekezdés',
        outdent: 'Behúzás csökkentése',
        indent: 'Behúzás növelése',
        left: 'Igazítás balra',
        center: 'Igazítás középre',
        right: 'Igazítás jobbra',
        justify: 'Sorkizárt'
      },
      color: {
        recent: 'Jelenlegi szín',
        more: 'További színek',
        background: 'Háttérszín',
        foreground: 'Betűszín',
        transparent: 'Átlátszó',
        setTransparent: 'Átlászóság beállítása',
        reset: 'Visszaállítás',
        resetToDefault: 'Alaphelyzetbe állítás'
      },
      shortcut: {
        shortcuts: 'Gyorsbillentyű',
        close: 'Bezárás',
        textFormatting: 'Szöveg formázása',
        action: 'Művelet',
        paragraphFormatting: 'Bekezdés formázása',
        documentStyle: 'Dokumentumstílus'
      },
      history: {
        undo: 'Visszavonás',
        redo: 'Újra'
      }

    }
  });
})(jQuery);
