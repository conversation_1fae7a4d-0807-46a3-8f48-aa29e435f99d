(function ($) {
  $.extend($.summernote.lang, {
    'vi-VN': {
      font: {
        bold: 'In Đậm',
        italic: 'In Nghiên',
        underline: 'Gạch Dưới',
        clear: 'Bỏ Định Dạng',
        height: '<PERSON><PERSON><PERSON>ng <PERSON>ách <PERSON>àng',
        name: '<PERSON>ông Chữ',
        strikethrough: '<PERSON><PERSON><PERSON> Ng<PERSON>',
        size: 'Cỡ Chữ'
      },
      image: {
        image: 'Hình Ảnh',
        insert: 'Chèn',
        resizeFull: '100%',
        resizeHalf: '50%',
        resizeQuarter: '25%',
        floatLeft: 'Canh Trái',
        floatRight: 'Canh Phải',
        floatNone: 'Canh Đều',
        dragImageHere: 'Thả Ảnh Ở Đây',
        selectFromFiles: 'Chọn Từ Files',
        url: 'URL',
        remove: 'Ghỡ Bỏ'
      },
      link: {
        link: 'Đường Dẫn',
        insert: 'Chèn Đường Dẫn',
        unlink: 'Ghỡ Đường Dẫn',
        edit: 'Sửa',
        textToDisplay: 'Text Hiển Thị',
        url: 'URL',
        openInNewWindow: 'Mở ở Cửa Sổ Mới'
      },
      table: {
        table: 'Bảng'
      },
      hr: {
        insert: 'Chèn <PERSON>'
      },
      style: {
        style: 'Kiểu Chữ',
        normal: 'Chữ Thường',
        blockquote: 'Đoạn Trích',
        pre: 'Mã Code',
        h1: 'H1',
        h2: 'H2',
        h3: 'H3',
        h4: 'H4',
        h5: 'H5',
        h6: 'H6'
      },
      lists: {
        unordered: 'Liệt Kê Danh Sách',
        ordered: 'Liệt Kê Theo Số'
      },
      options: {
        help: 'Trợ Giúp',
        fullscreen: 'Đầy Màn Hình',
        codeview: 'Xem Dạng Code'
      },
      paragraph: {
        paragraph: 'Canh Lề',
        outdent: 'Dịch Sang Trái',
        indent: 'Dịch Sang Phải',
        left: 'Canh Trái',
        center: 'Canh Giữa',
        right: 'Canh Phải',
        justify: 'Canh Đều'
      },
      color: {
        recent: 'Màu Chữ',
        more: 'Mở Rộng',
        background: 'Màu Nền',
        foreground: 'Màu Chữ',
        transparent: 'Trong Suốt',
        setTransparent: 'Nền Trong Suốt',
        reset: 'Thiệt Lập Lại',
        resetToDefault: 'Trở Lại Ban Đầu'
      },
      shortcut: {
        shortcuts: 'Phím Tắt',
        close: 'Đóng',
        textFormatting: 'Định Dạng Văn Bản',
        action: 'Hành Động',
        paragraphFormatting: 'Định Dạng',
        documentStyle: 'Kiểu Văn Bản'
      },
      history: {
        undo: 'Lùi Lại',
        redo: 'Làm Lại'
      }
    }
  });
})(jQuery);
