.note-editor {
  /*! normalize.css v2.1.3 | MIT License | git.io/normalize */

}
.note-editor article,
.note-editor aside,
.note-editor details,
.note-editor figcaption,
.note-editor figure,
.note-editor footer,
.note-editor header,
.note-editor hgroup,
.note-editor main,
.note-editor nav,
.note-editor section,
.note-editor summary {
  display: block;
}
.note-editor audio,
.note-editor canvas,
.note-editor video {
  display: inline-block;
}
.note-editor audio:not([controls]) {
  display: none;
  height: 0;
}
.note-editor [hidden],
.note-editor template {
  display: none;
}
.note-editor html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
.note-editor body {
  margin: 0;
}
.note-editor a {
  background: transparent;
}
.note-editor a:focus {
  outline: thin dotted;
}
.note-editor a:active,
.note-editor a:hover {
  outline: 0;
}
.note-editor h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
.note-editor abbr[title] {
  border-bottom: 1px dotted;
}
.note-editor b,
.note-editor strong {
  font-weight: bold;
}
.note-editor dfn {
  font-style: italic;
}
.note-editor hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}
.note-editor mark {
  background: #ff0;
  color: #000;
}
.note-editor code,
.note-editor kbd,
.note-editor pre,
.note-editor samp {
  font-family: monospace, serif;
  font-size: 1em;
}
.note-editor pre {
  white-space: pre-wrap;
}
.note-editor q {
  quotes: "\201C" "\201D" "\2018" "\2019";
}
.note-editor small {
  font-size: 80%;
}
.note-editor sub,
.note-editor sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
.note-editor sup {
  top: -0.5em;
}
.note-editor sub {
  bottom: -0.25em;
}
.note-editor img {
  border: 0;
}
.note-editor svg:not(:root) {
  overflow: hidden;
}
.note-editor figure {
  margin: 0;
}
.note-editor fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
.note-editor legend {
  border: 0;
  padding: 0;
}
.note-editor button,
.note-editor input,
.note-editor select,
.note-editor textarea {
  font-family: inherit;
  font-size: 100%;
  margin: 0;
}
.note-editor button,
.note-editor input {
  line-height: normal;
}
.note-editor button,
.note-editor select {
  text-transform: none;
}
.note-editor button,
.note-editor html input[type="button"],
.note-editor input[type="reset"],
.note-editor input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
.note-editor button[disabled],
.note-editor html input[disabled] {
  cursor: default;
}
.note-editor input[type="checkbox"],
.note-editor input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
.note-editor input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.note-editor input[type="search"]::-webkit-search-cancel-button,
.note-editor input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
.note-editor button::-moz-focus-inner,
.note-editor input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.note-editor textarea {
  overflow: auto;
  vertical-align: top;
}
.note-editor table {
  border-collapse: collapse;
  border-spacing: 0;
}
@media print {
  .note-editor * {
    text-shadow: none !important;
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  .note-editor a,
  .note-editor a:visited {
    text-decoration: underline;
  }
  .note-editor a[href]:after {
    content: " (" attr(href) ")";
  }
  .note-editor abbr[title]:after {
    content: " (" attr(title) ")";
  }
  .note-editor .ir a:after,
  .note-editor a[href^="javascript:"]:after,
  .note-editor a[href^="#"]:after {
    content: "";
  }
  .note-editor pre,
  .note-editor blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  .note-editor thead {
    display: table-header-group;
  }
  .note-editor tr,
  .note-editor img {
    page-break-inside: avoid;
  }
  .note-editor img {
    max-width: 100% !important;
  }
  @page  {
    margin: 2cm .5cm;
  }
  .note-editor p,
  .note-editor h2,
  .note-editor h3 {
    orphans: 3;
    widows: 3;
  }
  .note-editor h2,
  .note-editor h3 {
    page-break-after: avoid;
  }
  .note-editor .navbar {
    display: none;
  }
  .note-editor .table td,
  .note-editor .table th {
    background-color: #fff !important;
  }
  .note-editor .btn > .caret,
  .note-editor .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .note-editor .label {
    border: 1px solid #000;
  }
  .note-editor .table {
    border-collapse: collapse !important;
  }
  .note-editor .table-bordered th,
  .note-editor .table-bordered td {
    border: 1px solid #ddd !important;
  }
}
.note-editor *,
.note-editor *:before,
.note-editor *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.note-editor html {
  font-size: 62.5%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.note-editor body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.428571429;
  color: #333333;
  background-color: #ffffff;
}
.note-editor input,
.note-editor button,
.note-editor select,
.note-editor textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.note-editor a {
  color: #428bca;
  text-decoration: none;
}
.note-editor a:hover,
.note-editor a:focus {
  color: #2a6496;
  text-decoration: underline;
}
.note-editor a:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.note-editor img {
  vertical-align: middle;
}
.note-editor .img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.note-editor .img-rounded {
  border-radius: 6px;
}
.note-editor .img-thumbnail {
  padding: 4px;
  line-height: 1.428571429;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.note-editor .img-circle {
  border-radius: 50%;
}
.note-editor hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eeeeee;
}
.note-editor .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.note-editor p {
  margin: 0 0 10px;
}
.note-editor .lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 200;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .note-editor .lead {
    font-size: 21px;
  }
}
.note-editor small,
.note-editor .small {
  font-size: 85%;
}
.note-editor cite {
  font-style: normal;
}
.note-editor .text-muted {
  color: #999999;
}
.note-editor .text-primary {
  color: #428bca;
}
.note-editor .text-primary:hover {
  color: #3071a9;
}
.note-editor .text-warning {
  color: #c09853;
}
.note-editor .text-warning:hover {
  color: #a47e3c;
}
.note-editor .text-danger {
  color: #b94a48;
}
.note-editor .text-danger:hover {
  color: #953b39;
}
.note-editor .text-success {
  color: #468847;
}
.note-editor .text-success:hover {
  color: #356635;
}
.note-editor .text-info {
  color: #3a87ad;
}
.note-editor .text-info:hover {
  color: #2d6987;
}
.note-editor .text-left {
  text-align: left;
}
.note-editor .text-right {
  text-align: right;
}
.note-editor .text-center {
  text-align: center;
}
.note-editor h1,
.note-editor h2,
.note-editor h3,
.note-editor h4,
.note-editor h5,
.note-editor h6,
.note-editor .h1,
.note-editor .h2,
.note-editor .h3,
.note-editor .h4,
.note-editor .h5,
.note-editor .h6 {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}
.note-editor h1 small,
.note-editor h2 small,
.note-editor h3 small,
.note-editor h4 small,
.note-editor h5 small,
.note-editor h6 small,
.note-editor .h1 small,
.note-editor .h2 small,
.note-editor .h3 small,
.note-editor .h4 small,
.note-editor .h5 small,
.note-editor .h6 small,
.note-editor h1 .small,
.note-editor h2 .small,
.note-editor h3 .small,
.note-editor h4 .small,
.note-editor h5 .small,
.note-editor h6 .small,
.note-editor .h1 .small,
.note-editor .h2 .small,
.note-editor .h3 .small,
.note-editor .h4 .small,
.note-editor .h5 .small,
.note-editor .h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #999999;
}
.note-editor h1,
.note-editor h2,
.note-editor h3 {
  margin-top: 20px;
  margin-bottom: 10px;
}
.note-editor h1 small,
.note-editor h2 small,
.note-editor h3 small,
.note-editor h1 .small,
.note-editor h2 .small,
.note-editor h3 .small {
  font-size: 65%;
}
.note-editor h4,
.note-editor h5,
.note-editor h6 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.note-editor h4 small,
.note-editor h5 small,
.note-editor h6 small,
.note-editor h4 .small,
.note-editor h5 .small,
.note-editor h6 .small {
  font-size: 75%;
}
.note-editor h1,
.note-editor .h1 {
  font-size: 36px;
}
.note-editor h2,
.note-editor .h2 {
  font-size: 30px;
}
.note-editor h3,
.note-editor .h3 {
  font-size: 24px;
}
.note-editor h4,
.note-editor .h4 {
  font-size: 18px;
}
.note-editor h5,
.note-editor .h5 {
  font-size: 14px;
}
.note-editor h6,
.note-editor .h6 {
  font-size: 12px;
}
.note-editor .page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eeeeee;
}
.note-editor ul,
.note-editor ol {
  margin-top: 0;
  margin-bottom: 10px;
}
.note-editor ul ul,
.note-editor ol ul,
.note-editor ul ol,
.note-editor ol ol {
  margin-bottom: 0;
}
.note-editor .list-unstyled {
  padding-left: 0;
  list-style: none;
}
.note-editor .list-inline {
  padding-left: 0;
  list-style: none;
}
.note-editor .list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
.note-editor dl {
  margin-bottom: 20px;
}
.note-editor dt,
.note-editor dd {
  line-height: 1.428571429;
}
.note-editor dt {
  font-weight: bold;
}
.note-editor dd {
  margin-left: 0;
}
@media (min-width: 768px) {
  .note-editor .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .note-editor .dl-horizontal dd {
    margin-left: 180px;
  }
  .note-editor .dl-horizontal dd:before,
  .note-editor .dl-horizontal dd:after {
    content: " ";
    /* 1 */
  
    display: table;
    /* 2 */
  
  }
  .note-editor .dl-horizontal dd:after {
    clear: both;
  }
  .note-editor .dl-horizontal dd:before,
  .note-editor .dl-horizontal dd:after {
    content: " ";
    /* 1 */
  
    display: table;
    /* 2 */
  
  }
  .note-editor .dl-horizontal dd:after {
    clear: both;
  }
}
.note-editor abbr[title],
.note-editor abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #999999;
}
.note-editor abbr.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
.note-editor blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  border-left: 5px solid #eeeeee;
}
.note-editor blockquote p {
  font-size: 17.5px;
  font-weight: 300;
  line-height: 1.25;
}
.note-editor blockquote p:last-child {
  margin-bottom: 0;
}
.note-editor blockquote small {
  display: block;
  line-height: 1.428571429;
  color: #999999;
}
.note-editor blockquote small:before {
  content: '\2014 \00A0';
}
.note-editor blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
}
.note-editor blockquote.pull-right p,
.note-editor blockquote.pull-right small,
.note-editor blockquote.pull-right .small {
  text-align: right;
}
.note-editor blockquote.pull-right small:before,
.note-editor blockquote.pull-right .small:before {
  content: '';
}
.note-editor blockquote.pull-right small:after,
.note-editor blockquote.pull-right .small:after {
  content: '\00A0 \2014';
}
.note-editor blockquote:before,
.note-editor blockquote:after {
  content: "";
}
.note-editor address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.428571429;
}
.note-editor code,
.note-editor kdb,
.note-editor pre,
.note-editor samp {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
}
.note-editor code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  white-space: nowrap;
  border-radius: 4px;
}
.note-editor pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.428571429;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
.note-editor pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.note-editor .pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.note-editor .container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.note-editor .container:before,
.note-editor .container:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .container:after {
  clear: both;
}
.note-editor .container:before,
.note-editor .container:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .container:after {
  clear: both;
}
.note-editor .row {
  margin-left: -15px;
  margin-right: -15px;
}
.note-editor .row:before,
.note-editor .row:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .row:after {
  clear: both;
}
.note-editor .row:before,
.note-editor .row:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .row:after {
  clear: both;
}
.note-editor .col-xs-1, 
.note-editor .col-sm-1, 
.note-editor .col-md-1, 
.note-editor .col-lg-1, 
.note-editor .col-xs-2, 
.note-editor .col-sm-2, 
.note-editor .col-md-2, 
.note-editor .col-lg-2, 
.note-editor .col-xs-3, 
.note-editor .col-sm-3, 
.note-editor .col-md-3, 
.note-editor .col-lg-3, 
.note-editor .col-xs-4, 
.note-editor .col-sm-4, 
.note-editor .col-md-4, 
.note-editor .col-lg-4, 
.note-editor .col-xs-5, 
.note-editor .col-sm-5, 
.note-editor .col-md-5, 
.note-editor .col-lg-5, 
.note-editor .col-xs-6, 
.note-editor .col-sm-6, 
.note-editor .col-md-6, 
.note-editor .col-lg-6, 
.note-editor .col-xs-7, 
.note-editor .col-sm-7, 
.note-editor .col-md-7, 
.note-editor .col-lg-7, 
.note-editor .col-xs-8, 
.note-editor .col-sm-8, 
.note-editor .col-md-8, 
.note-editor .col-lg-8, 
.note-editor .col-xs-9, 
.note-editor .col-sm-9, 
.note-editor .col-md-9, 
.note-editor .col-lg-9, 
.note-editor .col-xs-10, 
.note-editor .col-sm-10, 
.note-editor .col-md-10, 
.note-editor .col-lg-10, 
.note-editor .col-xs-11, 
.note-editor .col-sm-11, 
.note-editor .col-md-11, 
.note-editor .col-lg-11, 
.note-editor .col-xs-12, 
.note-editor .col-sm-12, 
.note-editor .col-md-12, 
.note-editor .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
}
.note-editor .col-xs-1, 
.note-editor .col-xs-2, 
.note-editor .col-xs-3, 
.note-editor .col-xs-4, 
.note-editor .col-xs-5, 
.note-editor .col-xs-6, 
.note-editor .col-xs-7, 
.note-editor .col-xs-8, 
.note-editor .col-xs-9, 
.note-editor .col-xs-10, 
.note-editor .col-xs-11 {
  float: left;
}
.note-editor .col-xs-12 {
  width: 100%;
}
.note-editor .col-xs-11 {
  width: 91.66666666666666%;
}
.note-editor .col-xs-10 {
  width: 83.33333333333334%;
}
.note-editor .col-xs-9 {
  width: 75%;
}
.note-editor .col-xs-8 {
  width: 66.66666666666666%;
}
.note-editor .col-xs-7 {
  width: 58.333333333333336%;
}
.note-editor .col-xs-6 {
  width: 50%;
}
.note-editor .col-xs-5 {
  width: 41.66666666666667%;
}
.note-editor .col-xs-4 {
  width: 33.33333333333333%;
}
.note-editor .col-xs-3 {
  width: 25%;
}
.note-editor .col-xs-2 {
  width: 16.666666666666664%;
}
.note-editor .col-xs-1 {
  width: 8.333333333333332%;
}
.note-editor .col-xs-pull-12 {
  right: 100%;
}
.note-editor .col-xs-pull-11 {
  right: 91.66666666666666%;
}
.note-editor .col-xs-pull-10 {
  right: 83.33333333333334%;
}
.note-editor .col-xs-pull-9 {
  right: 75%;
}
.note-editor .col-xs-pull-8 {
  right: 66.66666666666666%;
}
.note-editor .col-xs-pull-7 {
  right: 58.333333333333336%;
}
.note-editor .col-xs-pull-6 {
  right: 50%;
}
.note-editor .col-xs-pull-5 {
  right: 41.66666666666667%;
}
.note-editor .col-xs-pull-4 {
  right: 33.33333333333333%;
}
.note-editor .col-xs-pull-3 {
  right: 25%;
}
.note-editor .col-xs-pull-2 {
  right: 16.666666666666664%;
}
.note-editor .col-xs-pull-1 {
  right: 8.333333333333332%;
}
.note-editor .col-xs-push-12 {
  left: 100%;
}
.note-editor .col-xs-push-11 {
  left: 91.66666666666666%;
}
.note-editor .col-xs-push-10 {
  left: 83.33333333333334%;
}
.note-editor .col-xs-push-9 {
  left: 75%;
}
.note-editor .col-xs-push-8 {
  left: 66.66666666666666%;
}
.note-editor .col-xs-push-7 {
  left: 58.333333333333336%;
}
.note-editor .col-xs-push-6 {
  left: 50%;
}
.note-editor .col-xs-push-5 {
  left: 41.66666666666667%;
}
.note-editor .col-xs-push-4 {
  left: 33.33333333333333%;
}
.note-editor .col-xs-push-3 {
  left: 25%;
}
.note-editor .col-xs-push-2 {
  left: 16.666666666666664%;
}
.note-editor .col-xs-push-1 {
  left: 8.333333333333332%;
}
.note-editor .col-xs-offset-12 {
  margin-left: 100%;
}
.note-editor .col-xs-offset-11 {
  margin-left: 91.66666666666666%;
}
.note-editor .col-xs-offset-10 {
  margin-left: 83.33333333333334%;
}
.note-editor .col-xs-offset-9 {
  margin-left: 75%;
}
.note-editor .col-xs-offset-8 {
  margin-left: 66.66666666666666%;
}
.note-editor .col-xs-offset-7 {
  margin-left: 58.333333333333336%;
}
.note-editor .col-xs-offset-6 {
  margin-left: 50%;
}
.note-editor .col-xs-offset-5 {
  margin-left: 41.66666666666667%;
}
.note-editor .col-xs-offset-4 {
  margin-left: 33.33333333333333%;
}
.note-editor .col-xs-offset-3 {
  margin-left: 25%;
}
.note-editor .col-xs-offset-2 {
  margin-left: 16.666666666666664%;
}
.note-editor .col-xs-offset-1 {
  margin-left: 8.333333333333332%;
}
@media (min-width: 768px) {
  .note-editor .container {
    width: 750px;
  }
  .note-editor .col-sm-1, 
  .note-editor .col-sm-2, 
  .note-editor .col-sm-3, 
  .note-editor .col-sm-4, 
  .note-editor .col-sm-5, 
  .note-editor .col-sm-6, 
  .note-editor .col-sm-7, 
  .note-editor .col-sm-8, 
  .note-editor .col-sm-9, 
  .note-editor .col-sm-10, 
  .note-editor .col-sm-11 {
    float: left;
  }
  .note-editor .col-sm-12 {
    width: 100%;
  }
  .note-editor .col-sm-11 {
    width: 91.66666666666666%;
  }
  .note-editor .col-sm-10 {
    width: 83.33333333333334%;
  }
  .note-editor .col-sm-9 {
    width: 75%;
  }
  .note-editor .col-sm-8 {
    width: 66.66666666666666%;
  }
  .note-editor .col-sm-7 {
    width: 58.333333333333336%;
  }
  .note-editor .col-sm-6 {
    width: 50%;
  }
  .note-editor .col-sm-5 {
    width: 41.66666666666667%;
  }
  .note-editor .col-sm-4 {
    width: 33.33333333333333%;
  }
  .note-editor .col-sm-3 {
    width: 25%;
  }
  .note-editor .col-sm-2 {
    width: 16.666666666666664%;
  }
  .note-editor .col-sm-1 {
    width: 8.333333333333332%;
  }
  .note-editor .col-sm-pull-12 {
    right: 100%;
  }
  .note-editor .col-sm-pull-11 {
    right: 91.66666666666666%;
  }
  .note-editor .col-sm-pull-10 {
    right: 83.33333333333334%;
  }
  .note-editor .col-sm-pull-9 {
    right: 75%;
  }
  .note-editor .col-sm-pull-8 {
    right: 66.66666666666666%;
  }
  .note-editor .col-sm-pull-7 {
    right: 58.333333333333336%;
  }
  .note-editor .col-sm-pull-6 {
    right: 50%;
  }
  .note-editor .col-sm-pull-5 {
    right: 41.66666666666667%;
  }
  .note-editor .col-sm-pull-4 {
    right: 33.33333333333333%;
  }
  .note-editor .col-sm-pull-3 {
    right: 25%;
  }
  .note-editor .col-sm-pull-2 {
    right: 16.666666666666664%;
  }
  .note-editor .col-sm-pull-1 {
    right: 8.333333333333332%;
  }
  .note-editor .col-sm-push-12 {
    left: 100%;
  }
  .note-editor .col-sm-push-11 {
    left: 91.66666666666666%;
  }
  .note-editor .col-sm-push-10 {
    left: 83.33333333333334%;
  }
  .note-editor .col-sm-push-9 {
    left: 75%;
  }
  .note-editor .col-sm-push-8 {
    left: 66.66666666666666%;
  }
  .note-editor .col-sm-push-7 {
    left: 58.333333333333336%;
  }
  .note-editor .col-sm-push-6 {
    left: 50%;
  }
  .note-editor .col-sm-push-5 {
    left: 41.66666666666667%;
  }
  .note-editor .col-sm-push-4 {
    left: 33.33333333333333%;
  }
  .note-editor .col-sm-push-3 {
    left: 25%;
  }
  .note-editor .col-sm-push-2 {
    left: 16.666666666666664%;
  }
  .note-editor .col-sm-push-1 {
    left: 8.333333333333332%;
  }
  .note-editor .col-sm-offset-12 {
    margin-left: 100%;
  }
  .note-editor .col-sm-offset-11 {
    margin-left: 91.66666666666666%;
  }
  .note-editor .col-sm-offset-10 {
    margin-left: 83.33333333333334%;
  }
  .note-editor .col-sm-offset-9 {
    margin-left: 75%;
  }
  .note-editor .col-sm-offset-8 {
    margin-left: 66.66666666666666%;
  }
  .note-editor .col-sm-offset-7 {
    margin-left: 58.333333333333336%;
  }
  .note-editor .col-sm-offset-6 {
    margin-left: 50%;
  }
  .note-editor .col-sm-offset-5 {
    margin-left: 41.66666666666667%;
  }
  .note-editor .col-sm-offset-4 {
    margin-left: 33.33333333333333%;
  }
  .note-editor .col-sm-offset-3 {
    margin-left: 25%;
  }
  .note-editor .col-sm-offset-2 {
    margin-left: 16.666666666666664%;
  }
  .note-editor .col-sm-offset-1 {
    margin-left: 8.333333333333332%;
  }
}
@media (min-width: 992px) {
  .note-editor .container {
    width: 970px;
  }
  .note-editor .col-md-1, 
  .note-editor .col-md-2, 
  .note-editor .col-md-3, 
  .note-editor .col-md-4, 
  .note-editor .col-md-5, 
  .note-editor .col-md-6, 
  .note-editor .col-md-7, 
  .note-editor .col-md-8, 
  .note-editor .col-md-9, 
  .note-editor .col-md-10, 
  .note-editor .col-md-11 {
    float: left;
  }
  .note-editor .col-md-12 {
    width: 100%;
  }
  .note-editor .col-md-11 {
    width: 91.66666666666666%;
  }
  .note-editor .col-md-10 {
    width: 83.33333333333334%;
  }
  .note-editor .col-md-9 {
    width: 75%;
  }
  .note-editor .col-md-8 {
    width: 66.66666666666666%;
  }
  .note-editor .col-md-7 {
    width: 58.333333333333336%;
  }
  .note-editor .col-md-6 {
    width: 50%;
  }
  .note-editor .col-md-5 {
    width: 41.66666666666667%;
  }
  .note-editor .col-md-4 {
    width: 33.33333333333333%;
  }
  .note-editor .col-md-3 {
    width: 25%;
  }
  .note-editor .col-md-2 {
    width: 16.666666666666664%;
  }
  .note-editor .col-md-1 {
    width: 8.333333333333332%;
  }
  .note-editor .col-md-pull-12 {
    right: 100%;
  }
  .note-editor .col-md-pull-11 {
    right: 91.66666666666666%;
  }
  .note-editor .col-md-pull-10 {
    right: 83.33333333333334%;
  }
  .note-editor .col-md-pull-9 {
    right: 75%;
  }
  .note-editor .col-md-pull-8 {
    right: 66.66666666666666%;
  }
  .note-editor .col-md-pull-7 {
    right: 58.333333333333336%;
  }
  .note-editor .col-md-pull-6 {
    right: 50%;
  }
  .note-editor .col-md-pull-5 {
    right: 41.66666666666667%;
  }
  .note-editor .col-md-pull-4 {
    right: 33.33333333333333%;
  }
  .note-editor .col-md-pull-3 {
    right: 25%;
  }
  .note-editor .col-md-pull-2 {
    right: 16.666666666666664%;
  }
  .note-editor .col-md-pull-1 {
    right: 8.333333333333332%;
  }
  .note-editor .col-md-push-12 {
    left: 100%;
  }
  .note-editor .col-md-push-11 {
    left: 91.66666666666666%;
  }
  .note-editor .col-md-push-10 {
    left: 83.33333333333334%;
  }
  .note-editor .col-md-push-9 {
    left: 75%;
  }
  .note-editor .col-md-push-8 {
    left: 66.66666666666666%;
  }
  .note-editor .col-md-push-7 {
    left: 58.333333333333336%;
  }
  .note-editor .col-md-push-6 {
    left: 50%;
  }
  .note-editor .col-md-push-5 {
    left: 41.66666666666667%;
  }
  .note-editor .col-md-push-4 {
    left: 33.33333333333333%;
  }
  .note-editor .col-md-push-3 {
    left: 25%;
  }
  .note-editor .col-md-push-2 {
    left: 16.666666666666664%;
  }
  .note-editor .col-md-push-1 {
    left: 8.333333333333332%;
  }
  .note-editor .col-md-offset-12 {
    margin-left: 100%;
  }
  .note-editor .col-md-offset-11 {
    margin-left: 91.66666666666666%;
  }
  .note-editor .col-md-offset-10 {
    margin-left: 83.33333333333334%;
  }
  .note-editor .col-md-offset-9 {
    margin-left: 75%;
  }
  .note-editor .col-md-offset-8 {
    margin-left: 66.66666666666666%;
  }
  .note-editor .col-md-offset-7 {
    margin-left: 58.333333333333336%;
  }
  .note-editor .col-md-offset-6 {
    margin-left: 50%;
  }
  .note-editor .col-md-offset-5 {
    margin-left: 41.66666666666667%;
  }
  .note-editor .col-md-offset-4 {
    margin-left: 33.33333333333333%;
  }
  .note-editor .col-md-offset-3 {
    margin-left: 25%;
  }
  .note-editor .col-md-offset-2 {
    margin-left: 16.666666666666664%;
  }
  .note-editor .col-md-offset-1 {
    margin-left: 8.333333333333332%;
  }
}
@media (min-width: 1200px) {
  .note-editor .container {
    width: 1170px;
  }
  .note-editor .col-lg-1, 
  .note-editor .col-lg-2, 
  .note-editor .col-lg-3, 
  .note-editor .col-lg-4, 
  .note-editor .col-lg-5, 
  .note-editor .col-lg-6, 
  .note-editor .col-lg-7, 
  .note-editor .col-lg-8, 
  .note-editor .col-lg-9, 
  .note-editor .col-lg-10, 
  .note-editor .col-lg-11 {
    float: left;
  }
  .note-editor .col-lg-12 {
    width: 100%;
  }
  .note-editor .col-lg-11 {
    width: 91.66666666666666%;
  }
  .note-editor .col-lg-10 {
    width: 83.33333333333334%;
  }
  .note-editor .col-lg-9 {
    width: 75%;
  }
  .note-editor .col-lg-8 {
    width: 66.66666666666666%;
  }
  .note-editor .col-lg-7 {
    width: 58.333333333333336%;
  }
  .note-editor .col-lg-6 {
    width: 50%;
  }
  .note-editor .col-lg-5 {
    width: 41.66666666666667%;
  }
  .note-editor .col-lg-4 {
    width: 33.33333333333333%;
  }
  .note-editor .col-lg-3 {
    width: 25%;
  }
  .note-editor .col-lg-2 {
    width: 16.666666666666664%;
  }
  .note-editor .col-lg-1 {
    width: 8.333333333333332%;
  }
  .note-editor .col-lg-pull-12 {
    right: 100%;
  }
  .note-editor .col-lg-pull-11 {
    right: 91.66666666666666%;
  }
  .note-editor .col-lg-pull-10 {
    right: 83.33333333333334%;
  }
  .note-editor .col-lg-pull-9 {
    right: 75%;
  }
  .note-editor .col-lg-pull-8 {
    right: 66.66666666666666%;
  }
  .note-editor .col-lg-pull-7 {
    right: 58.333333333333336%;
  }
  .note-editor .col-lg-pull-6 {
    right: 50%;
  }
  .note-editor .col-lg-pull-5 {
    right: 41.66666666666667%;
  }
  .note-editor .col-lg-pull-4 {
    right: 33.33333333333333%;
  }
  .note-editor .col-lg-pull-3 {
    right: 25%;
  }
  .note-editor .col-lg-pull-2 {
    right: 16.666666666666664%;
  }
  .note-editor .col-lg-pull-1 {
    right: 8.333333333333332%;
  }
  .note-editor .col-lg-push-12 {
    left: 100%;
  }
  .note-editor .col-lg-push-11 {
    left: 91.66666666666666%;
  }
  .note-editor .col-lg-push-10 {
    left: 83.33333333333334%;
  }
  .note-editor .col-lg-push-9 {
    left: 75%;
  }
  .note-editor .col-lg-push-8 {
    left: 66.66666666666666%;
  }
  .note-editor .col-lg-push-7 {
    left: 58.333333333333336%;
  }
  .note-editor .col-lg-push-6 {
    left: 50%;
  }
  .note-editor .col-lg-push-5 {
    left: 41.66666666666667%;
  }
  .note-editor .col-lg-push-4 {
    left: 33.33333333333333%;
  }
  .note-editor .col-lg-push-3 {
    left: 25%;
  }
  .note-editor .col-lg-push-2 {
    left: 16.666666666666664%;
  }
  .note-editor .col-lg-push-1 {
    left: 8.333333333333332%;
  }
  .note-editor .col-lg-offset-12 {
    margin-left: 100%;
  }
  .note-editor .col-lg-offset-11 {
    margin-left: 91.66666666666666%;
  }
  .note-editor .col-lg-offset-10 {
    margin-left: 83.33333333333334%;
  }
  .note-editor .col-lg-offset-9 {
    margin-left: 75%;
  }
  .note-editor .col-lg-offset-8 {
    margin-left: 66.66666666666666%;
  }
  .note-editor .col-lg-offset-7 {
    margin-left: 58.333333333333336%;
  }
  .note-editor .col-lg-offset-6 {
    margin-left: 50%;
  }
  .note-editor .col-lg-offset-5 {
    margin-left: 41.66666666666667%;
  }
  .note-editor .col-lg-offset-4 {
    margin-left: 33.33333333333333%;
  }
  .note-editor .col-lg-offset-3 {
    margin-left: 25%;
  }
  .note-editor .col-lg-offset-2 {
    margin-left: 16.666666666666664%;
  }
  .note-editor .col-lg-offset-1 {
    margin-left: 8.333333333333332%;
  }
}
.note-editor table {
  max-width: 100%;
  background-color: transparent;
}
.note-editor th {
  text-align: left;
}
.note-editor .table {
  width: 100%;
  margin-bottom: 20px;
}
.note-editor .table > thead > tr > th,
.note-editor .table > tbody > tr > th,
.note-editor .table > tfoot > tr > th,
.note-editor .table > thead > tr > td,
.note-editor .table > tbody > tr > td,
.note-editor .table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.428571429;
  vertical-align: top;
  border-top: 1px solid #dddddd;
}
.note-editor .table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #dddddd;
}
.note-editor .table > caption + thead > tr:first-child > th,
.note-editor .table > colgroup + thead > tr:first-child > th,
.note-editor .table > thead:first-child > tr:first-child > th,
.note-editor .table > caption + thead > tr:first-child > td,
.note-editor .table > colgroup + thead > tr:first-child > td,
.note-editor .table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.note-editor .table > tbody + tbody {
  border-top: 2px solid #dddddd;
}
.note-editor .table .table {
  background-color: #ffffff;
}
.note-editor .table-condensed > thead > tr > th,
.note-editor .table-condensed > tbody > tr > th,
.note-editor .table-condensed > tfoot > tr > th,
.note-editor .table-condensed > thead > tr > td,
.note-editor .table-condensed > tbody > tr > td,
.note-editor .table-condensed > tfoot > tr > td {
  padding: 5px;
}
.note-editor .table-bordered {
  border: 1px solid #dddddd;
}
.note-editor .table-bordered > thead > tr > th,
.note-editor .table-bordered > tbody > tr > th,
.note-editor .table-bordered > tfoot > tr > th,
.note-editor .table-bordered > thead > tr > td,
.note-editor .table-bordered > tbody > tr > td,
.note-editor .table-bordered > tfoot > tr > td {
  border: 1px solid #dddddd;
}
.note-editor .table-bordered > thead > tr > th,
.note-editor .table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.note-editor .table-striped > tbody > tr:nth-child(odd) > td,
.note-editor .table-striped > tbody > tr:nth-child(odd) > th {
  background-color: #f9f9f9;
}
.note-editor .table-hover > tbody > tr:hover > td,
.note-editor .table-hover > tbody > tr:hover > th {
  background-color: #f5f5f5;
}
.note-editor table col[class*="col-"] {
  float: none;
  display: table-column;
}
.note-editor table td[class*="col-"],
.note-editor table th[class*="col-"] {
  float: none;
  display: table-cell;
}
.note-editor .table > thead > tr > td.active,
.note-editor .table > tbody > tr > td.active,
.note-editor .table > tfoot > tr > td.active,
.note-editor .table > thead > tr > th.active,
.note-editor .table > tbody > tr > th.active,
.note-editor .table > tfoot > tr > th.active,
.note-editor .table > thead > tr.active > td,
.note-editor .table > tbody > tr.active > td,
.note-editor .table > tfoot > tr.active > td,
.note-editor .table > thead > tr.active > th,
.note-editor .table > tbody > tr.active > th,
.note-editor .table > tfoot > tr.active > th {
  background-color: #f5f5f5;
}
.note-editor .table > thead > tr > td.success,
.note-editor .table > tbody > tr > td.success,
.note-editor .table > tfoot > tr > td.success,
.note-editor .table > thead > tr > th.success,
.note-editor .table > tbody > tr > th.success,
.note-editor .table > tfoot > tr > th.success,
.note-editor .table > thead > tr.success > td,
.note-editor .table > tbody > tr.success > td,
.note-editor .table > tfoot > tr.success > td,
.note-editor .table > thead > tr.success > th,
.note-editor .table > tbody > tr.success > th,
.note-editor .table > tfoot > tr.success > th {
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.note-editor .table-hover > tbody > tr > td.success:hover,
.note-editor .table-hover > tbody > tr > th.success:hover,
.note-editor .table-hover > tbody > tr.success:hover > td,
.note-editor .table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6;
  border-color: #c9e2b3;
}
.note-editor .table > thead > tr > td.danger,
.note-editor .table > tbody > tr > td.danger,
.note-editor .table > tfoot > tr > td.danger,
.note-editor .table > thead > tr > th.danger,
.note-editor .table > tbody > tr > th.danger,
.note-editor .table > tfoot > tr > th.danger,
.note-editor .table > thead > tr.danger > td,
.note-editor .table > tbody > tr.danger > td,
.note-editor .table > tfoot > tr.danger > td,
.note-editor .table > thead > tr.danger > th,
.note-editor .table > tbody > tr.danger > th,
.note-editor .table > tfoot > tr.danger > th {
  background-color: #f2dede;
  border-color: #ebccd1;
}
.note-editor .table-hover > tbody > tr > td.danger:hover,
.note-editor .table-hover > tbody > tr > th.danger:hover,
.note-editor .table-hover > tbody > tr.danger:hover > td,
.note-editor .table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc;
  border-color: #e4b9c0;
}
.note-editor .table > thead > tr > td.warning,
.note-editor .table > tbody > tr > td.warning,
.note-editor .table > tfoot > tr > td.warning,
.note-editor .table > thead > tr > th.warning,
.note-editor .table > tbody > tr > th.warning,
.note-editor .table > tfoot > tr > th.warning,
.note-editor .table > thead > tr.warning > td,
.note-editor .table > tbody > tr.warning > td,
.note-editor .table > tfoot > tr.warning > td,
.note-editor .table > thead > tr.warning > th,
.note-editor .table > tbody > tr.warning > th,
.note-editor .table > tfoot > tr.warning > th {
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.note-editor .table-hover > tbody > tr > td.warning:hover,
.note-editor .table-hover > tbody > tr > th.warning:hover,
.note-editor .table-hover > tbody > tr.warning:hover > td,
.note-editor .table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc;
  border-color: #f7e1b5;
}
@media (max-width: 767px) {
  .note-editor .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    overflow-x: scroll;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #dddddd;
    -webkit-overflow-scrolling: touch;
  }
  .note-editor .table-responsive > .table {
    margin-bottom: 0;
  }
  .note-editor .table-responsive > .table > thead > tr > th,
  .note-editor .table-responsive > .table > tbody > tr > th,
  .note-editor .table-responsive > .table > tfoot > tr > th,
  .note-editor .table-responsive > .table > thead > tr > td,
  .note-editor .table-responsive > .table > tbody > tr > td,
  .note-editor .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .note-editor .table-responsive > .table-bordered {
    border: 0;
  }
  .note-editor .table-responsive > .table-bordered > thead > tr > th:first-child,
  .note-editor .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .note-editor .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .note-editor .table-responsive > .table-bordered > thead > tr > td:first-child,
  .note-editor .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .note-editor .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .note-editor .table-responsive > .table-bordered > thead > tr > th:last-child,
  .note-editor .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .note-editor .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .note-editor .table-responsive > .table-bordered > thead > tr > td:last-child,
  .note-editor .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .note-editor .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .note-editor .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .note-editor .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .note-editor .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .note-editor .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
.note-editor fieldset {
  padding: 0;
  margin: 0;
  border: 0;
}
.note-editor legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
.note-editor label {
  display: inline-block;
  margin-bottom: 5px;
  font-weight: bold;
}
.note-editor input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.note-editor input[type="radio"],
.note-editor input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  /* IE8-9 */

  line-height: normal;
}
.note-editor input[type="file"] {
  display: block;
}
.note-editor select[multiple],
.note-editor select[size] {
  height: auto;
}
.note-editor select optgroup {
  font-size: inherit;
  font-style: inherit;
  font-family: inherit;
}
.note-editor input[type="file"]:focus,
.note-editor input[type="radio"]:focus,
.note-editor input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.note-editor input[type="number"]::-webkit-outer-spin-button,
.note-editor input[type="number"]::-webkit-inner-spin-button {
  height: auto;
}
.note-editor output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555555;
  vertical-align: middle;
}
.note-editor .form-control:-moz-placeholder {
  color: #999999;
}
.note-editor .form-control::-moz-placeholder {
  color: #999999;
}
.note-editor .form-control:-ms-input-placeholder {
  color: #999999;
}
.note-editor .form-control::-webkit-input-placeholder {
  color: #999999;
}
.note-editor .form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555555;
  vertical-align: middle;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #cccccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.note-editor .form-control:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.note-editor .form-control[disabled],
.note-editor .form-control[readonly],
fieldset[disabled] .note-editor .form-control {
  cursor: not-allowed;
  background-color: #eeeeee;
}
textarea.note-editor .form-control {
  height: auto;
}
.note-editor .form-group {
  margin-bottom: 15px;
}
.note-editor .radio,
.note-editor .checkbox {
  display: block;
  min-height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 20px;
  vertical-align: middle;
}
.note-editor .radio label,
.note-editor .checkbox label {
  display: inline;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.note-editor .radio input[type="radio"],
.note-editor .radio-inline input[type="radio"],
.note-editor .checkbox input[type="checkbox"],
.note-editor .checkbox-inline input[type="checkbox"] {
  float: left;
  margin-left: -20px;
}
.note-editor .radio + .radio,
.note-editor .checkbox + .checkbox {
  margin-top: -5px;
}
.note-editor .radio-inline,
.note-editor .checkbox-inline {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.note-editor .radio-inline + .radio-inline,
.note-editor .checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
.note-editor input[type="radio"][disabled],
.note-editor input[type="checkbox"][disabled],
.note-editor .radio[disabled],
.note-editor .radio-inline[disabled],
.note-editor .checkbox[disabled],
.note-editor .checkbox-inline[disabled],
fieldset[disabled] .note-editor input[type="radio"],
fieldset[disabled] .note-editor input[type="checkbox"],
fieldset[disabled] .note-editor .radio,
fieldset[disabled] .note-editor .radio-inline,
fieldset[disabled] .note-editor .checkbox,
fieldset[disabled] .note-editor .checkbox-inline {
  cursor: not-allowed;
}
.note-editor .input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.note-editor .input-sm {
  height: 30px;
  line-height: 30px;
}
textarea.note-editor .input-sm {
  height: auto;
}
.note-editor .input-lg {
  height: 45px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 6px;
}
select.note-editor .input-lg {
  height: 45px;
  line-height: 45px;
}
textarea.note-editor .input-lg {
  height: auto;
}
.note-editor .has-warning .help-block,
.note-editor .has-warning .control-label {
  color: #c09853;
}
.note-editor .has-warning .form-control {
  border-color: #c09853;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.note-editor .has-warning .form-control:focus {
  border-color: #a47e3c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
}
.note-editor .has-warning .input-group-addon {
  color: #c09853;
  border-color: #c09853;
  background-color: #fcf8e3;
}
.note-editor .has-error .help-block,
.note-editor .has-error .control-label {
  color: #b94a48;
}
.note-editor .has-error .form-control {
  border-color: #b94a48;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.note-editor .has-error .form-control:focus {
  border-color: #953b39;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
}
.note-editor .has-error .input-group-addon {
  color: #b94a48;
  border-color: #b94a48;
  background-color: #f2dede;
}
.note-editor .has-success .help-block,
.note-editor .has-success .control-label {
  color: #468847;
}
.note-editor .has-success .form-control {
  border-color: #468847;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.note-editor .has-success .form-control:focus {
  border-color: #356635;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
}
.note-editor .has-success .input-group-addon {
  color: #468847;
  border-color: #468847;
  background-color: #dff0d8;
}
.note-editor .form-control-static {
  margin-bottom: 0;
}
.note-editor .help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373;
}
@media (min-width: 768px) {
  .note-editor .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .note-editor .form-inline .form-control {
    display: inline-block;
  }
  .note-editor .form-inline .radio,
  .note-editor .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
  }
  .note-editor .form-inline .radio input[type="radio"],
  .note-editor .form-inline .checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0;
  }
}
.note-editor .form-horizontal .control-label,
.note-editor .form-horizontal .radio,
.note-editor .form-horizontal .checkbox,
.note-editor .form-horizontal .radio-inline,
.note-editor .form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
}
.note-editor .form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px;
}
.note-editor .form-horizontal .form-group:before,
.note-editor .form-horizontal .form-group:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .form-horizontal .form-group:after {
  clear: both;
}
.note-editor .form-horizontal .form-group:before,
.note-editor .form-horizontal .form-group:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .form-horizontal .form-group:after {
  clear: both;
}
.note-editor .form-horizontal .form-control-static {
  padding-top: 7px;
}
@media (min-width: 768px) {
  .note-editor .form-horizontal .control-label {
    text-align: right;
  }
}
.note-editor .btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.note-editor .btn:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.note-editor .btn:hover,
.note-editor .btn:focus {
  color: #333333;
  text-decoration: none;
}
.note-editor .btn:active,
.note-editor .btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.note-editor .btn.disabled,
.note-editor .btn[disabled],
fieldset[disabled] .note-editor .btn {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.note-editor .btn-default {
  color: #333333;
  background-color: #ffffff;
  border-color: #cccccc;
}
.note-editor .btn-default:hover,
.note-editor .btn-default:focus,
.note-editor .btn-default:active,
.note-editor .btn-default.active,
.open .dropdown-toggle.note-editor .btn-default {
  color: #333333;
  background-color: #ebebeb;
  border-color: #adadad;
}
.note-editor .btn-default:active,
.note-editor .btn-default.active,
.open .dropdown-toggle.note-editor .btn-default {
  background-image: none;
}
.note-editor .btn-default.disabled,
.note-editor .btn-default[disabled],
fieldset[disabled] .note-editor .btn-default,
.note-editor .btn-default.disabled:hover,
.note-editor .btn-default[disabled]:hover,
fieldset[disabled] .note-editor .btn-default:hover,
.note-editor .btn-default.disabled:focus,
.note-editor .btn-default[disabled]:focus,
fieldset[disabled] .note-editor .btn-default:focus,
.note-editor .btn-default.disabled:active,
.note-editor .btn-default[disabled]:active,
fieldset[disabled] .note-editor .btn-default:active,
.note-editor .btn-default.disabled.active,
.note-editor .btn-default[disabled].active,
fieldset[disabled] .note-editor .btn-default.active {
  background-color: #ffffff;
  border-color: #cccccc;
}
.note-editor .btn-primary {
  color: #ffffff;
  background-color: #428bca;
  border-color: #357ebd;
}
.note-editor .btn-primary:hover,
.note-editor .btn-primary:focus,
.note-editor .btn-primary:active,
.note-editor .btn-primary.active,
.open .dropdown-toggle.note-editor .btn-primary {
  color: #ffffff;
  background-color: #3276b1;
  border-color: #285e8e;
}
.note-editor .btn-primary:active,
.note-editor .btn-primary.active,
.open .dropdown-toggle.note-editor .btn-primary {
  background-image: none;
}
.note-editor .btn-primary.disabled,
.note-editor .btn-primary[disabled],
fieldset[disabled] .note-editor .btn-primary,
.note-editor .btn-primary.disabled:hover,
.note-editor .btn-primary[disabled]:hover,
fieldset[disabled] .note-editor .btn-primary:hover,
.note-editor .btn-primary.disabled:focus,
.note-editor .btn-primary[disabled]:focus,
fieldset[disabled] .note-editor .btn-primary:focus,
.note-editor .btn-primary.disabled:active,
.note-editor .btn-primary[disabled]:active,
fieldset[disabled] .note-editor .btn-primary:active,
.note-editor .btn-primary.disabled.active,
.note-editor .btn-primary[disabled].active,
fieldset[disabled] .note-editor .btn-primary.active {
  background-color: #428bca;
  border-color: #357ebd;
}
.note-editor .btn-warning {
  color: #ffffff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.note-editor .btn-warning:hover,
.note-editor .btn-warning:focus,
.note-editor .btn-warning:active,
.note-editor .btn-warning.active,
.open .dropdown-toggle.note-editor .btn-warning {
  color: #ffffff;
  background-color: #ed9c28;
  border-color: #d58512;
}
.note-editor .btn-warning:active,
.note-editor .btn-warning.active,
.open .dropdown-toggle.note-editor .btn-warning {
  background-image: none;
}
.note-editor .btn-warning.disabled,
.note-editor .btn-warning[disabled],
fieldset[disabled] .note-editor .btn-warning,
.note-editor .btn-warning.disabled:hover,
.note-editor .btn-warning[disabled]:hover,
fieldset[disabled] .note-editor .btn-warning:hover,
.note-editor .btn-warning.disabled:focus,
.note-editor .btn-warning[disabled]:focus,
fieldset[disabled] .note-editor .btn-warning:focus,
.note-editor .btn-warning.disabled:active,
.note-editor .btn-warning[disabled]:active,
fieldset[disabled] .note-editor .btn-warning:active,
.note-editor .btn-warning.disabled.active,
.note-editor .btn-warning[disabled].active,
fieldset[disabled] .note-editor .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.note-editor .btn-danger {
  color: #ffffff;
  background-color: #d9534f;
  border-color: #d43f3a;
}
.note-editor .btn-danger:hover,
.note-editor .btn-danger:focus,
.note-editor .btn-danger:active,
.note-editor .btn-danger.active,
.open .dropdown-toggle.note-editor .btn-danger {
  color: #ffffff;
  background-color: #d2322d;
  border-color: #ac2925;
}
.note-editor .btn-danger:active,
.note-editor .btn-danger.active,
.open .dropdown-toggle.note-editor .btn-danger {
  background-image: none;
}
.note-editor .btn-danger.disabled,
.note-editor .btn-danger[disabled],
fieldset[disabled] .note-editor .btn-danger,
.note-editor .btn-danger.disabled:hover,
.note-editor .btn-danger[disabled]:hover,
fieldset[disabled] .note-editor .btn-danger:hover,
.note-editor .btn-danger.disabled:focus,
.note-editor .btn-danger[disabled]:focus,
fieldset[disabled] .note-editor .btn-danger:focus,
.note-editor .btn-danger.disabled:active,
.note-editor .btn-danger[disabled]:active,
fieldset[disabled] .note-editor .btn-danger:active,
.note-editor .btn-danger.disabled.active,
.note-editor .btn-danger[disabled].active,
fieldset[disabled] .note-editor .btn-danger.active {
  background-color: #d9534f;
  border-color: #d43f3a;
}
.note-editor .btn-success {
  color: #ffffff;
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.note-editor .btn-success:hover,
.note-editor .btn-success:focus,
.note-editor .btn-success:active,
.note-editor .btn-success.active,
.open .dropdown-toggle.note-editor .btn-success {
  color: #ffffff;
  background-color: #47a447;
  border-color: #398439;
}
.note-editor .btn-success:active,
.note-editor .btn-success.active,
.open .dropdown-toggle.note-editor .btn-success {
  background-image: none;
}
.note-editor .btn-success.disabled,
.note-editor .btn-success[disabled],
fieldset[disabled] .note-editor .btn-success,
.note-editor .btn-success.disabled:hover,
.note-editor .btn-success[disabled]:hover,
fieldset[disabled] .note-editor .btn-success:hover,
.note-editor .btn-success.disabled:focus,
.note-editor .btn-success[disabled]:focus,
fieldset[disabled] .note-editor .btn-success:focus,
.note-editor .btn-success.disabled:active,
.note-editor .btn-success[disabled]:active,
fieldset[disabled] .note-editor .btn-success:active,
.note-editor .btn-success.disabled.active,
.note-editor .btn-success[disabled].active,
fieldset[disabled] .note-editor .btn-success.active {
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.note-editor .btn-info {
  color: #ffffff;
  background-color: #5bc0de;
  border-color: #46b8da;
}
.note-editor .btn-info:hover,
.note-editor .btn-info:focus,
.note-editor .btn-info:active,
.note-editor .btn-info.active,
.open .dropdown-toggle.note-editor .btn-info {
  color: #ffffff;
  background-color: #39b3d7;
  border-color: #269abc;
}
.note-editor .btn-info:active,
.note-editor .btn-info.active,
.open .dropdown-toggle.note-editor .btn-info {
  background-image: none;
}
.note-editor .btn-info.disabled,
.note-editor .btn-info[disabled],
fieldset[disabled] .note-editor .btn-info,
.note-editor .btn-info.disabled:hover,
.note-editor .btn-info[disabled]:hover,
fieldset[disabled] .note-editor .btn-info:hover,
.note-editor .btn-info.disabled:focus,
.note-editor .btn-info[disabled]:focus,
fieldset[disabled] .note-editor .btn-info:focus,
.note-editor .btn-info.disabled:active,
.note-editor .btn-info[disabled]:active,
fieldset[disabled] .note-editor .btn-info:active,
.note-editor .btn-info.disabled.active,
.note-editor .btn-info[disabled].active,
fieldset[disabled] .note-editor .btn-info.active {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.note-editor .btn-link {
  color: #428bca;
  font-weight: normal;
  cursor: pointer;
  border-radius: 0;
}
.note-editor .btn-link,
.note-editor .btn-link:active,
.note-editor .btn-link[disabled],
fieldset[disabled] .note-editor .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.note-editor .btn-link,
.note-editor .btn-link:hover,
.note-editor .btn-link:focus,
.note-editor .btn-link:active {
  border-color: transparent;
}
.note-editor .btn-link:hover,
.note-editor .btn-link:focus {
  color: #2a6496;
  text-decoration: underline;
  background-color: transparent;
}
.note-editor .btn-link[disabled]:hover,
fieldset[disabled] .note-editor .btn-link:hover,
.note-editor .btn-link[disabled]:focus,
fieldset[disabled] .note-editor .btn-link:focus {
  color: #999999;
  text-decoration: none;
}
.note-editor .btn-lg {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 6px;
}
.note-editor .btn-sm,
.note-editor .btn-xs {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.note-editor .btn-xs {
  padding: 1px 5px;
}
.note-editor .btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.note-editor .btn-block + .btn-block {
  margin-top: 5px;
}
.note-editor input[type="submit"].btn-block,
.note-editor input[type="reset"].btn-block,
.note-editor input[type="button"].btn-block {
  width: 100%;
}
.note-editor .fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.note-editor .fade.in {
  opacity: 1;
}
.note-editor .collapse {
  display: none;
}
.note-editor .collapse.in {
  display: block;
}
.note-editor .collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  transition: height 0.35s ease;
}
.note-editor .caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px solid #000000;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  border-bottom: 0 dotted;
}
.note-editor .dropdown {
  position: relative;
}
.note-editor .dropdown-toggle:focus {
  outline: 0;
}
.note-editor .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.note-editor .dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.note-editor .dropdown-menu .divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.note-editor .dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.428571429;
  color: #333333;
  white-space: nowrap;
}
.note-editor .dropdown-menu > li > a:hover,
.note-editor .dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.note-editor .dropdown-menu > .active > a,
.note-editor .dropdown-menu > .active > a:hover,
.note-editor .dropdown-menu > .active > a:focus {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  background-color: #428bca;
}
.note-editor .dropdown-menu > .disabled > a,
.note-editor .dropdown-menu > .disabled > a:hover,
.note-editor .dropdown-menu > .disabled > a:focus {
  color: #999999;
}
.note-editor .dropdown-menu > .disabled > a:hover,
.note-editor .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.note-editor .open > .dropdown-menu {
  display: block;
}
.note-editor .open > a {
  outline: 0;
}
.note-editor .dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #999999;
}
.note-editor .dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.note-editor .pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.note-editor .dropup .caret,
.note-editor .navbar-fixed-bottom .dropdown .caret {
  border-top: 0 dotted;
  border-bottom: 4px solid #000000;
  content: "";
}
.note-editor .dropup .dropdown-menu,
.note-editor .navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 1px;
}
@media (min-width: 768px) {
  .note-editor .navbar-right .dropdown-menu {
    right: 0;
    left: auto;
  }
}
.btn-default .note-editor .caret {
  border-top-color: #333333;
}
.btn-primary .note-editor .caret,
.btn-success .note-editor .caret,
.btn-warning .note-editor .caret,
.btn-danger .note-editor .caret,
.btn-info .note-editor .caret {
  border-top-color: #fff;
}
.note-editor .dropup .btn-default .caret {
  border-bottom-color: #333333;
}
.note-editor .dropup .btn-primary .caret,
.note-editor .dropup .btn-success .caret,
.note-editor .dropup .btn-warning .caret,
.note-editor .dropup .btn-danger .caret,
.note-editor .dropup .btn-info .caret {
  border-bottom-color: #fff;
}
.note-editor .btn-group,
.note-editor .btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.note-editor .btn-group > .btn,
.note-editor .btn-group-vertical > .btn {
  position: relative;
  float: left;
}
.note-editor .btn-group > .btn:hover,
.note-editor .btn-group-vertical > .btn:hover,
.note-editor .btn-group > .btn:focus,
.note-editor .btn-group-vertical > .btn:focus,
.note-editor .btn-group > .btn:active,
.note-editor .btn-group-vertical > .btn:active,
.note-editor .btn-group > .btn.active,
.note-editor .btn-group-vertical > .btn.active {
  z-index: 2;
}
.note-editor .btn-group > .btn:focus,
.note-editor .btn-group-vertical > .btn:focus {
  outline: none;
}
.note-editor .btn-group .btn + .btn,
.note-editor .btn-group .btn + .btn-group,
.note-editor .btn-group .btn-group + .btn,
.note-editor .btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.note-editor .btn-toolbar:before,
.note-editor .btn-toolbar:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .btn-toolbar:after {
  clear: both;
}
.note-editor .btn-toolbar:before,
.note-editor .btn-toolbar:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .btn-toolbar:after {
  clear: both;
}
.note-editor .btn-toolbar .btn-group {
  float: left;
}
.note-editor .btn-toolbar > .btn + .btn,
.note-editor .btn-toolbar > .btn-group + .btn,
.note-editor .btn-toolbar > .btn + .btn-group,
.note-editor .btn-toolbar > .btn-group + .btn-group {
  margin-left: 5px;
}
.note-editor .btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.note-editor .btn-group > .btn:first-child {
  margin-left: 0;
}
.note-editor .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.note-editor .btn-group > .btn:last-child:not(:first-child),
.note-editor .btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .btn-group > .btn-group {
  float: left;
}
.note-editor .btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.note-editor .btn-group > .btn-group:first-child > .btn:last-child,
.note-editor .btn-group > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.note-editor .btn-group > .btn-group:last-child > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .btn-group .dropdown-toggle:active,
.note-editor .btn-group.open .dropdown-toggle {
  outline: 0;
}
.note-editor .btn-group-xs > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
  padding: 1px 5px;
}
.note-editor .btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.note-editor .btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 6px;
}
.note-editor .btn-group > .btn + .dropdown-toggle {
  padding-left: 5px;
  padding-right: 5px;
}
.note-editor .btn-group > .btn-lg + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
}
.note-editor .btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.note-editor .btn .caret {
  margin-left: 0;
}
.note-editor .btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.note-editor .dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.note-editor .btn-group-vertical > .btn,
.note-editor .btn-group-vertical > .btn-group {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.note-editor .btn-group-vertical > .btn-group:before,
.note-editor .btn-group-vertical > .btn-group:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .btn-group-vertical > .btn-group:after {
  clear: both;
}
.note-editor .btn-group-vertical > .btn-group:before,
.note-editor .btn-group-vertical > .btn-group:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .btn-group-vertical > .btn-group:after {
  clear: both;
}
.note-editor .btn-group-vertical > .btn-group > .btn {
  float: none;
}
.note-editor .btn-group-vertical > .btn + .btn,
.note-editor .btn-group-vertical > .btn + .btn-group,
.note-editor .btn-group-vertical > .btn-group + .btn,
.note-editor .btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.note-editor .btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.note-editor .btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.note-editor .btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.note-editor .btn-group-vertical > .btn-group:first-child > .btn:last-child,
.note-editor .btn-group-vertical > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.note-editor .btn-group-vertical > .btn-group:last-child > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.note-editor .btn-group-justified .btn {
  float: none;
  display: table-cell;
  width: 1%;
}
.note-editor [data-toggle="buttons"] > .btn > input[type="radio"],
.note-editor [data-toggle="buttons"] > .btn > input[type="checkbox"] {
  display: none;
}
.note-editor .input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.note-editor .input-group.col {
  float: none;
  padding-left: 0;
  padding-right: 0;
}
.note-editor .input-group .form-control {
  width: 100%;
  margin-bottom: 0;
}
.note-editor .input-group-lg > .form-control,
.note-editor .input-group-lg > .input-group-addon,
.note-editor .input-group-lg > .input-group-btn > .btn {
  height: 45px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 6px;
}
select.note-editor .input-group-lg > .form-control,
select.note-editor .input-group-lg > .input-group-addon,
select.note-editor .input-group-lg > .input-group-btn > .btn {
  height: 45px;
  line-height: 45px;
}
textarea.note-editor .input-group-lg > .form-control,
textarea.note-editor .input-group-lg > .input-group-addon,
textarea.note-editor .input-group-lg > .input-group-btn > .btn {
  height: auto;
}
.note-editor .input-group-sm > .form-control,
.note-editor .input-group-sm > .input-group-addon,
.note-editor .input-group-sm > .input-group-btn > .btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.note-editor .input-group-sm > .form-control,
select.note-editor .input-group-sm > .input-group-addon,
select.note-editor .input-group-sm > .input-group-btn > .btn {
  height: 30px;
  line-height: 30px;
}
textarea.note-editor .input-group-sm > .form-control,
textarea.note-editor .input-group-sm > .input-group-addon,
textarea.note-editor .input-group-sm > .input-group-btn > .btn {
  height: auto;
}
.note-editor .input-group-addon,
.note-editor .input-group-btn,
.note-editor .input-group .form-control {
  display: table-cell;
}
.note-editor .input-group-addon:not(:first-child):not(:last-child),
.note-editor .input-group-btn:not(:first-child):not(:last-child),
.note-editor .input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.note-editor .input-group-addon,
.note-editor .input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.note-editor .input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #555555;
  text-align: center;
  background-color: #eeeeee;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
.note-editor .input-group-addon.input-sm {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 3px;
}
.note-editor .input-group-addon.input-lg {
  padding: 10px 16px;
  font-size: 18px;
  border-radius: 6px;
}
.note-editor .input-group-addon input[type="radio"],
.note-editor .input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.note-editor .input-group .form-control:first-child,
.note-editor .input-group-addon:first-child,
.note-editor .input-group-btn:first-child > .btn,
.note-editor .input-group-btn:first-child > .dropdown-toggle,
.note-editor .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.note-editor .input-group-addon:first-child {
  border-right: 0;
}
.note-editor .input-group .form-control:last-child,
.note-editor .input-group-addon:last-child,
.note-editor .input-group-btn:last-child > .btn,
.note-editor .input-group-btn:last-child > .dropdown-toggle,
.note-editor .input-group-btn:first-child > .btn:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .input-group-addon:last-child {
  border-left: 0;
}
.note-editor .input-group-btn {
  position: relative;
  white-space: nowrap;
}
.note-editor .input-group-btn:first-child > .btn {
  margin-right: -1px;
}
.note-editor .input-group-btn:last-child > .btn {
  margin-left: -1px;
}
.note-editor .input-group-btn > .btn {
  position: relative;
}
.note-editor .input-group-btn > .btn + .btn {
  margin-left: -4px;
}
.note-editor .input-group-btn > .btn:hover,
.note-editor .input-group-btn > .btn:active {
  z-index: 2;
}
.note-editor .nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.note-editor .nav:before,
.note-editor .nav:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .nav:after {
  clear: both;
}
.note-editor .nav:before,
.note-editor .nav:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .nav:after {
  clear: both;
}
.note-editor .nav > li {
  position: relative;
  display: block;
}
.note-editor .nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.note-editor .nav > li > a:hover,
.note-editor .nav > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.note-editor .nav > li.disabled > a {
  color: #999999;
}
.note-editor .nav > li.disabled > a:hover,
.note-editor .nav > li.disabled > a:focus {
  color: #999999;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.note-editor .nav .open > a,
.note-editor .nav .open > a:hover,
.note-editor .nav .open > a:focus {
  background-color: #eeeeee;
  border-color: #428bca;
}
.note-editor .nav .open > a .caret,
.note-editor .nav .open > a:hover .caret,
.note-editor .nav .open > a:focus .caret {
  border-top-color: #2a6496;
  border-bottom-color: #2a6496;
}
.note-editor .nav .nav-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.note-editor .nav > li > a > img {
  max-width: none;
}
.note-editor .nav-tabs {
  border-bottom: 1px solid #dddddd;
}
.note-editor .nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.note-editor .nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.428571429;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.note-editor .nav-tabs > li > a:hover {
  border-color: #eeeeee #eeeeee #dddddd;
}
.note-editor .nav-tabs > li.active > a,
.note-editor .nav-tabs > li.active > a:hover,
.note-editor .nav-tabs > li.active > a:focus {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-bottom-color: transparent;
  cursor: default;
}
.note-editor .nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.note-editor .nav-tabs.nav-justified > li {
  float: none;
}
.note-editor .nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
@media (min-width: 768px) {
  .note-editor .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .note-editor .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.note-editor .nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.note-editor .nav-tabs.nav-justified > .active > a,
.note-editor .nav-tabs.nav-justified > .active > a:hover,
.note-editor .nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #dddddd;
}
@media (min-width: 768px) {
  .note-editor .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #dddddd;
    border-radius: 4px 4px 0 0;
  }
  .note-editor .nav-tabs.nav-justified > .active > a,
  .note-editor .nav-tabs.nav-justified > .active > a:hover,
  .note-editor .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #ffffff;
  }
}
.note-editor .nav-pills > li {
  float: left;
}
.note-editor .nav-pills > li > a {
  border-radius: 4px;
}
.note-editor .nav-pills > li + li {
  margin-left: 2px;
}
.note-editor .nav-pills > li.active > a,
.note-editor .nav-pills > li.active > a:hover,
.note-editor .nav-pills > li.active > a:focus {
  color: #ffffff;
  background-color: #428bca;
}
.note-editor .nav-pills > li.active > a .caret,
.note-editor .nav-pills > li.active > a:hover .caret,
.note-editor .nav-pills > li.active > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.note-editor .nav-stacked > li {
  float: none;
}
.note-editor .nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.note-editor .nav-justified {
  width: 100%;
}
.note-editor .nav-justified > li {
  float: none;
}
.note-editor .nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
@media (min-width: 768px) {
  .note-editor .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .note-editor .nav-justified > li > a {
    margin-bottom: 0;
  }
}
.note-editor .nav-tabs-justified {
  border-bottom: 0;
}
.note-editor .nav-tabs-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.note-editor .nav-tabs-justified > .active > a,
.note-editor .nav-tabs-justified > .active > a:hover,
.note-editor .nav-tabs-justified > .active > a:focus {
  border: 1px solid #dddddd;
}
@media (min-width: 768px) {
  .note-editor .nav-tabs-justified > li > a {
    border-bottom: 1px solid #dddddd;
    border-radius: 4px 4px 0 0;
  }
  .note-editor .nav-tabs-justified > .active > a,
  .note-editor .nav-tabs-justified > .active > a:hover,
  .note-editor .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #ffffff;
  }
}
.note-editor .tab-content > .tab-pane {
  display: none;
}
.note-editor .tab-content > .active {
  display: block;
}
.note-editor .nav .caret {
  border-top-color: #428bca;
  border-bottom-color: #428bca;
}
.note-editor .nav a:hover .caret {
  border-top-color: #2a6496;
  border-bottom-color: #2a6496;
}
.note-editor .nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .navbar {
  position: relative;
  z-index: 1000;
  min-height: 50px;
  margin-bottom: 20px;
  border: 1px solid transparent;
}
.note-editor .navbar:before,
.note-editor .navbar:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .navbar:after {
  clear: both;
}
.note-editor .navbar:before,
.note-editor .navbar:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .navbar:after {
  clear: both;
}
@media (min-width: 768px) {
  .note-editor .navbar {
    border-radius: 4px;
  }
}
.note-editor .navbar-header:before,
.note-editor .navbar-header:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .navbar-header:after {
  clear: both;
}
.note-editor .navbar-header:before,
.note-editor .navbar-header:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .navbar-header:after {
  clear: both;
}
@media (min-width: 768px) {
  .note-editor .navbar-header {
    float: left;
  }
}
.note-editor .navbar-collapse {
  max-height: 340px;
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.note-editor .navbar-collapse:before,
.note-editor .navbar-collapse:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .navbar-collapse:after {
  clear: both;
}
.note-editor .navbar-collapse:before,
.note-editor .navbar-collapse:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .navbar-collapse:after {
  clear: both;
}
.note-editor .navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 768px) {
  .note-editor .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .note-editor .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .note-editor .navbar-collapse.in {
    overflow-y: visible;
  }
  .note-editor .navbar-collapse .navbar-nav.navbar-left:first-child {
    margin-left: -15px;
  }
  .note-editor .navbar-collapse .navbar-nav.navbar-right:last-child {
    margin-right: -15px;
  }
  .note-editor .navbar-collapse .navbar-text:last-child {
    margin-right: 0;
  }
}
.note-editor .container > .navbar-header,
.note-editor .container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .note-editor .container > .navbar-header,
  .note-editor .container > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.note-editor .navbar-static-top {
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .note-editor .navbar-static-top {
    border-radius: 0;
  }
}
.note-editor .navbar-fixed-top,
.note-editor .navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .note-editor .navbar-fixed-top,
  .note-editor .navbar-fixed-bottom {
    border-radius: 0;
  }
}
.note-editor .navbar-fixed-top {
  z-index: 1030;
  top: 0;
}
.note-editor .navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
}
.note-editor .navbar-brand {
  float: left;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
}
.note-editor .navbar-brand:hover,
.note-editor .navbar-brand:focus {
  text-decoration: none;
}
@media (min-width: 768px) {
  .navbar > .container .note-editor .navbar-brand {
    margin-left: -15px;
  }
}
.note-editor .navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
}
.note-editor .navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.note-editor .navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 768px) {
  .note-editor .navbar-toggle {
    display: none;
  }
}
.note-editor .navbar-nav {
  margin: 7.5px -15px;
}
.note-editor .navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 20px;
}
@media (max-width: 767px) {
  .note-editor .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  .note-editor .navbar-nav .open .dropdown-menu > li > a,
  .note-editor .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px;
  }
  .note-editor .navbar-nav .open .dropdown-menu > li > a {
    line-height: 20px;
  }
  .note-editor .navbar-nav .open .dropdown-menu > li > a:hover,
  .note-editor .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .note-editor .navbar-nav {
    float: left;
    margin: 0;
  }
  .note-editor .navbar-nav > li {
    float: left;
  }
  .note-editor .navbar-nav > li > a {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
@media (min-width: 768px) {
  .note-editor .navbar-left {
    float: left !important;
  }
  .note-editor .navbar-right {
    float: right !important;
  }
}
.note-editor .navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 8px;
  margin-bottom: 8px;
}
@media (min-width: 768px) {
  .note-editor .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .note-editor .navbar-form .form-control {
    display: inline-block;
  }
  .note-editor .navbar-form .radio,
  .note-editor .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
  }
  .note-editor .navbar-form .radio input[type="radio"],
  .note-editor .navbar-form .checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0;
  }
}
@media (max-width: 767px) {
  .note-editor .navbar-form .form-group {
    margin-bottom: 5px;
  }
}
@media (min-width: 768px) {
  .note-editor .navbar-form {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 0;
    padding-bottom: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}
.note-editor .navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.note-editor .navbar-nav.pull-right > li > .dropdown-menu,
.note-editor .navbar-nav > li > .dropdown-menu.pull-right {
  left: auto;
  right: 0;
}
.note-editor .navbar-btn {
  margin-top: 8px;
  margin-bottom: 8px;
}
.note-editor .navbar-text {
  float: left;
  margin-top: 15px;
  margin-bottom: 15px;
}
@media (min-width: 768px) {
  .note-editor .navbar-text {
    margin-left: 15px;
    margin-right: 15px;
  }
}
.note-editor .navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7;
}
.note-editor .navbar-default .navbar-brand {
  color: #777777;
}
.note-editor .navbar-default .navbar-brand:hover,
.note-editor .navbar-default .navbar-brand:focus {
  color: #5e5e5e;
  background-color: transparent;
}
.note-editor .navbar-default .navbar-text {
  color: #777777;
}
.note-editor .navbar-default .navbar-nav > li > a {
  color: #777777;
}
.note-editor .navbar-default .navbar-nav > li > a:hover,
.note-editor .navbar-default .navbar-nav > li > a:focus {
  color: #333333;
  background-color: transparent;
}
.note-editor .navbar-default .navbar-nav > .active > a,
.note-editor .navbar-default .navbar-nav > .active > a:hover,
.note-editor .navbar-default .navbar-nav > .active > a:focus {
  color: #555555;
  background-color: #e7e7e7;
}
.note-editor .navbar-default .navbar-nav > .disabled > a,
.note-editor .navbar-default .navbar-nav > .disabled > a:hover,
.note-editor .navbar-default .navbar-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.note-editor .navbar-default .navbar-toggle {
  border-color: #dddddd;
}
.note-editor .navbar-default .navbar-toggle:hover,
.note-editor .navbar-default .navbar-toggle:focus {
  background-color: #dddddd;
}
.note-editor .navbar-default .navbar-toggle .icon-bar {
  background-color: #cccccc;
}
.note-editor .navbar-default .navbar-collapse,
.note-editor .navbar-default .navbar-form {
  border-color: #e7e7e7;
}
.note-editor .navbar-default .navbar-nav > .dropdown > a:hover .caret,
.note-editor .navbar-default .navbar-nav > .dropdown > a:focus .caret {
  border-top-color: #333333;
  border-bottom-color: #333333;
}
.note-editor .navbar-default .navbar-nav > .open > a,
.note-editor .navbar-default .navbar-nav > .open > a:hover,
.note-editor .navbar-default .navbar-nav > .open > a:focus {
  background-color: #e7e7e7;
  color: #555555;
}
.note-editor .navbar-default .navbar-nav > .open > a .caret,
.note-editor .navbar-default .navbar-nav > .open > a:hover .caret,
.note-editor .navbar-default .navbar-nav > .open > a:focus .caret {
  border-top-color: #555555;
  border-bottom-color: #555555;
}
.note-editor .navbar-default .navbar-nav > .dropdown > a .caret {
  border-top-color: #777777;
  border-bottom-color: #777777;
}
@media (max-width: 767px) {
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777777;
  }
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #333333;
    background-color: transparent;
  }
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #555555;
    background-color: #e7e7e7;
  }
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .note-editor .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
  }
}
.note-editor .navbar-default .navbar-link {
  color: #777777;
}
.note-editor .navbar-default .navbar-link:hover {
  color: #333333;
}
.note-editor .navbar-inverse {
  background-color: #222222;
  border-color: #080808;
}
.note-editor .navbar-inverse .navbar-brand {
  color: #999999;
}
.note-editor .navbar-inverse .navbar-brand:hover,
.note-editor .navbar-inverse .navbar-brand:focus {
  color: #ffffff;
  background-color: transparent;
}
.note-editor .navbar-inverse .navbar-text {
  color: #999999;
}
.note-editor .navbar-inverse .navbar-nav > li > a {
  color: #999999;
}
.note-editor .navbar-inverse .navbar-nav > li > a:hover,
.note-editor .navbar-inverse .navbar-nav > li > a:focus {
  color: #ffffff;
  background-color: transparent;
}
.note-editor .navbar-inverse .navbar-nav > .active > a,
.note-editor .navbar-inverse .navbar-nav > .active > a:hover,
.note-editor .navbar-inverse .navbar-nav > .active > a:focus {
  color: #ffffff;
  background-color: #080808;
}
.note-editor .navbar-inverse .navbar-nav > .disabled > a,
.note-editor .navbar-inverse .navbar-nav > .disabled > a:hover,
.note-editor .navbar-inverse .navbar-nav > .disabled > a:focus {
  color: #444444;
  background-color: transparent;
}
.note-editor .navbar-inverse .navbar-toggle {
  border-color: #333333;
}
.note-editor .navbar-inverse .navbar-toggle:hover,
.note-editor .navbar-inverse .navbar-toggle:focus {
  background-color: #333333;
}
.note-editor .navbar-inverse .navbar-toggle .icon-bar {
  background-color: #ffffff;
}
.note-editor .navbar-inverse .navbar-collapse,
.note-editor .navbar-inverse .navbar-form {
  border-color: #101010;
}
.note-editor .navbar-inverse .navbar-nav > .open > a,
.note-editor .navbar-inverse .navbar-nav > .open > a:hover,
.note-editor .navbar-inverse .navbar-nav > .open > a:focus {
  background-color: #080808;
  color: #ffffff;
}
.note-editor .navbar-inverse .navbar-nav > .dropdown > a:hover .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.note-editor .navbar-inverse .navbar-nav > .dropdown > a .caret {
  border-top-color: #999999;
  border-bottom-color: #999999;
}
.note-editor .navbar-inverse .navbar-nav > .open > a .caret,
.note-editor .navbar-inverse .navbar-nav > .open > a:hover .caret,
.note-editor .navbar-inverse .navbar-nav > .open > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
@media (max-width: 767px) {
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #080808;
  }
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #999999;
  }
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #ffffff;
    background-color: transparent;
  }
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #ffffff;
    background-color: #080808;
  }
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .note-editor .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #444444;
    background-color: transparent;
  }
}
.note-editor .navbar-inverse .navbar-link {
  color: #999999;
}
.note-editor .navbar-inverse .navbar-link:hover {
  color: #ffffff;
}
.note-editor .breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 4px;
}
.note-editor .breadcrumb > li {
  display: inline-block;
}
.note-editor .breadcrumb > li + li:before {
  content: "/\00a0";
  padding: 0 5px;
  color: #cccccc;
}
.note-editor .breadcrumb > .active {
  color: #999999;
}
.note-editor .pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.note-editor .pagination > li {
  display: inline;
}
.note-editor .pagination > li > a,
.note-editor .pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  line-height: 1.428571429;
  text-decoration: none;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  margin-left: -1px;
}
.note-editor .pagination > li:first-child > a,
.note-editor .pagination > li:first-child > span {
  margin-left: 0;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.note-editor .pagination > li:last-child > a,
.note-editor .pagination > li:last-child > span {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
.note-editor .pagination > li > a:hover,
.note-editor .pagination > li > span:hover,
.note-editor .pagination > li > a:focus,
.note-editor .pagination > li > span:focus {
  background-color: #eeeeee;
}
.note-editor .pagination > .active > a,
.note-editor .pagination > .active > span,
.note-editor .pagination > .active > a:hover,
.note-editor .pagination > .active > span:hover,
.note-editor .pagination > .active > a:focus,
.note-editor .pagination > .active > span:focus {
  z-index: 2;
  color: #ffffff;
  background-color: #428bca;
  border-color: #428bca;
  cursor: default;
}
.note-editor .pagination > .disabled > span,
.note-editor .pagination > .disabled > span:hover,
.note-editor .pagination > .disabled > span:focus,
.note-editor .pagination > .disabled > a,
.note-editor .pagination > .disabled > a:hover,
.note-editor .pagination > .disabled > a:focus {
  color: #999999;
  background-color: #ffffff;
  border-color: #dddddd;
  cursor: not-allowed;
}
.note-editor .pagination-lg > li > a,
.note-editor .pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 18px;
}
.note-editor .pagination-lg > li:first-child > a,
.note-editor .pagination-lg > li:first-child > span {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.note-editor .pagination-lg > li:last-child > a,
.note-editor .pagination-lg > li:last-child > span {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.note-editor .pagination-sm > li > a,
.note-editor .pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px;
}
.note-editor .pagination-sm > li:first-child > a,
.note-editor .pagination-sm > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.note-editor .pagination-sm > li:last-child > a,
.note-editor .pagination-sm > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.note-editor .pager {
  padding-left: 0;
  margin: 20px 0;
  list-style: none;
  text-align: center;
}
.note-editor .pager:before,
.note-editor .pager:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .pager:after {
  clear: both;
}
.note-editor .pager:before,
.note-editor .pager:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .pager:after {
  clear: both;
}
.note-editor .pager li {
  display: inline;
}
.note-editor .pager li > a,
.note-editor .pager li > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 15px;
}
.note-editor .pager li > a:hover,
.note-editor .pager li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.note-editor .pager .next > a,
.note-editor .pager .next > span {
  float: right;
}
.note-editor .pager .previous > a,
.note-editor .pager .previous > span {
  float: left;
}
.note-editor .pager .disabled > a,
.note-editor .pager .disabled > a:hover,
.note-editor .pager .disabled > a:focus,
.note-editor .pager .disabled > span {
  color: #999999;
  background-color: #ffffff;
  cursor: not-allowed;
}
.note-editor .label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
}
.note-editor .label[href]:hover,
.note-editor .label[href]:focus {
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
}
.note-editor .label:empty {
  display: none;
}
.note-editor .label-default {
  background-color: #999999;
}
.note-editor .label-default[href]:hover,
.note-editor .label-default[href]:focus {
  background-color: #808080;
}
.note-editor .label-primary {
  background-color: #428bca;
}
.note-editor .label-primary[href]:hover,
.note-editor .label-primary[href]:focus {
  background-color: #3071a9;
}
.note-editor .label-success {
  background-color: #5cb85c;
}
.note-editor .label-success[href]:hover,
.note-editor .label-success[href]:focus {
  background-color: #449d44;
}
.note-editor .label-info {
  background-color: #5bc0de;
}
.note-editor .label-info[href]:hover,
.note-editor .label-info[href]:focus {
  background-color: #31b0d5;
}
.note-editor .label-warning {
  background-color: #f0ad4e;
}
.note-editor .label-warning[href]:hover,
.note-editor .label-warning[href]:focus {
  background-color: #ec971f;
}
.note-editor .label-danger {
  background-color: #d9534f;
}
.note-editor .label-danger[href]:hover,
.note-editor .label-danger[href]:focus {
  background-color: #c9302c;
}
.note-editor .badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999999;
  border-radius: 10px;
}
.note-editor .badge:empty {
  display: none;
}
.note-editor a.badge:hover,
.note-editor a.badge:focus {
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
}
.note-editor .btn .badge {
  position: relative;
  top: -1px;
}
.note-editor a.list-group-item.active > .badge,
.note-editor .nav-pills > .active > a > .badge {
  color: #428bca;
  background-color: #ffffff;
}
.note-editor .nav-pills > li > a > .badge {
  margin-left: 3px;
}
.note-editor .jumbotron {
  padding: 30px;
  margin-bottom: 30px;
  font-size: 21px;
  font-weight: 200;
  line-height: 2.1428571435;
  color: inherit;
  background-color: #eeeeee;
}
.note-editor .jumbotron h1 {
  line-height: 1;
  color: inherit;
}
.note-editor .jumbotron p {
  line-height: 1.4;
}
.container .note-editor .jumbotron {
  border-radius: 6px;
}
@media screen and (min-width: 768px) {
  .note-editor .jumbotron {
    padding-top: 48px;
    padding-bottom: 48px;
  }
  .container .note-editor .jumbotron {
    padding-left: 60px;
    padding-right: 60px;
  }
  .note-editor .jumbotron h1 {
    font-size: 63px;
  }
}
.note-editor .thumbnail {
  padding: 4px;
  line-height: 1.428571429;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
  display: block;
  margin-bottom: 20px;
}
.note-editor .thumbnail > img {
  display: block;
  max-width: 100%;
  height: auto;
}
.note-editor a.thumbnail:hover,
.note-editor a.thumbnail:focus,
.note-editor a.thumbnail.active {
  border-color: #428bca;
}
.note-editor .thumbnail > img {
  margin-left: auto;
  margin-right: auto;
}
.note-editor .thumbnail .caption {
  padding: 9px;
  color: #333333;
}
.note-editor .alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.note-editor .alert h4 {
  margin-top: 0;
  color: inherit;
}
.note-editor .alert .alert-link {
  font-weight: bold;
}
.note-editor .alert > p,
.note-editor .alert > ul {
  margin-bottom: 0;
}
.note-editor .alert > p + p {
  margin-top: 5px;
}
.note-editor .alert-dismissable {
  padding-right: 35px;
}
.note-editor .alert-dismissable .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.note-editor .alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #468847;
}
.note-editor .alert-success hr {
  border-top-color: #c9e2b3;
}
.note-editor .alert-success .alert-link {
  color: #356635;
}
.note-editor .alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #3a87ad;
}
.note-editor .alert-info hr {
  border-top-color: #a6e1ec;
}
.note-editor .alert-info .alert-link {
  color: #2d6987;
}
.note-editor .alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #c09853;
}
.note-editor .alert-warning hr {
  border-top-color: #f7e1b5;
}
.note-editor .alert-warning .alert-link {
  color: #a47e3c;
}
.note-editor .alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #b94a48;
}
.note-editor .alert-danger hr {
  border-top-color: #e4b9c0;
}
.note-editor .alert-danger .alert-link {
  color: #953b39;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@-moz-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@-o-keyframes progress-bar-stripes {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 40px 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.note-editor .progress {
  overflow: hidden;
  height: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.note-editor .progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #ffffff;
  text-align: center;
  background-color: #428bca;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
.note-editor .progress-striped .progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.note-editor .progress.active .progress-bar {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -moz-animation: progress-bar-stripes 2s linear infinite;
  -ms-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.note-editor .progress-bar-success {
  background-color: #5cb85c;
}
.progress-striped .note-editor .progress-bar-success {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.note-editor .progress-bar-info {
  background-color: #5bc0de;
}
.progress-striped .note-editor .progress-bar-info {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.note-editor .progress-bar-warning {
  background-color: #f0ad4e;
}
.progress-striped .note-editor .progress-bar-warning {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.note-editor .progress-bar-danger {
  background-color: #d9534f;
}
.progress-striped .note-editor .progress-bar-danger {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.note-editor .media,
.note-editor .media-body {
  overflow: hidden;
  zoom: 1;
}
.note-editor .media,
.note-editor .media .media {
  margin-top: 15px;
}
.note-editor .media:first-child {
  margin-top: 0;
}
.note-editor .media-object {
  display: block;
}
.note-editor .media-heading {
  margin: 0 0 5px;
}
.note-editor .media > .pull-left {
  margin-right: 10px;
}
.note-editor .media > .pull-right {
  margin-left: 10px;
}
.note-editor .media-list {
  padding-left: 0;
  list-style: none;
}
.note-editor .list-group {
  margin-bottom: 20px;
  padding-left: 0;
}
.note-editor .list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #ffffff;
  border: 1px solid #dddddd;
}
.note-editor .list-group-item:first-child {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
.note-editor .list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.note-editor .list-group-item > .badge {
  float: right;
}
.note-editor .list-group-item > .badge + .badge {
  margin-right: 5px;
}
.note-editor a.list-group-item {
  color: #555555;
}
.note-editor a.list-group-item .list-group-item-heading {
  color: #333333;
}
.note-editor a.list-group-item:hover,
.note-editor a.list-group-item:focus {
  text-decoration: none;
  background-color: #f5f5f5;
}
.note-editor a.list-group-item.active,
.note-editor a.list-group-item.active:hover,
.note-editor a.list-group-item.active:focus {
  z-index: 2;
  color: #ffffff;
  background-color: #428bca;
  border-color: #428bca;
}
.note-editor a.list-group-item.active .list-group-item-heading,
.note-editor a.list-group-item.active:hover .list-group-item-heading,
.note-editor a.list-group-item.active:focus .list-group-item-heading {
  color: inherit;
}
.note-editor a.list-group-item.active .list-group-item-text,
.note-editor a.list-group-item.active:hover .list-group-item-text,
.note-editor a.list-group-item.active:focus .list-group-item-text {
  color: #e1edf7;
}
.note-editor .list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.note-editor .list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.note-editor .panel {
  margin-bottom: 20px;
  background-color: #ffffff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.note-editor .panel-body {
  padding: 15px;
}
.note-editor .panel-body:before,
.note-editor .panel-body:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .panel-body:after {
  clear: both;
}
.note-editor .panel-body:before,
.note-editor .panel-body:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.note-editor .panel-body:after {
  clear: both;
}
.note-editor .panel > .list-group {
  margin-bottom: 0;
}
.note-editor .panel > .list-group .list-group-item {
  border-width: 1px 0;
}
.note-editor .panel > .list-group .list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.note-editor .panel > .list-group .list-group-item:last-child {
  border-bottom: 0;
}
.note-editor .panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.note-editor .panel > .table,
.note-editor .panel > .table-responsive {
  margin-bottom: 0;
}
.note-editor .panel > .panel-body + .table,
.note-editor .panel > .panel-body + .table-responsive {
  border-top: 1px solid #dddddd;
}
.note-editor .panel > .table-bordered,
.note-editor .panel > .table-responsive > .table-bordered {
  border: 0;
}
.note-editor .panel > .table-bordered > thead > tr > th:first-child,
.note-editor .panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.note-editor .panel > .table-bordered > tbody > tr > th:first-child,
.note-editor .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.note-editor .panel > .table-bordered > tfoot > tr > th:first-child,
.note-editor .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.note-editor .panel > .table-bordered > thead > tr > td:first-child,
.note-editor .panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.note-editor .panel > .table-bordered > tbody > tr > td:first-child,
.note-editor .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.note-editor .panel > .table-bordered > tfoot > tr > td:first-child,
.note-editor .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.note-editor .panel > .table-bordered > thead > tr > th:last-child,
.note-editor .panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.note-editor .panel > .table-bordered > tbody > tr > th:last-child,
.note-editor .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.note-editor .panel > .table-bordered > tfoot > tr > th:last-child,
.note-editor .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.note-editor .panel > .table-bordered > thead > tr > td:last-child,
.note-editor .panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.note-editor .panel > .table-bordered > tbody > tr > td:last-child,
.note-editor .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.note-editor .panel > .table-bordered > tfoot > tr > td:last-child,
.note-editor .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.note-editor .panel > .table-bordered > thead > tr:last-child > th,
.note-editor .panel > .table-responsive > .table-bordered > thead > tr:last-child > th,
.note-editor .panel > .table-bordered > tbody > tr:last-child > th,
.note-editor .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.note-editor .panel > .table-bordered > tfoot > tr:last-child > th,
.note-editor .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th,
.note-editor .panel > .table-bordered > thead > tr:last-child > td,
.note-editor .panel > .table-responsive > .table-bordered > thead > tr:last-child > td,
.note-editor .panel > .table-bordered > tbody > tr:last-child > td,
.note-editor .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.note-editor .panel > .table-bordered > tfoot > tr:last-child > td,
.note-editor .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td {
  border-bottom: 0;
}
.note-editor .panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.note-editor .panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
}
.note-editor .panel-title > a {
  color: inherit;
}
.note-editor .panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #dddddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.note-editor .panel-group .panel {
  margin-bottom: 0;
  border-radius: 4px;
  overflow: hidden;
}
.note-editor .panel-group .panel + .panel {
  margin-top: 5px;
}
.note-editor .panel-group .panel-heading {
  border-bottom: 0;
}
.note-editor .panel-group .panel-heading + .panel-collapse .panel-body {
  border-top: 1px solid #dddddd;
}
.note-editor .panel-group .panel-footer {
  border-top: 0;
}
.note-editor .panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #dddddd;
}
.note-editor .panel-default {
  border-color: #dddddd;
}
.note-editor .panel-default > .panel-heading {
  color: #333333;
  background-color: #f5f5f5;
  border-color: #dddddd;
}
.note-editor .panel-default > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #dddddd;
}
.note-editor .panel-default > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #dddddd;
}
.note-editor .panel-primary {
  border-color: #428bca;
}
.note-editor .panel-primary > .panel-heading {
  color: #ffffff;
  background-color: #428bca;
  border-color: #428bca;
}
.note-editor .panel-primary > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #428bca;
}
.note-editor .panel-primary > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #428bca;
}
.note-editor .panel-success {
  border-color: #d6e9c6;
}
.note-editor .panel-success > .panel-heading {
  color: #468847;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.note-editor .panel-success > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #d6e9c6;
}
.note-editor .panel-success > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #d6e9c6;
}
.note-editor .panel-warning {
  border-color: #faebcc;
}
.note-editor .panel-warning > .panel-heading {
  color: #c09853;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.note-editor .panel-warning > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #faebcc;
}
.note-editor .panel-warning > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #faebcc;
}
.note-editor .panel-danger {
  border-color: #ebccd1;
}
.note-editor .panel-danger > .panel-heading {
  color: #b94a48;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.note-editor .panel-danger > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebccd1;
}
.note-editor .panel-danger > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebccd1;
}
.note-editor .panel-info {
  border-color: #bce8f1;
}
.note-editor .panel-info > .panel-heading {
  color: #3a87ad;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.note-editor .panel-info > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #bce8f1;
}
.note-editor .panel-info > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #bce8f1;
}
.note-editor .well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.note-editor .well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.note-editor .well-lg {
  padding: 24px;
  border-radius: 6px;
}
.note-editor .well-sm {
  padding: 9px;
  border-radius: 3px;
}
.note-editor .close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000000;
  text-shadow: 0 1px 0 #ffffff;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.note-editor .close:hover,
.note-editor .close:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.note-editor .close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal {
  display: none;
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
}
.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}
.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
}
.modal-dialog {
  margin-left: auto;
  margin-right: auto;
  width: auto;
  padding: 10px;
  z-index: 1050;
}
.modal-content {
  position: relative;
  background-color: #ffffff;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: none;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
  background-color: #000000;
}
.modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-backdrop.in {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  min-height: 16.428571429px;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.428571429;
}
.modal-body {
  position: relative;
  padding: 20px;
}
.modal-footer {
  margin-top: 15px;
  padding: 19px 20px 20px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}
.modal-footer:before,
.modal-footer:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.modal-footer:after {
  clear: both;
}
.modal-footer:before,
.modal-footer:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.modal-footer:after {
  clear: both;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
@media screen and (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
}
.tooltip {
  position: absolute;
  z-index: 1030;
  display: block;
  visibility: visible;
  font-size: 12px;
  line-height: 1.4;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-left: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-left: -3px;
  padding: 0 5px;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #ffffff;
  text-align: center;
  text-decoration: none;
  background-color: #000000;
  border-radius: 4px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  left: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  right: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  left: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  right: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1010;
  display: none;
  max-width: 276px;
  padding: 1px;
  text-align: left;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  white-space: normal;
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-left: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-left: -10px;
}
.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}
.popover-content {
  padding: 9px 14px;
}
.popover .arrow,
.popover .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover .arrow {
  border-width: 11px;
}
.popover .arrow:after {
  border-width: 10px;
  content: "";
}
.popover.top .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.popover.top .arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #ffffff;
}
.popover.right .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.popover.right .arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #ffffff;
}
.popover.bottom .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.popover.bottom .arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #ffffff;
}
.popover.left .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.left .arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #ffffff;
  bottom: -10px;
}
.carousel {
  position: relative;
}
.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%;
}
.carousel-inner > .item {
  display: none;
  position: relative;
  -webkit-transition: 0.6s ease-in-out left;
  transition: 0.6s ease-in-out left;
}
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  display: block;
  max-width: 100%;
  height: auto;
  line-height: 1;
}
.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {
  display: block;
}
.carousel-inner > .active {
  left: 0;
}
.carousel-inner > .next,
.carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.carousel-inner > .next {
  left: 100%;
}
.carousel-inner > .prev {
  left: -100%;
}
.carousel-inner > .next.left,
.carousel-inner > .prev.right {
  left: 0;
}
.carousel-inner > .active.left {
  left: -100%;
}
.carousel-inner > .active.right {
  left: 100%;
}
.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-control.left {
  background-image: -webkit-gradient(linear, 0% top, 100% top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0.0001)));
  background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.5) 0%), color-stop(rgba(0, 0, 0, 0.0001) 100%));
  background-image: -moz-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}
.carousel-control.right {
  left: auto;
  right: 0;
  background-image: -webkit-gradient(linear, 0% top, 100% top, from(rgba(0, 0, 0, 0.0001)), to(rgba(0, 0, 0, 0.5)));
  background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.0001) 0%), color-stop(rgba(0, 0, 0, 0.5) 100%));
  background-image: -moz-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}
.carousel-control:hover,
.carousel-control:focus {
  color: #ffffff;
  text-decoration: none;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
  position: absolute;
  top: 50%;
  z-index: 5;
  display: inline-block;
}
.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
  left: 50%;
}
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
  right: 50%;
}
.carousel-control .icon-prev,
.carousel-control .icon-next {
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  font-family: serif;
}
.carousel-control .icon-prev:before {
  content: '\2039';
}
.carousel-control .icon-next:before {
  content: '\203a';
}
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center;
}
.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  border: 1px solid #ffffff;
  border-radius: 10px;
  cursor: pointer;
}
.carousel-indicators .active {
  margin: 0;
  width: 12px;
  height: 12px;
  background-color: #ffffff;
}
.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-caption .btn {
  text-shadow: none;
}
@media screen and (min-width: 768px) {
  .carousel-control .glyphicons-chevron-left,
  .carousel-control .glyphicons-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    margin-left: -15px;
    font-size: 30px;
  }
  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}
.clearfix:before,
.clearfix:after {
  content: " ";
  /* 1 */

  display: table;
  /* 2 */

}
.clearfix:after {
  clear: both;
}
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
  visibility: hidden !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.visible-xs,
tr.visible-xs,
th.visible-xs,
td.visible-xs {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs,
  td.visible-xs {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-xs.visible-sm {
    display: block !important;
  }
  tr.visible-xs.visible-sm {
    display: table-row !important;
  }
  th.visible-xs.visible-sm,
  td.visible-xs.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-xs.visible-md {
    display: block !important;
  }
  tr.visible-xs.visible-md {
    display: table-row !important;
  }
  th.visible-xs.visible-md,
  td.visible-xs.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-xs.visible-lg {
    display: block !important;
  }
  tr.visible-xs.visible-lg {
    display: table-row !important;
  }
  th.visible-xs.visible-lg,
  td.visible-xs.visible-lg {
    display: table-cell !important;
  }
}
.visible-sm,
tr.visible-sm,
th.visible-sm,
td.visible-sm {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-sm.visible-xs {
    display: block !important;
  }
  tr.visible-sm.visible-xs {
    display: table-row !important;
  }
  th.visible-sm.visible-xs,
  td.visible-sm.visible-xs {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm,
  td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-sm.visible-md {
    display: block !important;
  }
  tr.visible-sm.visible-md {
    display: table-row !important;
  }
  th.visible-sm.visible-md,
  td.visible-sm.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-sm.visible-lg {
    display: block !important;
  }
  tr.visible-sm.visible-lg {
    display: table-row !important;
  }
  th.visible-sm.visible-lg,
  td.visible-sm.visible-lg {
    display: table-cell !important;
  }
}
.visible-md,
tr.visible-md,
th.visible-md,
td.visible-md {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-md.visible-xs {
    display: block !important;
  }
  tr.visible-md.visible-xs {
    display: table-row !important;
  }
  th.visible-md.visible-xs,
  td.visible-md.visible-xs {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-md.visible-sm {
    display: block !important;
  }
  tr.visible-md.visible-sm {
    display: table-row !important;
  }
  th.visible-md.visible-sm,
  td.visible-md.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md,
  td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-md.visible-lg {
    display: block !important;
  }
  tr.visible-md.visible-lg {
    display: table-row !important;
  }
  th.visible-md.visible-lg,
  td.visible-md.visible-lg {
    display: table-cell !important;
  }
}
.visible-lg,
tr.visible-lg,
th.visible-lg,
td.visible-lg {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-lg.visible-xs {
    display: block !important;
  }
  tr.visible-lg.visible-xs {
    display: table-row !important;
  }
  th.visible-lg.visible-xs,
  td.visible-lg.visible-xs {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-lg.visible-sm {
    display: block !important;
  }
  tr.visible-lg.visible-sm {
    display: table-row !important;
  }
  th.visible-lg.visible-sm,
  td.visible-lg.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-lg.visible-md {
    display: block !important;
  }
  tr.visible-lg.visible-md {
    display: table-row !important;
  }
  th.visible-lg.visible-md,
  td.visible-lg.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg {
    display: block !important;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg,
  td.visible-lg {
    display: table-cell !important;
  }
}
.hidden-xs {
  display: block !important;
}
tr.hidden-xs {
  display: table-row !important;
}
th.hidden-xs,
td.hidden-xs {
  display: table-cell !important;
}
@media (max-width: 767px) {
  .hidden-xs,
  tr.hidden-xs,
  th.hidden-xs,
  td.hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-xs.hidden-sm,
  tr.hidden-xs.hidden-sm,
  th.hidden-xs.hidden-sm,
  td.hidden-xs.hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-xs.hidden-md,
  tr.hidden-xs.hidden-md,
  th.hidden-xs.hidden-md,
  td.hidden-xs.hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-xs.hidden-lg,
  tr.hidden-xs.hidden-lg,
  th.hidden-xs.hidden-lg,
  td.hidden-xs.hidden-lg {
    display: none !important;
  }
}
.hidden-sm {
  display: block !important;
}
tr.hidden-sm {
  display: table-row !important;
}
th.hidden-sm,
td.hidden-sm {
  display: table-cell !important;
}
@media (max-width: 767px) {
  .hidden-sm.hidden-xs,
  tr.hidden-sm.hidden-xs,
  th.hidden-sm.hidden-xs,
  td.hidden-sm.hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm,
  tr.hidden-sm,
  th.hidden-sm,
  td.hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-sm.hidden-md,
  tr.hidden-sm.hidden-md,
  th.hidden-sm.hidden-md,
  td.hidden-sm.hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-sm.hidden-lg,
  tr.hidden-sm.hidden-lg,
  th.hidden-sm.hidden-lg,
  td.hidden-sm.hidden-lg {
    display: none !important;
  }
}
.hidden-md {
  display: block !important;
}
tr.hidden-md {
  display: table-row !important;
}
th.hidden-md,
td.hidden-md {
  display: table-cell !important;
}
@media (max-width: 767px) {
  .hidden-md.hidden-xs,
  tr.hidden-md.hidden-xs,
  th.hidden-md.hidden-xs,
  td.hidden-md.hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-md.hidden-sm,
  tr.hidden-md.hidden-sm,
  th.hidden-md.hidden-sm,
  td.hidden-md.hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md,
  tr.hidden-md,
  th.hidden-md,
  td.hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-md.hidden-lg,
  tr.hidden-md.hidden-lg,
  th.hidden-md.hidden-lg,
  td.hidden-md.hidden-lg {
    display: none !important;
  }
}
.hidden-lg {
  display: block !important;
}
tr.hidden-lg {
  display: table-row !important;
}
th.hidden-lg,
td.hidden-lg {
  display: table-cell !important;
}
@media (max-width: 767px) {
  .hidden-lg.hidden-xs,
  tr.hidden-lg.hidden-xs,
  th.hidden-lg.hidden-xs,
  td.hidden-lg.hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-lg.hidden-sm,
  tr.hidden-lg.hidden-sm,
  th.hidden-lg.hidden-sm,
  td.hidden-lg.hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-lg.hidden-md,
  tr.hidden-lg.hidden-md,
  th.hidden-lg.hidden-md,
  td.hidden-lg.hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-lg,
  tr.hidden-lg,
  th.hidden-lg,
  td.hidden-lg {
    display: none !important;
  }
}
.visible-print,
tr.visible-print,
th.visible-print,
td.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
  .hidden-print,
  tr.hidden-print,
  th.hidden-print,
  td.hidden-print {
    display: none !important;
  }
}
