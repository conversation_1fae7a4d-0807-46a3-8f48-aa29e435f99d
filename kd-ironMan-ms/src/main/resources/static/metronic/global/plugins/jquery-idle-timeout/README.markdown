# jQuery Idle Timeout

See the original [Mint.com example](http://www.erichynds.com/examples/jquery-idle-timeout/example-mint.htm), or a [demo](http://www.erichynds.com/examples/jquery-idle-timeout/example-dialog.htm) using jQuery UI's dialog widget.

This script allows you to detect when a user becomes idle (detection provided by <PERSON>'s idletimer plugin) and notify the user his/her session
is about to expire.  Similar to the technique seen on Mint.com.  Polling requests are automatically sent to the server at a configurable
interval, maintaining the users session while s/he is using your application for long periods of time.

![Example](http://www.erichynds.com/examples/jquery-idle-timeout/screenshot.gif)
