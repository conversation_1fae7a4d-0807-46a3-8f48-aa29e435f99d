var Datatable=function(){var e,t,a,n,r,o,c=!1,i={},s=function(){var t=$('tbody > tr > td:nth-child(1) input[type="checkbox"]:checked',a).size(),n=e.dataTable.language.metronicGroupActions;t>0?$(".table-group-actions > span",r).text(n.replace("_TOTAL_",t)):$(".table-group-actions > span",r).text("")};return{init:function(l){if($().dataTable){o=this,l=$.extend(!0,{src:"",filterApplyAction:"filter",filterCancelAction:"filter_cancel",resetGroupActionInputOnSuccess:!0,loadingMessage:"Loading...",dataTable:{dom:"<'row'<'col-md-8 col-sm-12'pli><'col-md-4 col-sm-12'<'table-group-actions pull-right'>>r><'table-responsive't><'row'<'col-md-8 col-sm-12'pli><'col-md-4 col-sm-12'>>",pageLength:10,language:{metronicGroupActions:"_TOTAL_ records selected:  ",metronicAjaxRequestGeneralError:"Could not complete request. Please check your internet connection",lengthMenu:"<span class='seperator'>|</span>View _MENU_ records",info:"<span class='seperator'>|</span>Found total _TOTAL_ records",infoEmpty:"No records found to show",emptyTable:"No data available in table",zeroRecords:"No matching records found",paginate:{previous:"Prev",next:"Next",last:"Last",first:"First",page:"Page",pageOf:"of"}},orderCellsTop:!0,columnDefs:[{orderable:!1,targets:[0]}],pagingType:"bootstrap_extended",autoWidth:!1,processing:!1,serverSide:!0,ajax:{url:"",type:"POST",timeout:2e4,data:function(t){$.each(i,function(e,a){t[e]=a}),App.blockUI({message:e.loadingMessage,target:n,overlayColor:"none",cenrerY:!0,boxed:!0})},dataSrc:function(t){return t.customActionMessage&&App.alert({type:"OK"==t.customActionStatus?"success":"danger",icon:"OK"==t.customActionStatus?"check":"warning",message:t.customActionMessage,container:r,place:"prepend"}),t.customActionStatus&&e.resetGroupActionInputOnSuccess&&$(".table-group-action-input",r).val(""),1===$(".group-checkable",a).size()&&($(".group-checkable",a).attr("checked",!1),$.uniform.update($(".group-checkable",a))),e.onSuccess&&e.onSuccess.call(void 0,o,t),App.unblockUI(n),t.data},error:function(){e.onError&&e.onError.call(void 0,o),App.alert({type:"danger",icon:"warning",message:e.dataTable.language.metronicAjaxRequestGeneralError,container:r,place:"prepend"}),App.unblockUI(n)}},drawCallback:function(t){c===!1&&(c=!0,a.show()),App.initUniform($('input[type="checkbox"]',a)),s(),e.onDataLoad&&e.onDataLoad.call(void 0,o)}}},l),e=l,a=$(l.src),n=a.parents(".table-container");var p=$.fn.dataTableExt.oStdClasses;$.fn.dataTableExt.oStdClasses.sWrapper=$.fn.dataTableExt.oStdClasses.sWrapper+" dataTables_extended_wrapper",$.fn.dataTableExt.oStdClasses.sFilterInput="form-control input-xs input-sm input-inline",$.fn.dataTableExt.oStdClasses.sLengthSelect="form-control input-xs input-sm input-inline",t=a.DataTable(l.dataTable),$.fn.dataTableExt.oStdClasses.sWrapper=p.sWrapper,$.fn.dataTableExt.oStdClasses.sFilterInput=p.sFilterInput,$.fn.dataTableExt.oStdClasses.sLengthSelect=p.sLengthSelect,r=a.parents(".dataTables_wrapper"),1===$(".table-actions-wrapper",n).size()&&($(".table-group-actions",r).html($(".table-actions-wrapper",n).html()),$(".table-actions-wrapper",n).remove()),$(".group-checkable",a).change(function(){var e=a.find('tbody > tr > td:nth-child(1) input[type="checkbox"]'),t=$(this).prop("checked");$(e).each(function(){$(this).prop("checked",t)}),$.uniform.update(e),s()}),a.on("change",'tbody > tr > td:nth-child(1) input[type="checkbox"]',function(){s()}),a.on("click",".filter-submit",function(e){e.preventDefault(),o.submitFilter()}),a.on("click",".filter-cancel",function(e){e.preventDefault(),o.resetFilter()})}},submitFilter:function(){o.setAjaxParam("action",e.filterApplyAction),$('textarea.form-filter, select.form-filter, input.form-filter:not([type="radio"],[type="checkbox"])',a).each(function(){o.setAjaxParam($(this).attr("name"),$(this).val())}),$('input.form-filter[type="checkbox"]:checked',a).each(function(){o.addAjaxParam($(this).attr("name"),$(this).val())}),$('input.form-filter[type="radio"]:checked',a).each(function(){o.setAjaxParam($(this).attr("name"),$(this).val())}),t.ajax.reload()},resetFilter:function(){$("textarea.form-filter, select.form-filter, input.form-filter",a).each(function(){$(this).val("")}),$('input.form-filter[type="checkbox"]',a).each(function(){$(this).attr("checked",!1)}),o.clearAjaxParams(),o.addAjaxParam("action",e.filterCancelAction),t.ajax.reload()},getSelectedRowsCount:function(){return $('tbody > tr > td:nth-child(1) input[type="checkbox"]:checked',a).size()},getSelectedRows:function(){var e=[];return $('tbody > tr > td:nth-child(1) input[type="checkbox"]:checked',a).each(function(){e.push($(this).val())}),e},setAjaxParam:function(e,t){i[e]=t},addAjaxParam:function(e,t){i[e]||(i[e]=[]),skip=!1;for(var a=0;a<i[e].length;a++)i[e][a]===t&&(skip=!0);skip===!1&&i[e].push(t)},clearAjaxParams:function(e,t){i={}},getDataTable:function(){return t},getTableWrapper:function(){return r},gettableContainer:function(){return n},getTable:function(){return a}}};