<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>保司复核</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script src="${ctx}/carousel/dataCollection/viewer.js"></script>
    <script src="${ctx}/carousel/dataCollection/viewermain.js"></script>
    <link href="${ctx}/carousel/dataCollection/viewer.css" rel="stylesheet">
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/applyDuty/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet"
          type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet"
          type="text/css"/>
    <script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js"
            type="text/javascript"></script>
    <style>

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 30%;
            transform: translateY(-50%);
            background: greenyellow;
        }

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }
        /*loader需要的遮罩层css end*/

        .clear-margin {
            margin: 0px;
        }

        .clear-padding {
            padding: 0px;
        }

        .chart-search {
            margin-bottom: 10px;
            display: none;
        }


        .search-channel-content {
            height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .search-ul-list {
            list-style-type: none;
            height: 20px;
            padding-left: 0px;
            color: black;
        }

        .channel {
            float: left;
            display: block;
        }

        .typeClick {
            height:112px;
            margin-bottom: 10px;
            border:2px solid black;
            border-radius:5px!important;
            margin-left: 20px;
            cursor: pointer
        }

        .typeClickClick {
            height:112px;
            margin-bottom: 10px;
            border:2px solid #1676ff;
            background-color: #1676ff;
            color: white;
            border-radius:5px!important;
            margin-left: 20px;
            cursor: pointer
        }
        .has-error {
            border-color: #ce0d0d!important;
            color: red!important;
        }
        .bckColor {
            background-color: rgba(51, 153, 241, 0.3);
        }
        .imageListChoose{
            /*background-color: #bad6ff !important;*/
            border: 5px solid #055ad7;
        }
        .collectionCompleted {
            background-color: #E8F2FF;
        }
        .collectionButton{
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: transparent;
            border-radius: 6px !important;
            color: #1ABB00;
        }
        .collectionButtonClick{
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00 !important;
            border-radius: 6px !important;
            color: #FFFFFF;
        }
        .collectionData:hover{
            /*border-color: #1ABB00;*/
            border: 1px solid #1ABB00;
            background-color: #1ABB00;
            color: #FFFFFF;
        }
        .middleTable{
            border: 4px solid #1767ff !important;
        }
        .hangup {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #ce0d0d !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }
        .error {
            color: #ce0d0d !important;
        }
        .txt-over-hide {
            color: red;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display:block;
            width:650px;
        }
        .jump {
            /*float: right;*/
            border-radius: 5px !important;
            color: #3399ff !important;
            background-color: #fff !important;
            border-color: #3399ff;
            height: 40px;
            width: 100%;
        }
        .genTask {
            /*margin-left: 5px;*/
            color: #fff !important;
            background-color: #3399ff !important;
            border-radius: 5px !important;
            height: 40px;
            width: 100%;
        }
        .thClass{
            text-align: center;
            color: #fff;
        }
        .typeSelect{
            border-color: #3399ff;
            background-color: #3399ff;
        }

        .thumbnail-img {
            width: 50px;
            height: 45px;
            border: 4px solid #D6D6C1;
        }
        .selected-thumbnail-img{
            border: 4px solid #3399ff!important;
        }
        .collection-thumbnail-img{
            border: 4px solid #00B176;
        }

        /*里面的代码可以根据自己需求去进行更改*/
        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width:5px;
        }
        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            -webkit-box-shadow:inset006pxrgba(0,0,0,0.3);
            border-radius:10px;
        }
        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius:10px;
            background:rgba(0,0,0,0.1);
            -webkit-box-shadow:inset006pxrgba(0,0,0,0.5);
        }
        ::-webkit-scrollbar-thumb:window-inactive {
            background:rgba(255,0,0,0.4);
        }

        .ticket-information{
            height: 1050px;
            overflow-y: scroll;
        }

        .dataDisplayArea{
            width: 100%;
            height: 19%;
            border-radius: 10px !important;
            /*border: 1px solid #aca8a8;*/
            border: 1px solid #797979;
            cursor: pointer;
            margin-top: 10px;
            overflow: hidden;
        }
        .selected-dataDisplayArea{
            background-color: #91cae9;
        }
        .submit-dataDisplayArea{
            border: 3px solid #44db69 !important;
        }
        .change-dataDisplayArea{
            border: 3px solid red !important;
        }
        .dataDisplayArea>div{
            height: 20%;
            padding: 10px 0px 0px 0px !important;
        }
        .dataDisplayArea-head{
            background-color: #3399ff;
            /*border-radius: 10px 10px 0px 0px !important;*/
        }
        .dataDisplayArea-head-left{
            color: #ffffff;
            font-size: 15px;
            padding: 0px 0px 0px 5px;
        }
        .dataDisplayArea-head-right{
            text-align: right;
            padding: 0px !important;
        }
        .dataDisplayArea-head-img{
            width: 30px;
            height: 18px;
        }
        .dataDisplayArea-body-left{
            opacity: 0.7;
        }
        .dataDisplayArea-body-right{
            text-align: right;
        }

        .row{
            margin: 0px 0px;
        }

        .subject-name {
            
            font-size: 20px;
        }

        .error-class .layui-layer-content {
            font-size: 20px;
            font-weight: bold;
            color: red;
        }

        .serial-number {
            display: inline-block;
            background: #0256FF;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            color: #fff;
            line-height: 20px;
            text-align: center;
            margin-right: 10px;
        }

        .pj-input-lossAssessment, .rg-input-lossAssessment {
            width: 60px;
            height: 20px;
            outline: none;
        }
    </style>

    <script type="text/javascript">

        var viewer = null;

        var dataJson = JSON.parse(JSON.stringify(${assessmentReport.reqData!'{}'}));

        var reqJsonData = {};

        const loader =  new Loaders({style:"rectangle"});

        var reg= /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2}))|0)$/;

        var updDataMap = new Map();

        $(function () {

            console.log(dataJson);

            var error = '${errorMsg}';
            if (typeof error != 'undefined' && error.trim() != "") {
                layer.msg(error, {icon: 16, time: 2000, offset: 't', shade: [0.5, '#000'], skin: 'error-class'}, function (index) {
                    layer.close(index);
                    window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                });
            }

            reqJsonData.data = {};
            reqJsonData.data.claimCaseId = "${assessmentReport.claimCaseId}";
            reqJsonData.data.id = "${assessmentReport.id}";

            <#list assessmentFather.assessmentReport as vo>
            updDataMap.set("${vo.rowId}", <#if vo.approvedAmount == "">""<#else>"${vo.approvedAmount}"</#if>);
            </#list>
            updDataMap.set("nuclearLossSum", "${assessmentFather.nuclearLossSum}");

            console.log(updDataMap);


            /*图片展示工具*/
            (function () {
                viewer = new Viewer({
                    activeId: null
                });
            })();


            /*点击缩略图*/
            $('body').on('click','.thumbnail-img',function () {
                if($(this).hasClass('selected-thumbnail-img')){
                    return;
                }
                let attrId = $(this).data('fileid');
                $('.selected-thumbnail-img').removeClass('selected-thumbnail-img');
                $(this).addClass('selected-thumbnail-img');
                viewer.showImageByFileId(attrId);
                let imgNumber = $(this).data('img-number');
                let offset = imgNumber*95-(1050-95)/2;
                $('#thumbnail').scrollTop(offset);
            });

            /*A/D切换影像*/
            $('body').on('click', '.viewer-list > li', function () {
                let attchId = $(this).find('img').attr('alt');
                $('#thumbnail-' + attchId).trigger('click');
            });

            /*获取焦点移除报错样式*/
            $("body").on("focus", "input,textarea,select,.ggLayer", function () {
                document.onkeydown = function (w) {
                }
            });

            /*$('body').on('change', 'input', function () {
                var className = $(this).attr("class");
                if (className == "pj-input-lossAssessment") {
                    let pjSum = new BigDecimal("0");
                    $(".pj-input-lossAssessment").each(function(){
                        pjSum = pjSum.add(new BigDecimal($(this).val())).setScale(2, MathContext.ROUND_HALF_UP);
                    });
                    $("#pj-sum").text(pjSum);
                }
                if (className == "rg-input-lossAssessment") {
                    let rgSum = new BigDecimal("0");
                    $(".rg-input-lossAssessment").each(function(){
                        rgSum = rgSum.add(new BigDecimal($(this).val())).setScale(2, MathContext.ROUND_HALF_UP);
                    });
                    $("#rg-sum").text(rgSum);
                }
            });*/

        });

        function changeApprovedAmount() {
            let nuclearLossSum = new BigDecimal("0");
            $("input[name='approvedAmount']").each(function(){
                var rowId = $(this).closest(".col-sm-12").attr("rowid");
                updDataMap.set(rowId, $(this).val());
                if ($(this).val() != "" && reg.test($(this).val())) {
                    nuclearLossSum = nuclearLossSum.add(new BigDecimal($(this).val())).setScale(2, MathContext.ROUND_HALF_UP);
                }
            });
            nuclearLossSum = nuclearLossSum.subtract(new BigDecimal(dataJson.residualValue != "" ? dataJson.residualValue : "0"));
            $("#nuclearLossSum").text(nuclearLossSum);
            updDataMap.set("nuclearLossSum", nuclearLossSum.toString());
        }


        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layer.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        function seeCaseProcessReason(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看案件流转',
                area: [openWindowWidth,'700px'],
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getBSCaseAssessmentProcessReason?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！",{icon: 2,time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: [openWindowWidth,'700px'],
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

        function getProcessDescription(status, msg) {
            reqJsonData.status = status;
            if (status == 1) {
                reqJsonData.description = "保司驳回此估损清单：" + dataJson.code + "<br><br>";
            } else {
                reqJsonData.description = "保司通过此估损清单：" + dataJson.code + "<br><br>";
            }
            var updDescription = "";
            var resultDescription = "";
            for (const i in dataJson.assessmentReport) {
                var data = dataJson.assessmentReport[i];
                let assessmentName = data.repairName + "：<br>";
                if (data.code == "2-1") {
                    assessmentName = "维修配件--" + assessmentName;
                }
                if (data.code == "2-2") {
                    assessmentName = "维修人工--" + assessmentName;
                }
                // 修改的描述
                if (updDataMap.has(data.rowId)) {
                    if (data.approvedAmount == "" && data.approvedAmount != updDataMap.get(data.rowId)) {
                        updDescription += "设置了" + assessmentName + "<span style='margin-left: 30px'>核损金额：" +  updDataMap.get(data.rowId) + "</span><br>";
                    }
                    if (data.approvedAmount != "" && data.approvedAmount != updDataMap.get(data.rowId)) {
                        updDescription += "修改了" + assessmentName + "<span style='margin-left: 30px'>核损金额：" + data.approvedAmount + "-->" +  updDataMap.get(data.rowId) + "</span><br>";
                    }
                }
                // 结果的描述
                resultDescription += assessmentName;
                resultDescription += "<div style='margin-left: 30px'>名称：" + data.repairName + "<br>";
                resultDescription += "定损金额：" + data.lossAssessment + "<br>";
                resultDescription += "核损金额：" + updDataMap.get(data.rowId) + "</div><br>";
            }
            var residualNuclearLossValue = dataJson.residualNuclearLossValue;
            if (!residualNuclearLossValue) {
                updDescription += "设置了残值核损金额：" + $("#residualNuclearLossValue").val() + "<br>";
            } else if (residualNuclearLossValue != $("#residualNuclearLossValue").val()) {
                updDescription += "修改了残值核损金额：" + residualNuclearLossValue + "-->" + $("#residualNuclearLossValue").val() + "<br>";
            }

            reqJsonData.description += updDescription;      // 添加修改的描述
            reqJsonData.description += "---------------------------------------------------------------------------------------------------------<br><br>";
            reqJsonData.description += resultDescription;      // 添加结果的描述

            reqJsonData.description += "残值：" + dataJson.residualValue + "<br>";
            reqJsonData.description += "残值核损金额：" + $("#residualNuclearLossValue").val() + "<br>";
            reqJsonData.description += "理算金额：" + dataJson.verifyAmout + "<br>";
            reqJsonData.description += "理算描述：" + dataJson.verifyDetail + "<br>";
            reqJsonData.description += "责任比例：" + dataJson.dutyRate + "<br>";
            reqJsonData.description += "扣除费用：" + dataJson.deductFee + "<br>";
            reqJsonData.description += "核损合计：" + $("#nuclearLossSum").text() + "<br>";
            reqJsonData.description += "维修总金额：" + dataJson.lossAssessmentSum + "<br>";
            dataJson.reason = msg;
            if (status == 1) {
                msg = "驳回原因：" + msg;
            } else {
                msg = "通过原因：" + msg;
            }
            reqJsonData.description += "<br>" + msg + "<br>";
            for (const i in dataJson.assessmentReport) {
                var data = dataJson.assessmentReport[i];
                if (updDataMap.has(data.rowId)) {
                    dataJson.assessmentReport[i].approvedAmount = updDataMap.get(data.rowId);
                }
            }
            dataJson.nuclearLossSum = updDataMap.get("nuclearLossSum");                     // 核损合计
            dataJson.residualNuclearLossValue = $("#residualNuclearLossValue").val();     // 残值核损金额
            reqJsonData.data.reqData = dataJson;
            reqJsonData.taskId = "${taskId}";
            console.log(JSON.stringify(reqJsonData));
        }

        // 驳回
        function rejectAssessmentBack() {
            var bool = true;
            $("input").each(function(){
                if (!reg.test($(this).val())) {
                    bool = false;
                }
            });
            if (!bool) {
                layer.msg("核损金额填写有误，必填项！", {icon: 2, time: 1500, offset: 't'}, function (index) {
                    layer.close(index);
                });
                return;
            }
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="6" class="col-sm-6 form-control" name="rejectAssessmentMsg" id="rejectAssessmentMsg" autocomplete="off" placeholder="请输入驳回原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '驳回估损清单',
                area:  ['500px', '250px'],
                btn: ['确认','取消'],
                offset: 't',
                yes: function(index,obj){
                    var rejectAssessmentMsg = $("#rejectAssessmentMsg").val();
                    if(typeof rejectAssessmentMsg != 'string' || rejectAssessmentMsg.trim()==''){
                        layer.msg("驳回原因不能为空", {icon: 2, time: 3000,offset: 't'});
                    }else {
                        getProcessDescription(1, rejectAssessmentMsg);
                        $.ajax({
                            url: "${ctx}/assessmentReportController/assessmentReportAudit",
                            type: 'POST',
                            data: JSON.stringify(reqJsonData),
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                loader.show();
                                if (result.ret == "0") {
                                    setTimeout(function(){
                                        layer.msg(result.msg, {
                                            icon: 1,
                                            time: 2000, //1秒关闭（如果不配置，默认是3秒）
                                            offset: 't'
                                        }, function () {
                                           window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                                        });
                                    },1000);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500, //1秒关闭（如果不配置，默认是3秒）
                                        offset: 't'
                                    }, function (index) {
                                        window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        // 通过
        function assessmentPass() {
            var bool = true;
            let nuclearLossSum = new BigDecimal($("#nuclearLossSum").text().trim() != "" ? $("#nuclearLossSum").text().trim() : "0");
            $("input").each(function(){
                if (!reg.test($(this).val())) {
                    bool = false;
                }
            });
            if (!bool) {
                layer.msg("核损金额填写有误，必填项！", {icon: 2, time: 1500, offset: 't'}, function (index) {
                    layer.close(index);
                });
                return;
            }
            let lossAssessmentSum = new BigDecimal(dataJson.lossAssessmentSum != "" ? dataJson.lossAssessmentSum : "0");
            if (nuclearLossSum.compareTo(lossAssessmentSum) != 0) {
                layer.msg("核损合计与维修总金额不一致！", {icon: 2, time: 1500, offset: 't'}, function (index) {
                    layer.close(index);
                });
                return;
            }
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="6" class="col-sm-6 form-control" name="assessmentPassMsg" id="assessmentPassMsg" autocomplete="off" placeholder="请输入通过原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '通过估损清单',
                area:  ['500px', '250px'],
                btn: ['确认','取消'],
                offset: 't',
                yes: function(index,obj){
                    var assessmentPassMsg = $("#assessmentPassMsg").val();
                    getProcessDescription(2, assessmentPassMsg);
                    $.ajax({
                        url: "${ctx}/assessmentReportController/assessmentReportAudit",
                        type: 'POST',
                        data: JSON.stringify(reqJsonData),
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            loader.show();
                            if (result.ret == "0") {
                                setTimeout(function(){
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000, //1秒关闭（如果不配置，默认是3秒）
                                        offset: 't'
                                    }, function () {
                                        window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                                    });
                                },1000);
                            } else {
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 1500, //1秒关闭（如果不配置，默认是3秒）
                                    offset: 't'
                                }, function (index) {
                                    window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            });
        }


        // 跳过
        function skipAssessmentReportTask(id) {
            $.ajax({
                url: "${ctx}/claimCaseController/skipAssessmentReportTask?id=" + id,
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    loader.show();
                    if (result.ret == "0") {
                        setTimeout(function(){
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 2000, //1秒关闭（如果不配置，默认是3秒）
                                offset: 't'
                            }, function () {
                                window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                            });
                        },1000);
                    } else {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 1500, //1秒关闭（如果不配置，默认是3秒）
                            offset: 't'
                        }, function (index) {
                            window.location.href = "${ctx}/assessmentReportController/assessmentReportList";
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }
    </script>
</head>


<body id="qc-Body" style="overflow-x:hidden;background: #fff;">
<div class="container-fluid">
    <div class="row">
        <form id="inputForm" onsubmit="return false">
            <div class="row">
                <div class="col-sm-9">
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">
                        <div class="col-sm-3" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                案件号：${assessmentReport.claimCaseNo}
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                姓名：${assessmentReport.treatName}
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                是否延迟报立案：<#if claimCase.label?contains('Aax006')>是<#else>否</#if>
                            </div>
                        </div>
                    </div>
                    <#-- 按钮 -->
                    <div class="col-sm-12" style="margin-bottom: 5px;padding-left: 0px;padding-right: 0px;">

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <div class="col-sm-12" style="padding-left: 0px;">
                                <button type="button" class="btn genTask btn-look" onclick="seePlanName('${product.id!''}')">
                                    查看产品方案
                                </button>
                            </div>
                        </div>

                        <div class="col-sm-2" style="padding-left: 0px;">
                            <button type="button" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')"  class="btn genTask" >
                                历史案件
                            </button>
                        </div>

                        <div class="col-sm-2">
                            <button type="button" class="btn genTask circulation_reasons" onclick="seeCaseProcessReason('${claimCase.id}')"> 查看案件流转原因</button>
                        </div>

                    </div>
                    <#if assessmentReport.status != 2 && assessmentFather.reason != "">
                        <div class="col-sm-6 col-sm-offset-1" style="color: red;font-weight: bold;font-size: 15px">
                            驳回原因：${assessmentFather.reason} （最新一次驳回原因）
                        </div>
                    </#if>
                </div>
                <div class="col-sm-3">
                    <div class="col-sm-12 pull-right" style="margin-top: 20px">
                        <#if !isShow?exists >
                            <button class="btn green col-sm-3" style="margin-right: 20px" onclick="skipAssessmentReportTask('${assessmentReport.id}')">跳过</button>
                            <button class="btn blue col-sm-3" style="margin-right: 20px" onclick="assessmentPass()">通过</button>
                            <button class="btn btn-danger col-sm-3" onclick="rejectAssessmentBack()">驳回</button>
                        </#if>
                    </div>
                </div>
            </div>
            <!-- 左部分 -->
            <div class="col-sm-8" style="background: none!important;padding-right: 0px !important;">
                <#--审核头-->
                <div class="row">
                    <#-- 展示图片区域以及采集数据tbale -->
                    <div class="col-sm-12" style="padding: 5px 0px 0px 0px; background: none;">
                        <div class="row" style="height: 1050px; overflow: hidden;">
                            <div class="col-sm-1" style="height: 1050px;background-color: rgba(226, 226, 226, 0.5);padding-left: 0px!important;padding-right: 10px!important; border-right: groove;">
                                <#if claimAttachList?exists && (claimAttachList?size>0)>
                                    <div id="thumbnail" style="height: 1050px;<#if (claimAttachList?size>11)>overflow-y: scroll;</#if>overflow-x:hidden">
                                        <ol style="padding-left:25px !important;/*padding-right: 15px !important;*/">
                                            <#list claimAttachList as attach>
                                                <li style="width:auto; height:auto; cursor:pointer;text-align: center;padding-top:5px">
                                                    <img id="thumbnail-${attach.id}" class="thumbnail-img <#if attach_index == 0>selected-thumbnail-img</#if>"
                                                         data-fileid="${attach.id}" data-img-number=''
                                                         title="${attach.fileName}"
                                                         src="${attach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                                </li>
                                            </#list>
                                        </ol>
                                    </div>
                                </#if>
                            </div>
                            <div class="col-sm-11" style="height: 1300px;">
                                <ul id="images">
                                    <#list claimAttachList as attach>
                                        <li hidden="hidden">
                                            <img data-fileid="${attach.id}"
                                                 title="${attach.fileName}"
                                                 src="${attach.fileObjectId}"
                                                 alt="${attach.id}"/>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右部分 -->
            <div class="col-sm-4" style="background: none!important;padding-left: 0px;max-height: 900px;overflow-y: scroll;overflow-x: hidden;">
                <div style="text-align: center;margin-bottom: 10px;">
                    <span style="font-size: 16px;font-weight: bold;margin-left: 10px">估损清单名称：${assessmentReport.code} </span>
                </div>
                <div class="row">
                    <div style="margin-left: 5px;margin-bottom: 15px">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 10px">机动车基本信息 </span>
                    </div>
                    <div class="row" style="margin-bottom: 30px;">
                        <div id="dataCollectArea" class="row" style="padding-left: 20px;">
                            <div class="row" style="font-weight: bold;padding-left: 10px;line-height: 30px">
                               <div class="row">
                                   <span style="margin-right: 30px">车牌号码：${assessmentFather.carNumber}</span>
                                   <span>厂牌型号：${assessmentFather.carModel}</span>
                               </div>
                                <div class="row">
                                    <span style="margin-right: 30px">车辆识别代码：${assessmentFather.carEncoding}</span>
                                    <span>初次登记日期：${(assessmentFather.firstRegistrationTime?string["yyyy/MM/dd"])!''}</span>
                                </div>
                                <div class="row">
                                    <span style="margin-right: 30px">行驶证车主：${assessmentFather.carOwner}</span>
                                    <span>定损时间：${(assessmentFather.lossAssessmentTime?string["yyyy/MM/dd"])!''}</span>
                                </div>
                                <div class="row">
                                    <span style="margin-right: 30px">修理厂：${assessmentFather.repairFactory}</span>
                                    <span>是否4S：${assessmentFather.is4S}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div style="margin-left: 5px;">
                        <img class="marginLeft15" src="${ctx}/images/u48.svg" align="left" style="margin-top: 3px;"/>
                        <span style="font-size: 16px;font-weight: bold;margin-left: 10px">维修定损 </span>
                    </div>
                    <div class="row" style="margin-bottom: 30px" >
                        <div class="row" style="padding-left: 20px">
                            <div class="row" style="font-weight: bold;padding-left: 10px;line-height: 30px;margin: 20px 0px;">
                                <div class="col-sm-12">
                                    <span style="font-size: 20px;font-weight: lighter">维修配件</span>
                                </div>
                                <div class="col-sm-12">
                                    <div class="row">
                                        <#assign pjSum = 0>
                                        <#assign pjIndex = 0>
                                        <#list assessmentFather.assessmentReport as vo>
                                            <#if vo.code == "2-1">
                                            <#assign pjSum = pjSum + vo.lossAssessment>
                                            <#assign pjIndex = pjIndex + 1>
                                            <div class="col-sm-12" style="display: flex;justify-content: space-between;" rowid="${vo.rowId}">
                                                <div>
                                                    <span class="serial-number">${pjIndex}</span>
                                                    名称：${vo.repairName}
                                                </div>
                                                <div >
                                                    定损金额：${vo.lossAssessment}元
                                                </div>
                                                <div>
                                                    核损金额：<input type="text" value="${vo.approvedAmount}" name="approvedAmount" onchange="changeApprovedAmount()" class="form-control" style="width: 80px;height: 24px;display: inline-block;padding: 3px 5px;">
                                                </div>
                                            </div>
                                            </#if>
                                        </#list>
                                        <div class="col-sm-12 " style="color: red;text-align: right">
                                            配件金额合计：<span id="pj-sum">${pjSum}</span> 元
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="font-weight: bold;padding-left: 10px;line-height: 30px;margin: 20px 0px;">
                                <div class="col-sm-12">
                                    <span style="font-size: 20px;font-weight: lighter">维修人工</span>
                                </div>
                                <div class="col-sm-12">
                                    <div class="row">
                                        <#assign rgSum = 0>
                                        <#assign rgIndex = 0>
                                        <#list assessmentFather.assessmentReport as to>
                                            <#if to.code == "2-2">
                                                <#assign rgSum = rgSum + to.lossAssessment>
                                                <#assign rgIndex = rgIndex + 1>
                                                <div class="col-sm-12" style="display: flex;justify-content: space-between;" rowid="${to.rowId}">
                                                    <div>
                                                        <span class="serial-number">${rgIndex}</span>
                                                        名称：${to.repairName}
                                                    </div>
                                                    <div >
                                                        定损金额：${to.lossAssessment}元
                                                    </div>
                                                    <div>
                                                        核损金额：<input type="text" value="${to.approvedAmount}" name="approvedAmount" onchange="changeApprovedAmount()" class="form-control" style="width: 80px;height: 24px;display: inline-block;padding: 3px 5px;">
                                                    </div>
                                                </div>
                                            </#if>
                                        </#list>
                                        <div class="col-sm-12 " style="color: red;text-align: right" >
                                            维修人工合计：<span id="rg-sum">${rgSum}</span> 元
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="margin: 15px;">
                        <span style="font-size: 24px;font-weight: bold">残值：${(assessmentFather.residualValue?string('0.00'))!''} 元</span><br>
                        <div style="display: flex;">
                        <span style="font-size: 24px;font-weight: bold;">
                            残值核损金额：
                        </span>
                        <input type="text" class="form-control" style="width: 260px;padding: 3px 5px;font-size: 24px;font-weight: bold;" name="residualNuclearLossValue" id="residualNuclearLossValue" value="${assessmentFather.residualNuclearLossValue}" >
                        </div>
                        <span style="font-size: 24px;font-weight: bold">理算金额：${(assessmentFather.verifyAmout?string('0.00'))!''} 元</span><br>
                        <span style="font-size: 24px;font-weight: bold">理算描述：${assessmentFather.verifyDetail}</span><br>
                        <span style="font-size: 24px;font-weight: bold">责任比例：${assessmentFather.dutyRate} %</span><br>
                        <span style="font-size: 24px;font-weight: bold">扣除费用：${(assessmentFather.deductFee?string('0.00'))!''} 元</span><br>
                        <span style="font-size: 24px;color: red;font-weight: bold">维修总金额：${(assessmentFather.lossAssessmentSum?string('0.00'))!''} 元</span><br>
                        <span style="font-size: 24px;color: red;font-weight: bold">核损合计：<span id="nuclearLossSum">${(assessmentFather.nuclearLossSum?string('0.00'))!''}</span> 元</span><br>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
