<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type").select2({
                placeholder: "请选择",
                width: null
            });
        });

        
        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }

        function startAudit(id) {
            window.location.href = "${ctx}/claimCaseController/startBSAudit?assessmentReportId="+id;
        }

        function showAssessement(id) {
            window.location.href = "${ctx}/claimCaseController/showAssessement?assessmentReportId="+id;
        }


    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
    </style>
</head>
<body>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">保司审核</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/assessmentReportController/assessmentReportList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${assessmentReportSup.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${assessmentReportSup.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${assessmentReportSup.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">估损表类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control" name="type" id="type" value="">
                                           <option value="">请选择</option>
                                           <#list typeMap.keySet() as key>
                                               <option value="${key}" <#if key == assessmentReportSup.type> selected </#if>>${typeMap.get(key)}</option>
                                           </#list>
                                       </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                    <button type="button" class="btn btn-info" style="margin-left: 10px;" onclick="startAudit('')">开始审核</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${assessmentReportReq.status}">
                            <ul style="padding: 0px;margin: 0px;display: flex">
                                <li class="li-default <#if assessmentReportReq.status == "">li-blue</#if>" onclick="statusSwitch()">全部
                                </li>
                                <li class="li-default <#if assessmentReportReq.status == 0>li-blue</#if>" onclick="statusSwitch(0)">待处理
                                </li>
                                <li class="li-default <#if assessmentReportReq.status == 1>li-blue</#if>" onclick="statusSwitch(1)">驳回池
                                </li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="12%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="15%">出险人身份证</th>
                        <th width="12%">估损表类型</th>
                        <th width="12%">定损/维修总金额</th>
                        <th width="12%">操作人</th>
                        <th width="12%" >创建时间</th>
                        <th width="15%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.treatName}</td>
                            <td title="">${vo.treatIdNum}</td>
                            <td>
                                ${typeMap.get(vo.type+"")}
                            </td>
                            <td>
                                ${(((vo.reqData!'{}')?eval).lossAssessmentSum)!'-'}
                            </td>

                            <td>${vo.auditer}</td>
                            <td>${vo.createTime?string('yyyy-MM-dd')}</td>

                            <td>
                                <#if vo.status == 0>
                                    <a href="#" onclick="startAudit('${vo.id}')">加急审核</a>
                                </#if>
                                <#if vo.status != 0>
                                    <a href="#" onclick="showAssessement('${vo.id}')">查看</a>
                                </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>