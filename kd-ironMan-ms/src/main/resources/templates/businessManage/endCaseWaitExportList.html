<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#type").select2({
                placeholder: "请选择",
                width: null
            });

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
            });
        });

        //时间格式化
        function timeStamp2String(time) {
            var datetime = new Date();
            datetime.setTime(time);
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
            var hour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
            var minute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
            var second = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
            return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
        }

        // 查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId+"&type=1";
        }
        // 导出案件
        function openExportCaseInfo(caseId) {
            $("#push-elm-log").html("");
            $("#showLabel").html("");
            $('#exportCaseInfoContainer').find('span.checked').removeClass("checked");
            var formData = new FormData();
            formData.append("claimCaseId", caseId);
            $.ajax({
                url: "${ctx}/claimCaseController/getPushElmLog",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        var data = eval("(" + result.msg + ")");
                        $.each(data.claimCaseLogList, function(index, log) {
                            var trs = `<tr class="rowInfo">
                                            <td width="10%" align="center">
                                                <div class="icon-plus"></div>
                                            </td>
                                            <td width="20%">`+log.position+`</td>
                                            <td width="20%">`+log.status+`</td>
                                            <td width="20%">`+log.creator+`</td>
                                            <td width="30%">`+timeStamp2String(log.createTime)+`</td>
                                        </tr>
                                        <tr class="detailsInfo">
                                            <td width="10%" align="center"></td>
                                            <td width="90%" colspan="4" style="overflow-x: visible;">
                                                请求参数：`+log.reqData+`<br>
                                                响应结果：`+log.resData+`<br>
                                            </td>
                                        </tr>`;
                            $("#push-elm-log").append(trs);
                        });

                        if(data.labelList){
                            var str = ``;
                            for(let labelInfo of data.labelList){
                                str += `<span  style="color: `+labelInfo.fontColor+`;background-color: `+labelInfo.color+`" >`+labelInfo.msg+`</span>`;
                                str += `&nbsp&nbsp&nbsp`;
                            }
                            $("#showLabel").append(str);
                        }
                    }else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        }, function (index) {
                            layer.close(index);
                        });
                        return;
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result);
                }
            });

            $("#modifyExportCaseStatus").attr("data-modify-status-case-id",caseId);
            layer.open({
                title: "导出案件信息",
                type: 1,
                content: $('#exportCaseInfoContainer'),
                area: ['900px', '500px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                yes: function (index, layero) {
                    let check = $('#exportCaseInfoContainer').find('span.checked');
                    let checkChoose = [];
                    let reqStr = "claimCaseIdList="+caseId;
                    $.each(check,function (index,obj) {
                        let children = $(this).children();
                        let code = children.attr("code");
                        checkChoose.push(code);
                        reqStr+="&"+code+"=1";
                    });
                    if(checkChoose.length==0){
                        layer.msg("请选择导出案件信息类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    window.location.href="${ctx}/claimCasePEController/downloadCaseAttach?"+reqStr;
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        //案件导出数据后修改状态
        function modifyExportCaseStatus(obj) {
            let caseId = $("#modifyExportCaseStatus").attr("data-modify-status-case-id");
            layer.confirm("是否修改状态！！！", function (index) {
                var formData = new FormData();
                formData.append("claimCaseId", caseId);
                $.ajax({
                    url: "${ctx}/claimCasePEController/modifyExportCaseStatus",
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret != "0") {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 1500
                            }, function (index1) {
                                layer.close(index1);
                            });
                        }else {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 3000
                            }, function (index1) {
                                window.location.reload();
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                    }
                });
            })
        }

    </script>
    <style>

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }
        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .logListInfo .rowInfo:hover{
            cursor: pointer;
        }
    </style>
</head>
<body>
<#--导出案件信息-->
<div id="exportCaseInfoContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择导出类型：</span>
        </div>
        <div class="col-sm-9">
            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
                <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedAttach"
                       type="checkbox" id="isNeedAttach"><label for="isNeedAttach">导出影像</label>
            </div>
            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
                <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedExcel"
                       type="checkbox" id="isNeedExcel"><label for="isNeedExcel">导出文件</label>
            </div>
        </div>
    </div>
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">标签：</span>
        </div>
        <div id="showLabel" class="col-sm-9">

        </div>
    </div>
    <div class="row" style="padding: 15px 30px;margin: 0px 0px">
        <button type="button" class="btn pull-right" style="background-color: #1676FF;color: white"
                id="modifyExportCaseStatus" onclick="modifyExportCaseStatus(this)">
            修改状态
        </button>
    </div>
    <div class="row logListInfo" style="margin-left: 20px;margin-right: 20px;">
        <div class="block-head-label col-sm-12" style="margin-bottom: 20px;">
            <span class="label-title">日志信息</span>
        </div>
        <div class="col-sm-12">
            <table class="table">
                <thead>
                <tr>
                    <td width="10%"></td>
                    <td width="20%">岗位</td>
                    <td width="20%">类型</td>
                    <td width="20%">人员</td>
                    <td width="30%">时间</td>
                </tr>
                </thead>
                <tbody id="push-elm-log">
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>高德</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">案件导出</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/endCaseWaitExportList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>


                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保险公司：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="insCode"
                                                id="insCode" >
                                            <option value="">请选择</option>
                                            <#if insCodeMap?exists>
                                                <#list insCodeMap.keySet() as key>
                                                    <option value="${key}" <#if claimCaseVo.insCode==key>selected</#if>>${insCodeMap.get(key)}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="15%">报案号</th>
                        <th width="10%">承保公司</th>
<!--                        <th width="10%">案件类型</th>-->
                        <th width="10%">赔付金额</th>
                        <th width="15%">状态</th>
                        <th width="20%">创建时间</th>
                        <th width="20%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="${vo.claimCaseNo}">${vo.claimCaseNo}</td>
                            <td>
                                <#if vo.insCode == 'DD'>
                                    <span style="display:inline-block;background-color: #26c281;color: #fff;padding: 2px 6px;border-radius: 2px;">大地</span>
                                </#if>
                            </td>
<!--                            <td title="${caseTypeMap.get(vo.caseType)!'未分类'}">${caseTypeMap.get(vo.caseType)!'未分类'}</td>-->
                            <td title="${vo.payAmount}">${vo.payAmount}</td>
                            <td title="${claimCaseStatusEumMap.get(vo.status).msg}">${claimCaseStatusEumMap.get(vo.status).msg}</td>
                            <td title="${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}">${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <a onclick="caseDetail('${vo.id}')">查看详情</a>
                                <@shiro.hasPermission name="EXPROT_CASE_INFO">
                                    <a onclick="openExportCaseInfo('${vo.id}')">导出案件信息</a>
                                </@shiro.hasPermission>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>