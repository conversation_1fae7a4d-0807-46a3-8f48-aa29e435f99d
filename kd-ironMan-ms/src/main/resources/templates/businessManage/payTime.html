<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>支付时间</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script type="text/javascript">
        var callbackdata = function () {
            var data = {
                paytime: $("#paytime").val(),
                payAmount: $("#payAmount").val()
            };
            return data;
        }
    </script>
    <style>
        .textareaStyle{
            height: 106px;
            width: 306px;
            margin-top: 20px;
            margin-bottom: 50px;
            margin-left: 100px;
        }
    </style>
</head>
<body >
    <div>
        <div class="block-show">
            <div class="form-group" style="margin-top: 20px;display: flex;align-items: center;">
                <label class="control-label col-sm-2 " style="padding-right: 0;text-align: right;">支付时间：</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="paytime" id="paytime" autocomplete="off" placeholder="yyyy-MM-dd HH:mm:ss">
                </div>
            </div>
            <div class="form-group" style="margin-top: 20px;display: flex;align-items: center;">
                <label class="control-label col-sm-2 " style="padding-right: 0;text-align: right;">支付金额：</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="payAmount" id="payAmount" autocomplete="off" placeholder="$.$$">
                </div>
            </div>
        </div>
    </div>
</body>
</html>
