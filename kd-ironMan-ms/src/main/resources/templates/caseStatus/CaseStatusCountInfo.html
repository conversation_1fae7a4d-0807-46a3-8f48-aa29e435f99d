<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        function findObjectListByStatus(status) {

            if(status!='null'){
                var _status=[status];
            }else{
                var _status=['BAX24','BAX27','BAX34','BAX37'];
            }
            window.location.href = "${ctx}/caseStatusCountInfoDetailController/findClaimCaseObjectsByStatus?status=" + _status
        }
        function findOtherObjectListByStatus() {

            var _status=['BAX24','BAX27','BAX34','BAX37','BAX10','BAX21','BAX22','BAX25','BAX31','BAX32','BAX35','BAX99'];
            window.location.href = "${ctx}/caseStatusCountInfoDetailController/findUknownClaimCaseObjectsByStatus?status=" + _status
        }
        function findCaseListByStatus(status) {
            var _status=[status];
            window.location.href = "${ctx}/caseStatusCountInfoDetailController/findClaimCaseByStatus?status=" + _status
        }
        function findExceptionCaseObjectsByStatus(status) {
            var _status=[status];
            window.location.href = "${ctx}/caseStatusCountInfoDetailController/findExceptionCaseObjectsByStatus?status=" + _status
        }

    </script>
    <style>
        .border{
            border: 1px solid #1676FF;
            border-radius:10px;
        }
        .flex{
            display: flex;

        }
        .box{
            margin:10px 0 10px 20px;
            width: 22%;
            height: 83px;
            text-align: center;
            padding-top: 15px;
            cursor: pointer;
        }
        .box > span{
            display: block;
            padding-top: 10px;
        }
        .border > div{
            font-size: 16px;
            font-font: '黑体';
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">我的任务</span></li>
                </ul>
            </div>
            <div class="portlet-body flex" >
                <div class="border" style="width: 70%;padding-bottom: 10px;border-color: #1676FF">
                    <div style="margin:10px 0 0 20px;font-size: 24px;font-weight: bold;">进行中案件数：${all_case} (案件数)</div>
                    <div class="flex">
                        <div class="border box" onclick="findCaseListByStatus('aax20')">
                            报案审核
                            <span>${case_aax20}</span>
                        </div>
                        <div class="border box" onclick="findCaseListByStatus('abx10')">
                            估损/理算审核
                            <span>${case_abx10}</span>
                        </div>
                        <div class="border box" onclick="findCaseListByStatus('acx10')">
                            待推送
                            <span>${case_acx10}</span>
                        </div>
                        <div class="border box" onclick="findCaseListByStatus('acx21')">
                            待回盘
                            <Span>${case_acx21}</Span>
                        </div>
                    </div>
                    <div class="border" style="width: 95%;margin: 0 auto;">
                        <div style="margin:10px 0 0 20px">估损/理算审核(赔付对象为单位)</div>
                        <div class="flex">
                            <div class="border box" onclick="findObjectListByStatus('BAX10')">
                                待分配
                                <span>${case_BAX10}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX21')">
                                估损待提交
                                <span>${case_BAX21}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX22')">
                                估损内部审核
                                <span>${case_BAX22}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('null')">
                                驳回
                                <span>${reject}</span>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="border box" onclick="findObjectListByStatus('BAX25')">
                                估损保司审核
                                <span>${case_BAX25}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX31')">
                                理算待提交
                                <span>${case_BAX31}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX32')">
                                理算内部审核
                                <span>${case_BAX32}</span>
                            </div>
                            <div class="border box" onclick="findObjectListByStatus('BAX35')">
                                理算保司审核
                                <span>${case_BAX35}</span>
                            </div>
                        </div>
                        <div class="border box" onclick="findOtherObjectListByStatus()">
                            未知状态
                            <span>${unknown_status}</span>
                        </div>
                    </div>
                </div>
                <div style="width: 15%;">
                    <div class="border box " onclick="findExceptionCaseObjectsByStatus('BAX99')" style="width: 100%;height:100px;margin-left: 20px;padding-top:20px;border-color: #EA0000 ;font-size: 16px;font-font: '黑体';">
                        异常案件数
                        <span>${case_exceptions}</span>
                    </div>
                    <div class="border box " onclick="findCaseListByStatus('aax21')" style="width: 100%;height:100px;margin-left: 20px;padding-top:20px;border-color: #F66333 ;font-size: 16px;font-font: '黑体';">
                        初审挂起
                        <span>${case_aax21}</span>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
</body>
</html>