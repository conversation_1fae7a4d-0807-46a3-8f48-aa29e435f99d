<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <#include "/common/cssResource.html">
  <#include "/common/jsResource.html">
  <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
  <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

  <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
  <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
  <script type="text/javascript">
    function startAudit2(flag,status) {
      status=status.replace('[','');
      status=status.replace(']','');
      window.location.href = "${ctx}/caseStatusCountInfoDetailController/compensationItemsExport?flag="+flag+"&status="+status;
    }
  </script>
  <style>

    .operator{
      height: 34px;
      background-color: rgb(255, 255, 255);
      box-shadow: rgba(0, 0, 0, 0.075) 0px 1px 1px inset;
      padding: 6px 12px;
      border-width: 1px;
      border-style: solid;
      border-color: rgb(194, 202, 216);
      border-image: initial;
      border-radius: 4px;
      transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    }
  </style>
</head>
<body>
  <div  class="portlet-body">
    <div >

      <form id="exportForm" class="form-horizontal" action="${ctx}/caseStatusCountInfoDetailController/compensationItemsExport" method="post">
<#--      <form id="searchForm" class="form-horizontal" action="${ctx}/caseStatusCountInfoDetailController/compensationItemsExport" method="post">-->
        <input type="hidden" name="flag" value="${flag}"/>
        <input type="hidden" name="status" value="${status}"/>
        <button type="submit" class="btn  btn-info" style="float: right;margin-left: 20px;">清单导出
        </button>
      </form>
      <form id="searchForm" class="form-horizontal" action="${ctx}/caseStatusCountInfoDetailController/findClaimCaseObjectsBySeaerchName" method="post">
        <div class="col-sm-3">
          <label  style="padding-right: 0">操作人: </label>
          <input type="text" class="operator" name="name" id="operator" value="" placeholder="请输入" >
        </div>
        <input type="hidden" name="status" value="${status}"/>
        <button type="submit" class="btn  btn-info" style="float: right;">搜索
        </button>
      </form>
<#--        <button type="submit" class="btn  btn-info" style="float: right;">清单导出-->
<#--        </button>-->

        <button type="button" class="btn  btn-info" style="margin-bottom: 10px;margin-top: 20px" onclick="startAudit2('${flag}','${status}')">导出</button>
<#--      </form>-->
<!--      <button class="btn btn-info" style="float: right;" onclick="compensationItemsExport()">清单导出</button>-->
    </div>
    <table class="table table-striped table-bordered table-hover table-header-fixed">
      <thead>
        <tr>
          <td>案件号</td>
          <td>渠道</td>
          <td>项目名称</td>
          <td>操作人</td>
          <td>内部审核人</td>
          <td>保司审核人</td>
          <td>创建时间</td>
        </tr>
      </thead>
      <tbody>
      <#list list as vo>
        <tr>
          <td>${vo.claimCaseNo}</td>
          <td>${vo.insCode!'--'}</td>
          <td>${vo.name!'--'}</td>
          <td>${getAuditer[vo.auditer]!'--'}</td>
          <td>${getCheckAuditer[vo.checkAuditer]!'--'}</td>
          <td>${getCheckAuditer[vo.insAuditer]!'--'}</td>
          <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
        </tr>
      </#list>
      </tbody>
    </table>
  </div>
</body>
</html>