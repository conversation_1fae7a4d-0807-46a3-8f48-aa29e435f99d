<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <#include "/common/cssResource.html">
  <#include "/common/jsResource.html">
  <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
  <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

  <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
  <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
  <script type="text/javascript">
    //查看详情
    function caseDetail(claimCaseId) {
      window.location.href="${ctx}/claimCaseController/caseDetail4BS?caseId="+claimCaseId;
    }
    //时间格式化
    function timeStamp2String(time) {
      var datetime = new Date();
      datetime.setTime(time);
      var year = datetime.getFullYear();
      var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
      var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
      var hour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
      var minute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
      var second = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
      return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
    }

    // 导出案件
    function openExportCaseInfo(caseId) {
      $("#push-elm-log").html("");
      $('#exportCaseInfoContainer').find('span.checked').removeClass("checked");
      var formData = new FormData();
      formData.append("claimCaseId", caseId);
      $.ajax({
        url: "${ctx}/insuranceCaseController/getPushElmLog",
        type: 'POST',
        data: formData,
        async: false,
        cache: false,
        contentType: false,
        processData: false,
        success: function (data) {
          var result = eval("(" + data + ")");
          if (result.ret == "0") {
            var data = eval("(" + result.msg + ")");
            $.each(data, function(index, log) {
              var trs = `<tr class="rowInfo">
                                            <td width="10%" align="center">
                                                <div class="icon-plus"></div>
                                            </td>
                                            <td width="20%">`+log.position+`</td>
                                            <td width="20%">`+log.status+`</td>
                                            <td width="20%">`+log.creator+`</td>
                                            <td width="30%">`+timeStamp2String(log.createTime)+`</td>
                                        </tr>
                                        <tr class="detailsInfo">
                                            <td width="10%" align="center"></td>
                                            <td width="90%" colspan="4" style="overflow-x: visible;">
                                                请求参数：`+log.reqData+`<br>
                                                响应结果：`+log.resData+`<br>
                                            </td>
                                        </tr>`;
              $("#push-elm-log").append(trs);
            });
          }else {
            layer.msg(result.msg, {
              icon: 2,
              time: 2000
            }, function (index) {
              layer.close(index);
            });
            return;
          }
        },
        error: function (data) {
          var result = eval("(" + data + ")");
          alert(result);
        }
      });

      $("#modifyExportCaseStatus").attr("data-modify-status-case-id",caseId);
      layer.open({
        title: "导出案件信息",
        type: 1,
        content: $('#exportCaseInfoContainer'),
        area: ['900px', '500px'],
        fixed: false,
        offset: 't',
        btn: ['确认', '取消'],
        yes: function (index, layero) {
          let check = $('#exportCaseInfoContainer').find('span.checked');
          let checkChoose = [];
          let reqStr = "claimCaseIdList="+caseId;
          $.each(check,function (index,obj) {
            let children = $(this).children();
            let code = children.attr("code");
            checkChoose.push(code);
            reqStr+="&"+code+"=1";
          });
          if(checkChoose.length==0){
            layer.msg("请选择导出案件信息类型！！！", {
              icon: 2,
              time: 2000
            });
            return;
          }
          window.location.href="${ctx}/downloadCenterController/downloadCaseAttach?"+reqStr;
          layer.close(index);
        },
        btn2: function (index, layero) {
          layer.closeAll();
        }
      });
    }


    // 推送大地
      function claimDataPush(id) {
        layer.confirm("是否确认推送？", {icon: 3, title: '温馨提示', offset: ['5%','30%']}, function (index) {
          $.ajax({
            url: "${ctx}/claimCaseController/claimDataPush?claimCaseId=" + id,
            type: 'POST',
            async: true,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
              var result = eval("(" + data + ")");
              if (result.ret == "0") {
                layer.msg(result.msg, {icon: 1, time: 1500, offset: ['5%','30%']}, function (index) {
                  window.location.reload();
                  layer.close(index);
                });
              } else {
                layer.msg(result.msg, {icon: 2, time: 1500, offset: ['5%','30%']}, function (index) {
                  layer.close(index);
                });
              }
            },
            error: function (data) {
              var result = eval("(" + data + ")");
              alert(result.msg);
            }
          });
        });
      }

    function casePushELM(id) {

      var openWindowWidth = $(document).width() * 0.5 + "px";
      var offsetH = ($(window).height() / 5 - 20 > 360 ? 120 : $(window).height() / 5 - 20) + "px";
      layer.open({
        type: 2,
        title: '支付时间',
        area: openWindowWidth,
        offset: offsetH,
        shadeClose: false,
        fix: false, //不固定
        btn: ['确定', '取消'], //按钮
        maxmin: true,
        btn1: function (index, layero) {
          //当点击‘确定'按钮的时候，获取弹出层返回的值
          var res = window["layui-layer-iframe" + index].callbackdata();
          if (res.paytime.trim() && res.payAmount.trim()) {
            //打印返回的值，看是否有我们想返回的值。
            var formData = new FormData();
            formData.append("id", id);
            formData.append("payTimeStr", res.paytime);
            formData.append("payAmountStr", res.payAmount);
            $.ajax({
              url: "${ctx}/claimCaseController/casePushELM",
              type: 'POST',
              data: formData,
              async: true,
              cache: false,
              contentType: false,
              processData: false,
              success: function (data) {
                var result = eval("(" + data + ")");
                if (result.ret == "0") {
                  layer.msg(result.msg, {
                    icon: 1,
                    time: 2000
                  },function () {
                    window.location.reload();
                  });
                } else {
                  layer.msg(result.msg, {
                    icon: 2,
                    time: 2500 //1秒关闭（如果不配置，默认是3秒）
                  });
                }
              },
              error: function (data) {
                var result = eval("(" + data + ")");
                alert(result.msg);
              }
            });
            //最后关闭弹出层
            layer.close(index);
          } else {
            alert("支付时间与支付金额不能为空");
          }

        },
        content: '${ctx}/claimCaseController/payTime'
      });
    }

    //案件导出数据后修改状态
    function modifyExportCaseStatus(obj) {
      let caseId = $("#modifyExportCaseStatus").attr("data-modify-status-case-id");
      layer.confirm("是否修改状态！！！", { offset:['5%','30%']},  function (index) {
        var formData = new FormData();
        formData.append("claimCaseId", caseId);
        $.ajax({
          url: "${ctx}/insuranceCaseController/modifyExportCaseStatus",
          type: 'POST',
          data: formData,
          async: false,
          cache: false,
          contentType: false,
          processData: false,
          success: function (data) {
            var result = eval("(" + data + ")");
            if (result.ret != "0") {
              layer.msg(result.msg, {
                icon: 2,
                time: 1500
              }, function (index1) {
                layer.close(index1);
              });
            }else {
              layer.msg(result.msg, {
                icon: 1,
                time: 3000
              }, function (index1) {
                window.location.reload();
              });
            }
          },
          error: function (data) {
            var result = eval("(" + data + ")");
          }
        });
      })
    }


    //查看日志
    function seeCaseProcessReason(claimCaseId) {
      var openWindowWidth = $(document).width() * 0.8 + "px";
      var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
      layer.open({
        type: 2,
        title: '查看日志',
        area: openWindowWidth,
        offset: offsetH,
        fix: false, //不固定
        maxmin: true,
        content: "${ctx}/insuranceCaseController/getCaseProcessReason?claimCaseId=" + claimCaseId,
        success: function (layero, index) {
          layer.iframeAuto(index);
        }
      });
    }
  </script>
  <style>
    .form-active > div {
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      border: 1px solid #e7ecf1;
      border-bottom: 0;
    }
    .form-active > ul {
      margin: 0px;
      padding: 0px;
      list-style: none;
    }
    td > a {
      display: inline-block;
      margin: 3px;
    }

    .minus {
      border: 1px solid black;
      border-radius: 50%;
      width: 13px;
      height: 13px;
      position: relative;
      margin-top: 3%;
    }

    .minus::before {
      content: '';
      position: absolute;
      left: 2px;
      top: 5px;
      width: 7px;
      border-top: 1px solid;
    }

    .logListInfo {
      margin-bottom: 2%;
    }

    .logListInfo table {
      border: 1px solid #C2C2C2;
    }

    .logListInfo .detailsInfo {
      display: none;
      padding-left: 8%;
    }

    .logListInfo .detailsInfo > td {
      word-wrap:break-word;
      word-break:break-all;
      text-align: left;
    }
    .logListInfo .rowInfo:hover{
      cursor: pointer;
    }
  </style>
</head>
<body>
<div id="exportCaseInfoContainer" style="display: none;width: 100%;height: 100%">
  <div class="row" style="padding-top: 30px;margin: 0px 0px">
    <div class="col-sm-3">
      <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择导出类型：</span>
    </div>
    <div class="col-sm-9">
      <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedAttach"
               type="checkbox" id="isNeedAttach"><label for="isNeedAttach">导出影像</label>
      </div>
      <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedExcel"
               type="checkbox" id="isNeedExcel"><label for="isNeedExcel">导出文件</label>
      </div>
      <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;">
        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="isNeedPushElm"
               type="checkbox" id="isNeedPushElm"><label for="isNeedPushElm">推送饿了么</label>
      </div>
    </div>
  </div>
  <div class="row" style="padding: 15px 30px;margin: 0px 0px">
    <button type="button" class="btn pull-right" style="background-color: #1676FF;color: white"
            id="modifyExportCaseStatus" onclick="modifyExportCaseStatus(this)">
      修改状态
    </button>
  </div>
  <div class="row logListInfo" style="margin-left: 20px;margin-right: 20px;">
    <div class="block-head-label col-sm-12" style="margin-bottom: 20px;">
      <span class="label-title">日志信息</span>
    </div>
    <div class="col-sm-12">
      <table class="table">
        <thead>
        <tr>
          <td width="10%"></td>
          <td width="20%">岗位</td>
          <td width="20%">类型</td>
          <td width="20%">人员</td>
          <td width="30%">时间</td>
        </tr>
        </thead>
        <tbody id="push-elm-log">
        </tbody>
      </table>
    </div>
  </div>
</div>
  <div class="portlet-body">
    <table class="table table-striped table-bordered table-hover table-header-fixed">
      <thead>
      <tr>
        <td>案件号</td>
        <td>渠道</td>
        <td>创建时间</td>
        <td>操作</td>
      </tr>
      </thead>
      <tbody>
      <#list list as vo>
      <tr>
        <td>${vo.claimCaseNo}</td>
        <td>${vo.insCode}</td>
        <td>${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
        <td>
          <a href="#" onclick="caseDetail('${vo.id}')">查看详情&nbsp;</a>
          <#if vo.status == 'acx10' && status != 'Exception'>
            <a href="#" onclick="openExportCaseInfo('${vo.id}')">导出&nbsp;</a>
            <a href="#" onclick="claimDataPush('${vo.id}')">推送</a>
          </#if>
          <#if vo.status == 'acx21' && status != 'Exception'>
            <a href="#" onclick="casePushELM('${vo.id}')">推送&nbsp;</a>
            <a href="#" onclick="seeCaseProcessReason('${vo.id}')">查看日志</a>
          </#if>
        </td>
      </tr>
      </#list>
      </tbody>
    </table>
  </div>
</body>
</html>