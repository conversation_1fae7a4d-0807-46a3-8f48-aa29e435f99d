<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>推送饿了么</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <style>
        div {
            display: inline-block;
        }
        .row {
            display: block;
            margin: 0px auto;
        }
        .col-sm-3 {
            width: 25%;
        }
        .col-sm-9 {
            width: 75%;
        }
        .form-group {
            margin: 50px;
        }
    </style>
    <script>

        $(function() {
            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
            });

            $("#status-content").on("click", "button", function() {
                var className = $(this).attr("class");
                if (className) {
                    $(this).removeClass("blue");
                } else {
                    $(this).attr("class", "blue");
                    $(this).siblings().removeClass("blue");
                }
            })
        })

        function push() {
            var status = $(".blue").attr("value");
            console.log(status);
            if (!status) {
                layer.msg("请选择回传状态", {icon: 2, time: 1500});
                return;
            }
            layer.confirm("请再次确认是否推送饿了么？", {icon: 3}, function (index) {
                    var formData = new FormData();
                    formData.append("claimCaseId", "${claimCase.id}");
                    formData.append("auditStatus", status);
                    $.ajax({
                        url: "${ctx}/claimCaseController/getBindPolicyPersonPage",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 2000}, function(index) {
                                    parent.window.location.reload();
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 1500
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            );
        }
    </script>
</head>
<body>
<div class="row " >
    <div class="row form-group">
        <div class="col-sm-4 text-right" style="padding-right: 0px;">保全信息：</div>
        <div class="col-sm-6">
            <#if policyPersonList??>
            <select class="form-control js-data-example-ajax kp-select2" name="policyPersonId" id="policyPersonId">
                <option value="">请选择</option>
                <#list policyPersonList as p>
                <option value="${p.id}" data-startDate="${p.startDate?string['yyyy-MM-dd HH:mm:ss']}" data-endDate="${p.endDate?string['yyyy-MM-dd HH:mm:ss']}">${p.startDate?string["yyyy-MM-dd HH:mm"]} 至 ${p.endDate?string["yyyy-MM-dd HH:mm"]}</option>
            </#list>
            </select>
        </div>
    <#else >
    <div class="row margin-bottom-15">
        <div class="col-sm-4 text-right">
            <span style="color: red; font-size: 20px;">暂无保全</span>
        </div>
    </div>
</#if>
    </div>
</div>
<div class="row">
    <div class="row text-center" >
        <button class="btn btn-primary" style="margin: 0px 5%;" onclick="push()">绑定</button>
        <button class="btn btn-warning" style="margin: 0px 5%;" onclick="parent.layer.closeAll();">取消</button>
    </div>
</div>
</body>
</html>