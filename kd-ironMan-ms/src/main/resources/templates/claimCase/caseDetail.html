<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->

    <script type="text/javascript">

        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        var layerTop = top.layer;

        const loader = new Loaders({style: "rectangle"});

        var scrollTop;  // 定义滚动高度

        $(document).ready(function () {

            iframeH();

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
                iframeH();
            });

            $(".attachInfo").on("click", "img", function (e) {
                bigImg($(this).attr("src"));
            });

            $(".block-head-label").on("click", "input[type='checkbox']", function (e) {
                var val = $(this).is(":checked");
                if (val) {
                    $(".rowInfo").each(function () {
                        $(this).show();
                    });
                } else {
                    $(".detailsInfo").hide();
                    $(".rowInfo").each(function () {
                        if ($(this).find("td:eq(3)").text().trim() == "系统") {
                            $(this).hide();
                        }
                    });
                }
                iframeH();

            });

            freshStatistics("0");

            (function () {
                $("img").each(function (idx, obj) {
                    if (!$(obj).attr("data-url")) {
                        return true;
                    }
                    let img = new Image();
                    img.src = $(obj).attr("data-url");
                    /*img.onload方法里如果不是自调用函数，则取不到对应的obj*/
                    img.onload = (function (e) {
                        $(obj).attr("src", $(obj).attr("data-url"));
                    })();
                    img.onerror = (function (e) {
                        $(obj).attr("src", '${ctx}/a/job_done.png');
                    });
                });
            })();

            //初始化赔付信息
            freshDutyInfo();
        });

        //下载保单
        function onloadEPolicy(policyPersonId) {
            if (typeof policyPersonId == 'undefined' || policyPersonId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("policyPersonId", policyPersonId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/onloadEPolicy",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //查看保险方案
        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //案件关闭
        function closeCase(caseId) {
            scrollTop = calculationScrollTop();
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>';
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area: ['500px', '300px'],
                btn: ['确认', '取消'],
                offset: scrollTop,
                yes: function (index, obj) {
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if (typeof closeCaseMsg != 'string' || closeCaseMsg.trim() == '') {
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000, offset: scrollTop});
                    } else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 3);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/claimCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    $("#screenLoading").css("top", "100px");
                                    setTimeout(function () {
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 2000, //1秒关闭（如果不配置，默认是3秒）
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    }, 1000);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500, //1秒关闭（如果不配置，默认是3秒）
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        //出险人身份证影像
        function seeTreatIdTypeAttach(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '出险人身份证影像信息',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/claimCaseAttachFile?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //补发短信
        function reSendMessage(claimCaseId) {
            scrollTop = calculationScrollTop();
            layer.open({
                title: "补发短信",
                type: 1,
                content: $('#reSendMessageContainer'),
                area: ['700px', '300px'],
                fixed: false,
                offset: scrollTop,
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#reSendMessageContainer').find('span.checked');
                    let children = check.children();
                    let code = children.attr("code");
                    if (typeof code == "undefined" || code == null) {
                        layerTop.msg("请选择短信类型！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    let reissueReason = $("#reissueReason").val();
                    if (reissueReason.trim() == "") {
                        layer.msg("请输入补发原因！！！", {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("messageCode", code);
                    formData.append("reissueReason", reissueReason);
                    $.ajax({
                        url: "${ctx}/claimCaseController/reSendMessage",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layerTop.msg("发送成功", {
                                    icon: 1,
                                    time: 2000
                                }, function () {
                                    layer.closeAll();
                                });
                            } else {
                                layerTop.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        //催办记录
        function pressDoLog() {
            layerTop.msg("待建设！！！", {
                icon: 3,
                time: 2000
            });
        }

        //查看估损金额
        function modifyAppraisalAmount(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.5 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '查看估损金额',
                area: openWindowWidth,
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseController/modifyAppraisalAmount?claimCaseId=" + claimCaseId + "&status=" + status,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function changeAppraisalAmount(money) {
            $("#appraisalAmount").html('估损金额：' + money + '元');
        }

        //添加日志
        function addCaseLog(claimCaseId) {
            let areaMsg = $("#logTextArea").val().trim();
            if (areaMsg == '') {
                layer.tips("请添加日志描述！！", '#logTextArea', {tips: 1});
                return;
            }

            var formData = new FormData();
            formData.append("claimCaseId", claimCaseId);
            formData.append("description", areaMsg);
            formData.append("type", 4);
            $("#addCaseLog").attr("disabled", "disabled");
            $.ajax({
                url: "${ctx}/claimCaseController/addCaseLog",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        let logMsg = result.data;
                        $(".logListInfo tbody").prepend(`
                        <tr class="rowInfo">
                                <td width="10%" align="center">
                                    <div class="icon-plus"></div>
                                </td>
                                <td>` + logMsg.position + `</td>
                                <td>` + logMsg.status + `</td>
                                <td>` + logMsg.creator.substring(0, logMsg.creator.indexOf("-")) + `</td>
                                <td>` + timeStamp2String(logMsg.createTime) + `</td>
                            </tr>
                            <tr class="detailsInfo">
                                <td></td>
                                <td colspan="4">` + logMsg.description + `</td>
                            </tr>
                        `);
                        iframeH();
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

            // 设置新增备注30秒内不能重复提交
            setTimeout(function () {
                $("#addCaseLog").removeAttr("disabled");
            }, 30000);

            console.log(areaMsg);
        }

        //标记是否疑难
        function markDiffcultCase(obj) {
            scrollTop = calculationScrollTop();
            let difficultCase = "${claimCase.isDifficultCase}";
            /*var X = $(obj).offset().top;  //获取当前元素x坐标
            var Y = $(obj).offset().left; //获取当前元素y坐标*/
            if (difficultCase + "" != 1) {
                var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control"  id="markDiffcultCaseMsg" autocomplete="off" placeholder="请输入挂起原因"></textarea></div>';
                layer.open({
                    type: 1,
                    content: content,
                    title: '案件挂起',
                    area: ['500px', '300px'],
                    btn: ['确认', '取消'],
                    offset: scrollTop,
                    yes: function (index, obj) {
                        console.log(typeof $("#markDiffcultCaseMsg").val());
                        var hangCaseMsg = $("#markDiffcultCaseMsg").val();
                        if (typeof hangCaseMsg != 'string' || hangCaseMsg.trim() == '') {
                            layer.msg("挂起原因不能为空", {icon: 2, time: 3000, offset: scrollTop});
                        } else {
                            var formData = new FormData();
                            formData.append("claimCaseId", "${claimCase.id}");
                            formData.append("type", 1);
                            formData.append("description", hangCaseMsg);
                            $.ajax({
                                url: "${ctx}/insuranceCaseController/markDiffcultCase",
                                type: 'POST',
                                data: formData,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        setTimeout(function () {
                                            layer.msg('案件挂起成功', {
                                                icon: 1,
                                                time: 3000,
                                                offset: scrollTop
                                            }, function () {
                                                window.location.reload();
                                            });
                                        }, 1200);
                                    } else {
                                        if (result.ret == "1001") {
                                            layer.confirm(
                                                result.msg, {icon: 1, title: '执行', offset: scrollTop}, function (index) {
                                                    var formData = new FormData();
                                                    formData.append("claimCaseId", "${claimCase.id}");
                                                    formData.append("type", 1);
                                                    formData.append("isSkip", 1);
                                                    formData.append("description", hangCaseMsg);
                                                    $.ajax({
                                                        url: "${ctx}/insuranceCaseController/markDiffcultCase",
                                                        type: 'POST',
                                                        data: formData,
                                                        async: false,
                                                        cache: false,
                                                        contentType: false,
                                                        processData: false,
                                                        success: function (data) {
                                                            var result = eval("(" + data + ")");
                                                            if (result.ret == "0") {
                                                                setTimeout(function () {
                                                                    layer.msg('挂起成功', {
                                                                        icon: 1,
                                                                        time: 3000,
                                                                        offset: scrollTop
                                                                    }, function () {
                                                                        window.location.reload();
                                                                    });
                                                                }, 1200);
                                                            } else {
                                                                layer.msg(result.msg, {
                                                                    icon: 1,
                                                                    time: 1500,
                                                                    offset: scrollTop
                                                                }, function (index) {
                                                                    layer.close(index);
                                                                });
                                                            }
                                                        },
                                                        error: function (data) {
                                                            var result = eval("(" + data + ")");
                                                            alert(result.msg);
                                                        }
                                                    })
                                                }
                                            );
                                        } else {
                                            layer.msg(result.msg, {
                                                icon: 1,
                                                time: 1500,
                                                offset: scrollTop
                                            }, function (index) {
                                                layer.close(index);
                                            });
                                        }

                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    alert(result.msg);
                                }
                            });
                        }
                    }
                });
            } else {
                layer.confirm(
                    '是否取消疑难?', {icon: 2, title: '执行'}, function (index) {
                        var formData = new FormData();
                        formData.append("claimCaseId", "${claimCase.id}");
                        formData.append("type", 2);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/markDiffcultCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    setTimeout(function () {
                                        layer.msg('案件取消挂起成功', {
                                            icon: 1,
                                            time: 3000,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    }, 1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });

                    }
                )
            }


        }

        //再次来电切换
        function changeCustomerTask(e) {
            scrollTop = calculationScrollTop();
            var formData = new FormData();
            formData.append("claimCaseId", "${claimCase.id}");
            if ($(e).text().trim() == "再次去电") {
                formData.append("phoneType", 0);
            }
            if ($(e).text().trim() == "去电完成") {
                formData.append("phoneType", 1);
            }
            formData.append("type", 4);
            $.ajax({
                url: "${ctx}/insuranceCaseController/againPhone",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    loader.show();
                    $("#screenLoading").css("top", scrollTop);
                    setTimeout(function () {
                        layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function (index) {
                            layer.close(index);
                            window.location.reload();
                        });
                    }, 1000);
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        //编辑影像
        function editAttach(claimCaseId) {
            scrollTop = calculationScrollTop();
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '影像编辑',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseController/editAttach?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //时间格式化
        function timeStamp2String(time) {
            var datetime = new Date();
            datetime.setTime(time);
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
            var hour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
            var minute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
            var second = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
            return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
        }

        function freshStatistics(type) {
            scrollTop = calculationScrollTop();
            let baseUserId = "${claimCase.baseUserId}";
            let formData = new FormData();
            formData.append("baseUserId", baseUserId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/getBaseUserStatistics",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        $("#lastStatisticsData").html(result.lastStatisticsData);
                        $("#times").html(result.times + "次");
                        $("#sumTimesRate").html(result.sumTimesRate + "%");
                        $("#continuousTimes").html(result.continuousTimes + "次");
                        $("#sumNear30TimesRate").html(result.sumNear30TimesRate + "%");
                        $("#near30DayTimes").html(result.near30DayTimes + "次");
                        $("#lastTimePolicyPersonDate").html(result.lastTimePolicyPersonDate);
                        $("#lastPolicyPersonDate").html(result.lastPolicyPersonDate);
                    } else {
                        if (type == "1") {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 1500,
                                offset: scrollTop
                            });
                        }

                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function verifyCase(claimCaseId) {
            window.open("${ctx}/insuranceCaseController/claimVerify?claimCaseId=" + claimCaseId);
        }


        function bigImg(src) {
            $("#bigimg").attr("src", src);//设置#bigimg元素的src属性

            /*获取当前点击图片的真实大小，并显示弹出层及大图*/
            $("<img/>").attr("src", src).load(function () {
                var windowW = $(window).width();//获取当前窗口宽度
                var windowH = $(window).height();//获取当前窗口高度
                var realWidth = this.width;//获取图片真实宽度
                var realHeight = this.height;//获取图片真实高度
                var imgWidth, imgHeight;
                var scale = 0.8;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放

                if (realHeight > windowH * scale) {//判断图片高度
                    imgHeight = windowH * scale;//如大于窗口高度，图片高度进行缩放
                    imgWidth = imgHeight / realHeight * realWidth;//等比例缩放宽度
                    if (imgWidth > windowW * scale) {//如宽度扔大于窗口宽度
                        imgWidth = windowW * scale;//再对宽度进行缩放
                    }
                } else if (realWidth > windowW * scale) {//如图片高度合适，判断图片宽度
                    imgWidth = windowW * scale;//如大于窗口宽度，图片宽度进行缩放
                    imgHeight = imgWidth / realWidth * realHeight;//等比例缩放高度
                } else {//如果图片真实高度和宽度都符合要求，高宽不变
                    imgWidth = realWidth;
                    imgHeight = realHeight;
                }
                $("#bigimg").css("width", imgWidth);//以最终的宽度对图片缩放

                var w = (windowW - imgWidth) / 2;//计算图片与窗口左边距
                var h = $(window.parent).scrollTop();//计算图片与窗口上边距
                var ifm = parent.document.getElementById("contentFrame");
                if (!ifm) {
                    h = 100;
                }
                var maxHeight = windowH - h - 100;
                if (maxHeight < 0) {
                    maxHeight = 100;
                }

                $("#innerdiv").css({"top": h, "left": w, "overflow-x": "auto", "max-height": maxHeight});//设置#innerdiv的top和left属性
                $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
            });

            var imgTransferDeg = 0;
            $(window).on("keydown", function (event) {
                switch (event.keyCode) {
                    // 放大
                    case 87:
                        imgToSize(100);
                        break;
                    // 缩小
                    case 83:
                        imgToSize(-100);
                        break;
                    // 左旋
                    case 65:
                        imgReverse(imgTransferDeg = imgTransferDeg - 90);
                        break;
                    // 右旋
                    case 68:
                        imgReverse(imgTransferDeg = imgTransferDeg + 90);
                        break;
                }
            });

            $("#outerdiv").click(function () {//再次点击淡出消失弹出层
                $(this).fadeOut("fast");
                $(window).off("keydown");
                $("#bigimg").attr("sytle", "border: 5px solid #fff;");
            });
        }

        //放大缩小图片
        function imgToSize(size) {
            var img = $("#bigimg");
            var oWidth = img.width(); //取得图片的实际宽度
            // var oHeight = img.height(); //取得图片的实际高度
            img.width(oWidth + size);
            // img.height((oHeight + size )/ oWidth * oHeight);

            var windowW = $(window).width();//获取当前窗口宽度
            var w = (windowW - oWidth) / 2;//计算图片与窗口左边距
            var h = $(window.parent).scrollTop();//计算图片与窗口上边距
            var ifm = parent.document.getElementById("contentFrame");
            if (!ifm) {
                h = 100;
            }
            $("#innerdiv").css({"top": h, "left": w});//设置#innerdiv的top和left属性
        }

        // 翻转图片
        function imgReverse(arg) {

            var img = $("#bigimg");
            img.css({"-webkit-transform": "rotate(" + arg + "deg)"});
        }

        //查看人员详情
        function viewPersonDetail(policyPersonId) {
            scrollTop = calculationScrollTop();
            if (policyPersonId.trim() == "") {
                layer.msg("暂无人员信息", {icon: 2, time: 2000, offset: scrollTop}, function (index) {
                    layer.close(index);
                });
                return;
            }
            window.location.href = "${ctx}/personnelManagementController/personnelManagementDetail?policyPersonId=" + policyPersonId;

        }

        function getHistoryRecord(baseUserId) {
            scrollTop = calculationScrollTop();
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000, offset: scrollTop});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '历史保单',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistoryRecord?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        function onloadClaimApplyBook(id) {
            scrollTop = calculationScrollTop();
            $.ajax({
                url: "${ctx}/claimCaseController/refreshBook?claimCaseId=" + id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }


        //刷新请求后台，责任数据
        function freshDutyInfo() {
            $("#dutyInfoShow").html("");
            var formData = new FormData();
            formData.append("claimCaseId", '${claimCase.id}');
            $.ajax({
                url: "${ctx}/claimCaseController/getClaimCaseObjectInfos",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    try {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            let dataList = JSON.parse(result.data);
                            if (dataList) {
                                $.each(dataList, function (index, obj) {
                                    let type = "-";
                                    switch (obj.type) {
                                        case 1:
                                            type = "代驾";
                                            break;
                                        case 2:
                                            type = "三者";
                                            break;
                                    }
                                    switch (obj.category) {
                                        case 1:
                                            type += "人伤";
                                            break;
                                        case 2:
                                            type += "物损";
                                            break;
                                        case 3:
                                            type += "车损";
                                            break;
                                    }

                                    let name = "-";
                                    if (obj.name != undefined) {
                                        name = obj.name;
                                    }
                                    let mobile = "-";
                                    if (obj.mobile != undefined) {
                                        mobile = obj.mobile;
                                    }
                                    let estimatedOverallLoss = "-";
                                    if (obj.estimatedOverallLoss != undefined) {
                                        estimatedOverallLoss = obj.estimatedOverallLoss;
                                    }
                                    let accidentProportion = "-";
                                    if (obj.accidentProportion != undefined) {
                                        accidentProportion = obj.accidentProportion;
                                    }
                                    let gn = "";
                                    <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") >
                                    <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") >
                                    gn = '<a onclick="editObject(\'' + obj.id + '\')">修改</a>';
                                    <@shiro.hasPermission name="CLAIM_CASE_OBJECT_DEL">
                                    gn += '<a onclick="delObject(\'' + obj.id + '\')">删除</a>';
                                    </@shiro.hasPermission>
                                    </#if>
                                    </#if>
                                    let init = $('<tr>\n' +
                                        '                                <td width="15%">' + type + '</td>\n' +
                                        '                                <td width="15%">' + name + '</td>\n' +
                                        '                                <td width="15%">' + mobile + '</td>\n' +
                                        '                                <td width="15%">' + estimatedOverallLoss + '</td>\n' +
                                        '                                <td width="15%">' + accidentProportion + '%</td>\n' +
                                        '                                <td width="15%">' + gn + '</td>\n' +
                                        '</tr>');
                                    $("#dutyInfoShow").append(init);
                                });
                            }

                        } else {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 1500 //1秒关闭（如果不配置，默认是3秒）
                            }, function (index) {
                                layer.close(index);
                            });
                        }

                    } catch (e) {
                        console.error(e);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function editObject(objectId) {
            if (objectId == undefined) {
                objectId = "";
            }
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '损失项目',
                area: ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/editObject?objectId=" + objectId + "&claimCaseId=${claimCase.id}",
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function delObject(objectId) {
            scrollTop = calculationScrollTop();
            if (objectId == undefined || objectId == "") {
                layer.msg("损失项目不存在", {icon: 2, time: 1500});
                freshDutyInfo();
                return;
            }
            $.ajax({
                url: "${ctx}/claimCaseObjectController/claimCaseObjectDelete?claimCaseObjectId=" + objectId,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {icon: 1, time: 1500, offset: scrollTop}, function (index) {
                            window.location.reload();
                            layer.close(index);
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1500, offset: scrollTop}, function (index) {
                            freshDutyInfo();
                            layer.close(index);
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0, 0, 0, 0.3);
            top: 0;
        }

        #screen #screenLoading {
            margin: 0 auto;
            top: 70%;
            transform: translateY(-50%);
            background: greenyellow;
        }

        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .widget-thumb .widget-thumb-heading {
            font-size: 16px !important;
        }

        .caption-subject {
            font-weight: 500 !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }

        .btn-light-bule {
            color: #3662EC;
            background-color: #DEEBFF;
            border-color: #DEEBFF;
            margin-left: 3%;
        }

        .btn-light-bule:hover {
            color: #3662EC;
        }

        .container-head {
            border-bottom-width: 3px !important;
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .container-boby {
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .block-show {
            display: flex;
        }

        .block-head-label {
            margin-bottom: 1%;
            font-size: 20px;
        }

        .block-head-label a {
            font-size: 15px;
            margin-left: 10px;
        }

        .block-head-label span {
            font-size: 15px;
        }

        .block-border {
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #EFEFEF;
            border-left: 3px solid #FFFFFF;
            border-right: 3px solid #EFEFEF;
            margin-text-outline: 1.5%;
            margin-bottom: 2%;
        }

        .block-border .col-sm-4 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-8 {
            padding: 0px 1px 0px 0px !important;
        }

        .block-border .col-sm-12 {
            padding: 0px 1px 0px 0px !important;
        }

        .left-min-5 {
            width: 41.66666%;
            padding-bottom: 2.5%;
            background-color: #EFEFEF;
            padding-top: 2.5%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-min-5 {
            width: 41.66666%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-min-7 {
            width: 58.33334%;
            padding-bottom: 2.5%;
            background-color: white;
            padding-top: 2.5%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-min-7 {
            width: 58.33334%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }

        .left-mid-5 {
            width: 20.83333%;
            padding-bottom: 1.25%;
            background-color: #EFEFEF;
            padding-top: 1.25%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-mid-5 {
            width: 20.83333%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-mid-7 {
            width: 79.16667%;
            padding-bottom: 1.25%;
            background-color: #FFFFFF;
            padding-top: 1.25%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-mid-7 {
            width: 79.16667%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .left-max-5 {
            width: 13.88888%;
            padding-bottom: 0.833%;
            background-color: #EFEFEF;
            padding-top: 0.833%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-max-5 {
            width: 13.88888%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-max-7 {
            width: 86.11112%;
            padding-bottom: 0.833%;
            background-color: #FFFFFF;
            padding-top: 0.833%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-max-7 {
            width: 86.11112%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .attachInfo {
            border: 1px solid #D8D8D8;
            padding: 0px 3.5% 20px;
            margin-bottom: 2%;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 1px;
            top: 1px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo {
            margin-bottom: 2%;
        }

        .payDutyInfo .col-sm-5 {
            display: block;
            margin-bottom: 20px;;
            font-weight: bold;
            font-size: 17px;
            height: 210px;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 3%;
            border: 1px solid #000;
        }

        .notice-no-deal {
            background-color: #EE3232;
        }

        .notice-deal {
            background-color: #3662EC;
        }

        .notice-hangup {
            background-color: #F49929;
        }

        .notice-default {
            background-color: darkgrey;
        }


        .logInfo {
            margin-bottom: 2%;
        }

        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        #reSendMessageContainer .radio {
            margin-left: 0px;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
            text-align: left;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

        .label-title {
            font-size: 22px !important;
        }

        .subject-span {
            display: inline-block;
            margin-right: 40px;
            margin-top: 12px;
        }

        .subject-text-overflow {
            display: inline-block;
            max-width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
        }

        .span-type {
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin: 3px;
            padding: 2px;
        }

        .dutyInfoArea th,
        .dutyInfoArea td {
            text-align: center;
            border-top: 1px solid #C2C2C2 !important;
            border-bottom: 1px solid #C2C2C2 !important;
        }

        td > a {
            display: inline-block;
            margin: 3px;
        }
    </style>
</head>

<body>

<#-- 图片放大 -->
<div id="outerdiv" style="position: fixed; top: 0; left: 0; background: rgba(0,0,0,0.7); z-index: 2; width: 100%; height: 100%; display: none;">
    <div id="innerdiv" style="position: absolute;">
        <img id="bigimg" style="border: 5px solid #fff;" src=""/>
    </div>
</div>

<#--补发短信-->
<div id="reSendMessageContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
        </div>
        <div class="col-sm-9">
            <#if messageType?? && (messageType.keySet()?size>0)>
                <#list messageType.keySet() as key>
                    <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="radio">${messageType.get(key)}
                    </div>
                </#list>
            </#if>
            <textarea class="form-control" id="reissueReason" rows="5" placeholder="请输入补发原因"></textarea>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title container-head">
                <div class="row">
                    <div class="col-sm-5 pull-left">
                        <div class="col-sm-3"><span
                                    style="border-radius:15px;background-color: orange;padding: 5px;color: #fff;display: inline-block">${claimCase.productName!'暂无产品'}</span>
                        </div>
                        <div class="col-sm-4" style="font-weight: bold;font-size: 17px;">${claimCase.treatName!'-'}&nbsp;&nbsp;${claimCase.plateNumber!'-'}</div>
                        <div class="col-sm-4" style="color: #979797;">${(claimCase.treatDate?string('yyyy-MM-dd HH'))!'-'}时出险</div>
                    </div>
                    <div class="col-sm-2" style="font-weight: bold;font-size: 17px;">
                        <span style="color: #507FBF">${claimStatusMsg}</span>
                        <@shiro.hasPermission name="URGENT_VERIFY">
                            <#if claimCase.status == "abx10">
                                <button class="btn btn-warning" onclick="verifyCase('${claimCase.id}')">赔案审核</button>
                            </#if>
                        </@shiro.hasPermission>
                    </div>
                    <div class="col-sm-5">
                        <button class="btn btn-bule" onclick="onloadEPolicy('${claimCase.policyPersonId}')">下载保单
                        </button>
                        <#--催办记录暂时不处理，后期添加新功能-->
                        <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>
                            <button class="btn btn-light-bule" onclick="pressDoLog()">催办记录（0）</button>
                            <#--<button class="btn btn-bule" onclick="reSendMessage('${claimCase.id}')">补发短信</button>-->
                            <@shiro.hasPermission name="REPORT_CASE_LIST_CLOSE_CASE">
                                <button class="btn btn-bule" onclick="closeCase('${claimCase.id}')">关闭案件</button>
                            </@shiro.hasPermission>
                        </#if>
                    </div>
                </div>
            </div>
            <div class="portlet-body container-boby">

                <div class="row">
                    <div class="block-head-label">
                        <span class="label-title">投保频率</span> &nbsp;&nbsp;&nbsp;<span style="font-size: 10px">最近一次统计时间：</span><span style="font-size: 10px" id="lastStatisticsData"> </span> <a onclick="freshStatistics('1')">刷新</a>
                        <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>
                            <a class="pull-right" onclick="modifyAppraisalAmount('${claimCase.id}')">查看</a>
                        </#if>
                        <span class="pull-right" id="appraisalAmount">估损金额：${claimCase.appraisalAmount}元</span>
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="总投保次数">总投保次数：</div>
                                    <div class="right-min-7" id="times"></div>
                                    <a style="position: absolute;right: 1px;top: 30%" onclick="getHistoryRecord('${claimCase.baseUserId!''}')">历史保单记录</a>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="总投保率">总投保率：</div>
                                    <div class="right-min-7" id="sumTimesRate"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="最近连续投保天数">最近连续投保天数：</div>
                                    <div class="right-min-7" id="continuousTimes"></div>
                                </div>

                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="最近30天投保率">最近30天投保率：</div>
                                    <div class="right-min-7" id="sumNear30TimesRate"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="近30天投保天数">近30天投保天数：</div>
                                    <div class="right-mid-7" id="near30DayTimes"></div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="left-min-5" title="最近一次投保时间">最近一次投保时间：</div>
                                        <div class="right-min-7" id="lastTimePolicyPersonDate"></div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="left-mid-5" title="最近投保时间">最近投保时间：</div>
                                        <div class="right-mid-7" id="lastPolicyPersonDate"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="row">
                    <div class="block-head-label">
                        <span class="label-title">出险人信息</span>
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="保单号">保单号：</div>
                                    <div class="right-min-7">${policyPerson.policyNo!'-'}</div>
                                    <a style="position: absolute;right: 1px;display: block;width: 60px;"
                                       onclick="seePlanName('${product.id!''}')">查看方案</a>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="保险期限">保险期限：</div>
                                    <div class="right-min-7">${(policyPerson.startDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}
                                        至${(policyPerson.endDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险人">出险人：</div>
                                    <div class="right-min-7" id="treatName-text">${claimCase.treatName!'-'}</div>
                                    <#--待人员详情地址-->
                                    <a onclick="viewPersonDetail('${claimCase.policyPersonId}')" style="position: absolute;right: 1px;top: 30%">查看人员信息</a>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="身份证">身份证：</div>
                                    <div class="right-min-7">
                                        <span id="treatIdNum-text">
                                            ${claimCase.treatIdNum!'-'}
                                        </span>
                                        <a style="position: absolute;right: 1px;top: 30%"
                                           onclick="seeTreatIdTypeAttach('${claimCase.id}')">查看图片</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="车辆信息">车辆信息：</div>
                                    <div class="right-min-7">${claimCase.carBrand!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="车牌号">车牌号：</div>
                                    <div class="right-min-7">${claimCase.plateNumber!'-'}</div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="联系电话">联系电话：</div>
                                        <div class="right-max-7" id="treatMobile-text">${claimCase.treatMobile!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        <span class="label-title">报案信息</span>
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="案件号">案件号：</div>
                                    <div class="right-min-7">${claimCase.claimCaseNo!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="报案时间">报案时间：</div>
                                    <div class="right-min-7">
                                        ${(claimCase.startDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}&nbsp;&nbsp;&nbsp;
                                        <span style="color: red" title="出险后${diffTime!''}报案">出险后${diffTime!''}报案</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="报案人姓名">报案人姓名：</div>
                                    <div class="right-min-7">${claimCase.applyName!'-'}</div>
                                </div>
                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="联系电话">联系电话：</div>
                                        <div class="right-max-7">${claimCase.applyMobile!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        <span class="label-title">出险信息</span>
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险原因">出险原因：</div>
                                    <div class="right-min-7">交通事故</div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="出险时间">出险时间：</div>
                                    <div class="right-mid-7">
                                        ${(claimCase.treatDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}&nbsp;&nbsp;&nbsp;
                                        <span style="color: red">保单投保时间：${(policyPerson.startDate?string("yyyy-MM-dd"))!'-'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>


                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="事故类别">事故类别：</div>
                                    <div class="right-min-7">
                                        <#if claimCase.applyType?contains("AA001") || claimCase.applyType?contains("AB001")>
                                            <span class="span-type">门诊</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA002") || claimCase.applyType?contains("AB002")>
                                            <span class="span-type">住院</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA003") || claimCase.applyType?contains("AB003")>
                                            <span class="span-type">死亡</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA004") || claimCase.applyType?contains("AB004")>
                                            <span class="span-type">伤残</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AC")>
                                            <span class="span-type">物损</span>
                                        </#if>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="出险地点">出险地点：</div>
                                    <div class="right-mid-7" title="${claimCase.province!"-"}-${claimCase.city!"-"}-${claimCase.district!"-"}&nbsp;&nbsp;${claimCase.address!"-"}">
                                        ${claimCase.province!"-"}-${claimCase.city!"-"}-${claimCase.district!"-"} &nbsp;&nbsp;&nbsp;
                                        ${claimCase.address!"-"}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="left-min-5" title="医院名称">医院名称：</div>
                                        <div class="right-min-7">
                                            ${claimCase.hospitalName}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="left-mid-5" title="索赔金额（元）">索赔金额（元）：</div>
                                        <div class="right-mid-7">${(claimCase.applyMoney?string("0.00"))!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="经过">经过：</div>
                                        <div class="right-max-7" title="${claimCase.description!'-'}">
                                            ${claimCase.description!'-'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <div class="row dutyInfoArea">
                    <div class="block-head-label">
                        <span class="label-title">损失项目
                            <@shiro.hasPermission name="CLAIM_CASE_OBJECT_ADD">
                                <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") >
                                    <a onclick="editObject()">新增</a>
                                </#if>
                            </@shiro.hasPermission>
                        </span>
                    </div>
                    <div class="col-sm-12 clear-padding">
                        <table class="table" style="border: 1px solid #C2C2C2;">
                            <thead>
                            <tr>
                                <th width="15%">类型</th>
                                <th width="15%">赔付对象名称</th>
                                <th width="15%">手机号</th>
                                <th width="15%">预估金额</th>
                                <th width="15%">事故比例</th>
                                <th width="15%">功能</th>
                            </tr>
                            </thead>
                            <tbody id="dutyInfoShow">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        <span class="label-title">影像信息</span>
                        <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") && claimCase.reportCaseStatus!="2">
                            <a onclick="editAttach('${claimCase.id}')">编辑影像</a>
                        </#if>
                    </div>
                    <div class="col-sm-12 attachInfo">
                        <#if attachMap?? && (attachMap.keySet()?size>0)>
                            <#list attachMap.keySet() as key>
                                <div attach-type="${key}">
                                    <label><#if imgInfoMap.get(key)??>${imgInfoMap.get(key)}<#else >${key!'其它'}</#if></label>
                                    <div class="col-sm-12">
                                        <#list attachMap.get(key) as attach>
                                            <div class="col-sm-2" style="display: flex;flex-wrap: wrap;align-items: flex-start">
                                                <div style="position: relative;width:230px;height:250px">
                                                    <img src="${ctx}/a/ajax-loader.gif" data-url="${attach.fileObjectId}">
                                                    <#if attach.replenishNub!>
                                                        <div class="icon-attach">${attach.replenishNub!''}</div></#if>
                                                </div>
                                            </div>
                                        </#list>
                                    </div>
                                </div>
                            </#list>
                        </#if>
                    </div>
                </div>

                <div class="row payDutyInfo">
                    <div class="block-head-label">
                        <span class="label-title">赔付责任</span>
                    </div>
                    <#if claimCaseSubjectList?exists && (claimCaseSubjectList?size>0)>
                    <#list claimCaseSubjectList as claimCaseSubject>
                    <div class="col-sm-5 claim_subject_area">
                        <#switch claimCaseSubject.status>
                        <#case 0>
                        <div class="notice notice-no-deal">
                            <#break >
                            <#case 1>
                            <div class="notice notice-deal">
                                <#break >
                                <#case -1>
                                <div class="notice notice-hangup">
                                    <#break >
                                    <#default >
                                    <div class="notice notice-default">
                                        </#switch>
                                        <div>${claimCaseSubject.remark!'-'}</div>
                                    </div>
                                </div>
                                </#list>
                                </#if>
                            </div>

                            <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>
                                <div class="row logInfo">
                                    <div class="block-head-label">
                                        <span class="label-title">沟通记录/新增备注</span>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <textarea class="form-control" id="logTextArea" rows="5" placeholder="请输入"></textarea>
                                            <div class="col-sm-12">
                                                <button class="btn btn-warning" onclick="addCaseLog('${claimCase.id}')" id="addCaseLog">新增备注</button>
                                                <#--<#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") && claimCase.reportCaseStatus != 2 && claimCase.policyPersonId != "">
                                                    <button class="btn btn-bule" onclick="changeCustomerTask(this)">
                                                        <#if isCanCustomerTask=='true'>再次去电<#else >去电完成</#if>
                                                    </button>
                                                    <button class="btn btn-bule" onclick="markDiffcultCase(this)">
                                                        <#if claimCase.isDifficultCase!=1>标记疑难<#else >取消疑难</#if>
                                                    </button>
                                                </#if>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </#if>

                            <div class="row logListInfo">
                                <div class="block-head-label">
                                    <span class="label-title">日志信息</span>
                                    <input type="checkbox" id="checkSystem"> <label for="checkSystem" style="font-size: 8px;font-weight: bold;">是否展示系统日志</label>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <td width="10%"></td>
                                            <td width="20%">岗位</td>
                                            <td width="20%">类型</td>
                                            <td width="20%">人员</td>
                                            <td width="30%">时间</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <#if claimCaseLogShows??>
                                            <#list claimCaseLogShows as log>
                                                <tr class="rowInfo" <#if log.creator == "系统"> style="display: none;" </#if> >
                                                    <td width="10%" align="center">
                                                        <div class="icon-plus"></div>
                                                    </td>
                                                    <td width="20%">${log.position}</td>
                                                    <td width="20%">
                                                        ${claimCaseLogTypeEnumMap.get(log.type).msg}
                                                    </td>
                                                    <td width="20%">
                                                        <#if log.creator?contains("-") && log.creator!="-1" >
                                                            ${log.creator?substring(0,log.creator?index_of("-"))}
                                                        <#else>
                                                            ${log.creator}
                                                        </#if>
                                                    </td>
                                                    <td width="30%">${log.createTime?string["yyyy-MM-dd HH:mm:ss"]}</td>
                                                </tr>
                                                <tr class="detailsInfo">
                                                    <td width="10%" align="center"></td>
                                                    <td width="90%" colspan="4" style="overflow-x: visible;">${log.description}</td>
                                                </tr>
                                            </#list>
                                        </#if>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
</body>
</html>