<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>推送饿了么</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <style>

        .clear-padding {
            padding: 0px;
        }

        .clear-padding-right {
            padding-right: 0px;
        }

        .clear-padding-left {
            padding-left: 0px;
        }

        .btn-div {
            text-align: right;
            margin: 15px;
        }

        .btn-div > button {
            margin-right: 20px;
        }

        button.active {
            background: #0597FF !important;
            color: #ecebeb !important;
        }

        .btn-diy {
            padding: 3px 15px;
            margin-right: 10px;
            background-color: transparent;
            border: 1px solid #eee;
        }
    </style>
    <script>

        $(function() {

            $("#hang_up_content").on("click", "button", function() {
                $(this).addClass("active");
                $(this).siblings().removeClass("active");
            });
        });

        function addCaseHangUp() {
            var hangUpItem = $("button.active").attr("value");
            if (!hangUpItem) {
                layer.msg("请选择挂起事项", {icon: 2, time: 1500});
                return;
            }
            var hangUpReason = $("#hangUpReason").val();
            if (!hangUpReason) {
                layer.msg("请输入挂起说明", {icon: 2, time: 1500});
                return;
            }
            layer.confirm("请再次确认是否挂起？", {icon: 3}, function (index) {
                var formData = new FormData();
                formData.append("claimCaseId", "${claimCase.id}");
                formData.append("claimCaseNo", "${claimCase.claimCaseNo}");
                formData.append("hangUpSource", "${hangUpSource}");
                formData.append("hangUpItem", hangUpItem);
                formData.append("hangUpReason", hangUpReason);
                $.ajax({
                    url: "${ctx}/claimCaseHangUpController/addCaseHangUp",
                    type: 'POST',
                    data: formData,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 2000, shade: [0.2, '#000']}, function(index) {
                                <#if hangUpSource == "1">
                                    parent.location.href = "${ctx}/claimCaseController/claimCaseList?status=4";
                                </#if>
                                <#if hangUpSource == "2">
                                    parent.location.href = "${ctx}/claimCaseController/casePretreatmentList?status=4";
                                </#if>
                            });
                        } else {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 1500
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        function closePage() {
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index);
        }

    </script>
</head>
<body>
<div style="margin: 4% 4% 0px;">
    <div class="row margin-bottom-25" >
        <div class="col-sm-2" style="height: 34px;text-align: right;">挂起事项：</div>
        <div class="col-sm-8" id="hang_up_content">
            <#if itemMap?? && (itemMap.keySet()?size>0)>
            <#list itemMap.keySet() as key>
                <button class="btn btn-circle btn-diy <#if key == "99">active</#if>" value="${key}" >${itemMap.get(key)}</button>
            </#list>
            </#if>
        </div>
    </div>
    <div class="row margin-bottom-25">
        <div class="col-sm-2" style="height: 34px;text-align: right;">挂起说明：</div>
        <div class="col-sm-9">
            <textarea class="form-control" rows="10" width="100%" name="hangUpReason" id="hangUpReason" placeholder="请输入挂起说明"></textarea>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 btn-div" >
            <button class="btn btn-primary" onclick="addCaseHangUp()">新增</button>
            <button class="btn btn-warning" onclick="closePage()">取消</button>
        </div>
    </div>
</div>
</body>
</html>