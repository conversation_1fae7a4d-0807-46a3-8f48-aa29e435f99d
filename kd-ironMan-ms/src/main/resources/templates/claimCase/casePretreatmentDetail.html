<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <#--widget-->

    <script src="${ctx}/js/genProCity.js" type="text/javascript"></script>
    <script src="${ctx}/js/genVBrand.js" type="text/javascript"></script>

    <link href="${ctx}/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>


    <script type="text/javascript">
        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }


        var layerTop = top.layer;

        var scrollTop;  // 定义滚动高度

        const loader = new Loaders({style: "rectangle"});

        const proBrandPickerData = new GenVBrand().getValue();

        //是否赔案审核，是否报案案件，是否赔案案件
        var isCaseVerify = false;
        var isReportCase = false;
        var isPayCase = false;
        var tabStatus="${tabStatus}";
        $(document).ready(function () {

            <#if isClaimVerify?? && isClaimVerify=="1">
                isCaseVerify = true;
            <#elseif claimCase.status?contains("aax")>
                isReportCase = true;
            <#else >
                isPayCase = true;
            </#if>

            $("#carBrand").select2({
                placeholder: "请选择",
                width: null
            });

            var brand = "${claimCase.carBrand}";
            //初始化 品牌
            proBrandPickerData.forEach(function (item, index) {
                var childs = item['childs'];
                for (var i in childs) {
                    var value = childs[i];
                    var brandOption = document.createElement("option");
                    brandOption.innerText = value['value'];
                    brandOption.value = value['id'];
                    if (brand == value['id']) {
                        brandOption.selected = true;
                        $("#select2-carBrand-container").text(brand);
                    }
                    $("#carBrand").append(brandOption);
                }
            });

            $(".logListInfo").on("click", ".rowInfo", function (e) {
                e.stopPropagation();
                let icon = $(this).children(1).children(1);
                var iconClass = icon.attr("class");
                if (iconClass == 'icon-plus') {
                    icon.attr("class", "minus");
                    $(this).next().show();
                }
                if (iconClass == 'minus') {
                    icon.attr("class", "icon-plus");
                    $(this).next().hide();
                }
                iframeH();
            });

            $(".attachInfo").on("click", "img", function (e) {
                bigImg($(this).attr("src"));
            });

            $(".block-head-label").on("click", "input[type='checkbox']", function (e) {
                var val = $(this).is(":checked");
                if (val) {
                    $(".rowInfo").each(function () {
                        $(this).show();
                    });
                } else {
                    $(".detailsInfo").hide();
                    $(".rowInfo").each(function () {
                        if ($(this).find("td:eq(3)").text().trim() == "系统") {
                            $(this).hide();
                        }
                    });
                }
                iframeH();
            });

            // 计算滚动高度
            function calculationScrollTop() {
                var ifm = parent.document.getElementById("contentFrame");
                scrollTop = $(window.parent).scrollTop() + 150;
                if (!ifm) {
                    scrollTop = 300;
                }
                return scrollTop + "px";
            }

            $(".showDetailMsgBtn").on("click", function (e) {
                let title = $(".showDetailMsg").attr("title");
                var openWindowWidth = $(document).width() * 0.8 + "px";

                layer.open({
                    type: 1,
                    title: '经过',
                    area: openWindowWidth,
                    offset: calculationScrollTop(),
                    fix: false, //不固定
                    maxmin: true,
                    content: "<div style='background-color: #FFFFFF;color:black;padding: 50px'>" + title + "</div>",
                    success: function (layero, index) {
                        layer.iframeAuto(index);
                    }
                });
            });


            freshStatistics("0");
            (function () {
                $("img").each(function (idx, obj) {
                    if (!$(obj).attr("data-url")) {
                        return true;
                    }
                    let img = new Image();
                    img.src = $(obj).attr("data-url");
                    /*img.onload方法里如果不是自调用函数，则取不到对应的obj*/
                    img.onload = (function (e) {
                        $(obj).attr("src", $(obj).attr("data-url"));
                    })();
                    img.onerror = (function (e) {
                        $(obj).attr("src", '${ctx}/a/job_done.png');
                    });
                });

            })();

            //初始化赔付信息
            freshDutyInfo();

            if (isCaseVerify) {
                checkVerifCaseStatusIsError('${claimCase.id}', 'abx10');
            }

            $("#accidentType").select2({
                placeholder: "请选择",
                width: null,
                data: [{id: '', text: '请选择'}, {id: '单方', text: '单方'}, {id: '多方', text: '多方'}]
            });
            <#if claimCase.accidentType?exists>
            $("#accidentType").val(["${claimCase.accidentType}"]).trigger('change');
            </#if>

            $("#accidentLiability").select2({
                placeholder: "请选择",
                width: null
            });
            <#if claimCase.accidentLiability?exists>
            $("#accidentLiability").val(["${claimCase.accidentLiability}"]).trigger('change');
            </#if>

            $(".container-boby").on("click", ".select", function (e) {
                var fieldName = $(this).attr("fieldName");
                var oldValue = $("#" + fieldName).val();
                scrollTop = calculationScrollTop();
                layer.open({
                    title: "修改字段",
                    type: 1,
                    content: $('#' + fieldName + '-div'),
                    area: ['650px', '200px'],
                    fixed: false,
                    offset: 't',
                    btn: ['确认', '取消'],
                    shade: [0.8, '#393D49'],
                    shadeClose: true,
                    offset: scrollTop,
                    yes: function (index, layero) {
                        let value = $("#" + fieldName).val();
                        if (!value) {
                            layer.msg("输入的值不能为空", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        if (oldValue == value) {
                            layer.msg("请输入要修改的值", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        if (fieldName == "accidentProportion") {
                            var reg = /^\d+$/;
                            let result = false;
                            if (reg.test(value) && (value >= 0 && value <= 100)) {
                                result = true;
                            }
                            if (!result) {
                                layer.msg("事故比列格式错误，只能为0-100的整数！！", {
                                    icon: 2,
                                    time: 2000,
                                    offset: scrollTop
                                });
                                return;
                            }
                        }
                        var formData = new FormData();
                        formData.append("id", "${claimCase.id}");
                        formData.append("fieldName", fieldName);
                        formData.append(fieldName, value);
                        $.ajax({
                            url: "${ctx}/claimCaseController/setValueByFieldName",
                            type: 'POST',
                            data: formData,
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg("修改成功", {
                                        icon: 1,
                                        time: 2000,
                                        offset: scrollTop
                                    }, function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000,
                                        offset: scrollTop
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    },
                    btn2: function (index, layero) {
                        layer.closeAll();
                    }
                });
            });


            const data_address = `${claimCase.address}`;
            const data_description = `${claimCase.description}`;
            const data_plateNumber = `${claimCase.plateNumber}`;
            const data_treatMobile = `${claimCase.treatMobile}`;

            $(".container-boby").on("click", ".setPromptOpen", function (e) {
                var fieldName = $(this).attr("data-fieldName");
                var formType = $(this).attr("data-formType");
                var oldValue = "";
                switch (fieldName) {
                    case "address":{
                        oldValue = data_address
                        break;
                    }
                    case "description":{
                        oldValue = data_description
                        break;
                    }
                    case "plateNumber":{
                        oldValue = data_plateNumber
                        break;
                    }
                    case "treatMobile":{
                        oldValue = data_treatMobile
                        break;
                    }
                }

                scrollTop = calculationScrollTop();
                layer.prompt({
                    formType: formType,        //0（文本）默认1（密码）2（文本域）
                    value: oldValue,
                    title: '请输入值',
                    fixed: false,
                    offset: scrollTop,
                    area: formType == 0 ? 'auto' : ['800px', '500px'],
                    closeBtn: 0,
                    yes: function (index, layero) {
                        let value = layero.find(".layui-layer-input").val();
                        if (!value) {
                            layer.msg("输入的值不能为空", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        if (oldValue == value) {
                            layer.msg("请输入要修改的值", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        const mobileCheck = /^1[3456789][0-9]\d{8}$/;
                        if (fieldName == "treatMobile" && !mobileCheck.test(value)) {
                            layer.msg("出险人手机号不合法", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        var formData = new FormData();
                        formData.append("id", "${claimCase.id}");
                        formData.append("fieldName", fieldName);
                        formData.append(fieldName, value);
                        $.ajax({
                            url: "${ctx}/claimCaseController/setValueByFieldName",
                            type: 'POST',
                            data: formData,
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg("修改成功", {
                                        icon: 1,
                                        time: 2000,
                                        offset: scrollTop
                                    }, function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000,
                                        offset: scrollTop
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                });
            });

            $(".container-boby").on("click", ".knightlllegalItems", function (e) {
                scrollTop = calculationScrollTop();
                let oldValue = "${(((claimCase.exInfo!'{}')?eval).knightlllegalItems)!''}";
                layer.open({
                    title: "修改事故违规类型",
                    type: 1,
                    content: $("#knightlllegalItemsContainer"),
                    area: ['800px', '300px'],
                    fixed: false,
                    offset: 't',
                    btn: ['确认', '取消'],
                    shade: [0.8, '#393D49'],
                    shadeClose: true,
                    offset: scrollTop,
                    yes: function (index, layero) {
                        let value = "";
                        $("#knightlllegalItemsContainer").find("button[class='knightlllegalItems-blue']").each(function() {
                            value += "," + $(this).attr("data-value");
                        });
                        if (value) {
                            value = value.substr(1);
                        }
                        if (!value) {
                            layer.msg("请至少选择一项", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        if (oldValue == value) {
                            layer.msg("请选择要修改的选项", {
                                icon: 2,
                                time: 2000,
                                offset: scrollTop
                            });
                            return;
                        }
                        var formData = new FormData();
                        formData.append("id", "${claimCase.id}");
                        formData.append("fieldName", "exInfo");
                        formData.append("exInfo", value);
                        $.ajax({
                            url: "${ctx}/claimCaseController/setValueByFieldName",
                            type: 'POST',
                            data: formData,
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg("修改成功", {
                                        icon: 1,
                                        time: 2000,
                                        offset: scrollTop
                                    }, function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2000,
                                        offset: scrollTop
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    },
                    btn2: function (index, layero) {
                        layer.closeAll();
                    }
                });
            });

        });



        function checkVerifCaseStatusIsError(caseId, status) {
            if (caseId != undefined && caseId.trim() != "") {
                var isError = false;
                setInterval(function () {
                    if (!isError) {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/getCaseInfo",
                            type: 'POST',
                            data: formData,
                            async: true,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    if (result.msg != status) {
                                        isError = true;
                                        layer.msg("案件状态已修改！！！", {icon: 2, time: -1, shade: [0.5, '#000000']})
                                    }
                                } else {
                                    console.log(data);
                                }
                            },
                            error: function (data) {
                                console.log(data);
                            }
                        });
                    }
                }, 5000)

            }
        }

        function getLossAdjustingInfos(claimCaseId){
            var formData = new FormData();
            formData.append("claimCaseId", claimCaseId);
            $.ajax({
                url: "${ctx}/lossAdjustingController/getLossAdjustingInfosByclaimCaseId",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    try {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            let dataList = JSON.parse(result.data);
                            if (dataList) {
                                $.each(dataList, function (index, obj) {
                                    let estimatedAmounts = "-";
                                    if (obj.estimatedAmount != undefined) {
                                        estimatedAmounts = obj.estimatedAmount;
                                    }
                                    let gn = "";
                                    gn = '<a onclick="showLossAdjustingObject(\'' + obj.id + '\')">修改</a>';
                                    gn += '<a onclick="delLossAdjustingObject(\'' + obj.id + '\')">删除</a>';
                                    let init = $('<tr>\n' +
                                        '                                <td>公估费</td>\n' +
                                        '                                <td>公估费</td>\n' +
                                        '                                <td></td>\n' +
                                        '                                <td>' + estimatedAmounts + '</td>\n' +
                                        '                                <td></td>\n' +
                                        '                                <td></td>\n' +
                                        '                                <td></td>\n' +
                                        '                                <td>' + gn + '</td>\n' +
                                        '                            </tr>');
                                    $("#dutyInfoShow").append(init);
                                });
                            }
                        } else {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 1500 //1秒关闭（如果不配置，默认是3秒）
                            }, function (index) {
                                layer.close(index);
                            });
                        }
                    } catch (e) {
                        console.error(e);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }
        //刷新请求后台，责任数据
        function freshDutyInfo() {
            $("#dutyInfoShow").html("");
            var formData = new FormData();
            formData.append("claimCaseId", '${claimCase.id}');
            $.ajax({
                url: "${ctx}/claimCaseController/getClaimCaseObjectInfos",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    try {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            let dataList = JSON.parse(result.data);
                            if (dataList) {
                                $.each(dataList, function (index, obj) {
                                    let type = "-";
                                    switch (obj.type) {
                                        case 1:
                                            type = "代驾";
                                            break;
                                        case 2:
                                            type = "三者";
                                            break;
                                    }
                                    switch (obj.category) {
                                        case 1:
                                            type += "人伤";
                                            break;
                                        case 2:
                                            type += "物损";
                                            break;
                                        case 3:
                                            type += "车损";
                                            break;
                                    }
                                    let name = "-";
                                    if (obj.name != undefined) {
                                        name = obj.name;
                                    }
                                    let mobile = "-";
                                    if (obj.mobile != undefined) {
                                        mobile = obj.mobile;
                                    }
                                    let estimatedApprovedMoney = "-";
                                    if (obj.estimatedApprovedMoney != undefined) {
                                        estimatedApprovedMoney = obj.estimatedApprovedMoney;
                                    }
                                    let accidentProportion = "-";
                                    if (obj.accidentProportion != undefined) {
                                        accidentProportion = obj.accidentProportion;
                                    }
                                    let hospitalName = "-";
                                    if (obj.hospitalName != undefined) {
                                        hospitalName = obj.hospitalName;
                                    }
                                    let status = "-";
                                    if (obj.status != undefined) {
                                        status = obj.status;
                                    }
                                    let auditer = "-";
                                    if (obj.auditer != undefined) {
                                        auditer = obj.auditer;
                                    }
                                    let gn = "";

                                <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") && claimCase.status != "acx21">
                                    <@shiro.hasPermission name="CLAIM_CASE_OBJECT_MODIFY">
                                        gn = '<a onclick="editObject(\'' + obj.id + '\')">基础信息</a>';
                                    </@shiro.hasPermission>
                                    <@shiro.hasPermission name="CLAIM_CASE_OBJECT_DEL">
                                        gn += '<a onclick="delObject(\'' + obj.id + '\')">删除</a>';
                                    </@shiro.hasPermission>
                                </#if>
                                <@shiro.hasPermission name="CLAIM_CASE_OBJECT_LOOK">
                                    gn += '<a onclick="showObject(\'' + obj.id + '\')">估损表</a>';
                                </@shiro.hasPermission>
                                    if('${tabStatus}' == 16 && status == "影像材料已补充"){
                                        console.log("校验成功");
                                        gn += '<a onclick="auditing(\'' + obj.id + '\')">审核</a>';
                                        let init = $('<tr>\n' +
                                            '                                <td>' + type + '</td>\n' +
                                            '                                <td>' + name + '</td>\n' +
                                            '                                <td>' + mobile + '</td>\n' +
                                            '                                <td>' + estimatedApprovedMoney + '</td>\n' +
                                            '                                <td>' + hospitalName + '</td>\n' +
                                            '                                <td>' + status + '</td>\n' +
                                            '                                <td>' + auditer + '</td>\n' +
                                            '                                <td>' + gn + '</td>\n' +
                                            '                            </tr>');
                                        $("#dutyInfoShow").append(init);
                                    }else if('${tabStatus}' != 16){
                                        let init = $('<tr>\n' +
                                            '                                <td>' + type + '</td>\n' +
                                            '                                <td>' + name + '</td>\n' +
                                            '                                <td>' + mobile + '</td>\n' +
                                            '                                <td>' + estimatedApprovedMoney + '</td>\n' +
                                            '                                <td>' + hospitalName + '</td>\n' +
                                            '                                <td>' + status + '</td>\n' +
                                            '                                <td>' + auditer + '</td>\n' +
                                            '                                <td>' + gn + '</td>\n' +
                                            '                            </tr>');
                                        $("#dutyInfoShow").append(init);
                                    }
                                });
                            }
                        } else {
                            layer.msg(result.msg, {
                                icon: 1,
                                time: 1500 //1秒关闭（如果不配置，默认是3秒）
                            }, function (index) {
                                layer.close(index);
                            });
                        }
                    } catch (e) {
                        console.error(e);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
            if('${tabStatus}' != 16){
                getLossAdjustingInfos('${claimCase.id}');
            }

        }

        // 赔付对象 基本信息
        function editObject(objectId) {
            if (objectId == undefined) {
                objectId = "";
            }
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '损失项目',
                area: ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/editPretreatmentCaseObject?objectId=" + objectId + "&claimCaseId=${claimCase.id}",
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 公估对象 基本信息
        function showLossAdjustingObject(objectId) {
            if (objectId == undefined) {
                objectId = "";
            }
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '赔付信息',
                area: ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/lossAdjustingController/showLossAdjustingObject?objectId=" + objectId + "&claimCaseId=${claimCase.id}&claimCaseNo=${claimCase.claimCaseNo}",
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function delLossAdjustingObject(objectId) {
            scrollTop = calculationScrollTop();
            var formData = new FormData();
            formData.append("id", objectId);
            if (objectId == undefined || objectId == "") {
                layer.msg("公估项目不存在", {icon: 2, time: 1500});
                freshDutyInfo();
                return;
            }

            layer.confirm("是否确认删除？", {icon: 3, title: '温馨提示', offset: scrollTop}, function (index) {
                $.ajax({
                    url: "${ctx}/lossAdjustingController/lossAdjustingDelete?id=" + objectId,
                    type: 'POST',
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 1500, offset: scrollTop}, function (index) {
                                window.location.reload();
                                layer.close(index);
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1500, offset: scrollTop}, function (index) {
                                freshDutyInfo();
                                layer.close(index);
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }


        // 删除赔付对象
        function delObject(objectId) {
            scrollTop = calculationScrollTop();
            if (objectId == undefined || objectId == "") {
                layer.msg("损失项目不存在", {icon: 2, time: 1500});
                freshDutyInfo();
                return;
            }
            layer.confirm("是否确认删除？", {icon: 3, title: '温馨提示', offset: scrollTop}, function (index) {
                $.ajax({
                        url: "${ctx}/claimCaseObjectController/claimCaseObjectDelete?claimCaseObjectId=" + objectId,
                        type: 'POST',
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 1500, offset: scrollTop}, function (index) {
                                    window.location.reload();
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(result.msg, {icon: 2, time: 1500, offset: scrollTop}, function (index) {
                                    freshDutyInfo();
                                    layer.close(index);
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
            });
        }

        // 查看赔付对象
        function showObject(id) {
            window.location.href = "${ctx}/claimCaseObjectController/showObject?id="+id;
        }

        //审核
        function auditing(objectId){
            if (objectId == undefined) {
                objectId = "";
            }
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '审核',
                area: ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/auditingClaimCaseObject?objectId=" + objectId + "&claimCaseId=${claimCase.id}",
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });

        }

        //电子凭证
        function onloadEPolicy(policyPersonId) {
            if (typeof policyPersonId == 'undefined' || policyPersonId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("policyPersonId", policyPersonId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/onloadEPolicy",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //电子保单
        function onloadInteriorPolicy(policyPersonId) {
            if (typeof policyPersonId == 'undefined' || policyPersonId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("policyPersonId", policyPersonId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/onloadInteriorPolicy",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //查看保险方案
        function seePlanName(planId) {
            if (typeof planId == 'undefined' || planId == '') {
                layerTop.msg('暂未数据！！！', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            var formData = new FormData();
            formData.append("planId", planId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/seePlanNameByPlanId",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }

        //出险人身份证影像
        function seeTreatIdTypeAttach(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '出险人身份证影像信息',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/claimCaseAttachFile?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //补发短信
        function reSendMessage(claimCaseId) {
            scrollTop = calculationScrollTop();
            layer.open({
                title: "补发短信",
                type: 1,
                content: $('#reSendMessageContainer'),
                area: ['700px', '300px'],
                fixed: false,
                offset: scrollTop,
                btn: ['确认', '取消'],
                closeBtn: 0,
                yes: function (index, layero) {
                    let check = $('#reSendMessageContainer').find('span.checked');
                    let children = check.children();
                    let code = children.attr("code");
                    if (typeof code == "undefined" || code == null) {
                        layer.msg("请选择短信类型！！！", {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                        return;
                    }
                    let reissueReason = $("#reissueReason").val();
                    if (reissueReason.trim() == "") {
                        layer.msg("请输入补发原因！！！", {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                        return;
                    }
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("messageCode", code);
                    formData.append("reissueReason", reissueReason);
                    $.ajax({
                        url: "${ctx}/insuranceCaseController/reSendMessage",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layerTop.msg("发送成功", {
                                    icon: 1,
                                    time: 2000
                                }, function () {
                                    layer.closeAll();
                                });
                            } else {
                                layerTop.msg(result.msg, {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }

        //催办记录
        function pressDoLog() {
            layerTop.msg("待建设！！！", {
                icon: 3,
                time: 2000
            });
        }

        //查看饿了么估损金额
        function modifyAppraisalAmount(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.5 + "px";
            scrollTop = calculationScrollTop();
            let url;
            if (isReportCase) {
                url = "${ctx}/claimCaseController/modifyAppraisalAmount?claimCaseId=" + claimCaseId;
            } else {
                url = "${ctx}/insuranceCaseController/modifyAppraisalAmount?claimCaseId=" + claimCaseId;
            }
            layer.open({
                type: 2,
                title: '查看估损金额',
                area: openWindowWidth,
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: url,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //回显饿了么估损金额
        function changeAppraisalAmount(money) {
            $("#appraisalAmount").html('估损金额（旧版）：' + money + '元');
        }

        //查看估损金额
        function modifyObjectAppraisalAmount(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.5 + "px";
            scrollTop = calculationScrollTop();
            let url;
            if (isReportCase) {
                url = "${ctx}/claimCaseController/modifyObjectAppraisalAmount?claimCaseId=" + claimCaseId;
            } else {
                url = "${ctx}/insuranceCaseController/modifyObjectAppraisalAmount?claimCaseId=" + claimCaseId;
            }
            layer.open({
                type: 2,
                title: '查看估损金额',
                area: openWindowWidth,
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: url,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //回显估损金额
        function changeObjectAppraisalAmount(money) {
            $("#objectAppraisalAmount").html('估损金额（新版）：' + money + '元');
            window.location.reload();
        }

        //添加日志
        function addCaseLog(claimCaseId) {
            let areaMsg = $("#logTextArea").val().trim();
            if (areaMsg == '') {
                layer.tips("请添加日志描述！！", '#logTextArea', {tips: 1});
                return;
            }

            var formData = new FormData();
            formData.append("claimCaseId", claimCaseId);
            formData.append("description", areaMsg);
            $("#addCaseLog").attr("disabled", "disabled");
            $.ajax({
                url: "${ctx}/insuranceCaseController/addCaseLog",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        let logMsg = result.data;
                        console.log(logMsg);
                        $(".logListInfo tbody").prepend(`
                        <tr class="rowInfo">
                                <td width="10%" align="center">
                                    <div class="icon-plus"></div>
                                </td>
                                <td>` + logMsg.position + `</td>
                                <td>` + logMsg.status + `</td>
                                <td>` + logMsg.creator.substring(0, logMsg.creator.indexOf("-")) + `</td>
                                <td>` + timeStamp2String(logMsg.createTime) + `</td>
                            </tr>
                            <tr class="detailsInfo">
                                <td></td>
                                <td colspan="4">` + logMsg.description + `</td>
                            </tr>
                        `);
                        modifyLabel();
                        iframeH();
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

            // 设置新增备注30秒内不能重复提交
            setTimeout(function () {
                $("#addCaseLog").removeAttr("disabled");
            }, 30000);

            console.log(areaMsg);
        }

        //修改标签
        function modifyLabel() {
            scrollTop = calculationScrollTop();
            let dataList = [];
            let check = $('#editLabelContainer').find('span.checked');
            $.each(check, function (index, obj) {
                let code = $(this).children().attr("code");
                if (!(typeof code == "undefined" || code == null)) {
                    dataList.push(code);
                }
            })
            /*if (dataList.length == 0) {
                layer.msg("至少选择一个标签类型！！！", {
                    icon: 2,
                    time: 2000,
                    offset: scrollTop
                });
                return;
            }*/
            var formData = new FormData();
            formData.append("claimCaseId", "${claimCase.id}");
            formData.append("labelCode", dataList.join(","));
            $.ajax({
                url: "${ctx}/insuranceCaseController/editCaseLabelAction",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg("成功", {
                            icon: 1,
                            time: 3000,
                            offset: scrollTop
                        }, function () {
                            window.location.reload();
                        });
                    } else {
                        console.log(data);
                    }
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        //标记是否疑难
        function markDiffcultCase(e) {
            scrollTop = calculationScrollTop();
            let difficultCase = "${claimCase.isDifficultCase}";
            /*var X = $(obj).offset().top;  //获取当前元素x坐标
            var Y = $(obj).offset().left; //获取当前元素y坐标*/
            if (difficultCase + "" != 1) {
                var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control"  id="markDiffcultCaseMsg" autocomplete="off" placeholder="请输入挂起原因"></textarea></div>';
                layer.open({
                    type: 1,
                    content: content,
                    title: '案件挂起',
                    area: ['500px', '300px'],
                    btn: ['确认', '取消'],
                    offset: scrollTop,
                    yes: function (index, obj) {
                        var hangCaseMsg = $("#markDiffcultCaseMsg").val();
                        if (typeof hangCaseMsg != 'string' || hangCaseMsg.trim() == '') {
                            layer.msg("挂起原因不能为空", {icon: 2, time: 3000, offset: scrollTop});
                        } else {
                            var formData = new FormData();
                            formData.append("claimCaseId", "${claimCase.id}");
                            let type;
                            let url;
                            if (isReportCase) {
                                type = 1;
                            }
                            if (isPayCase) {
                                type = 5;
                            }
                            if (isCaseVerify) {
                                type = 7;
                                formData.append("isSkip", 1);
                            }
                            formData.append("type", type);
                            formData.append("description", hangCaseMsg);
                            $.ajax({
                                url: "${ctx}/insuranceCaseController/markDiffcultCase",
                                type: 'POST',
                                data: formData,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        layer.msg('案件挂起成功', {
                                            icon: 1,
                                            time: 1500,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    } else {
                                        if (result.ret == "1001") {
                                            layer.confirm(
                                                result.msg, {icon: 2, title: '执行', offset: scrollTop}, function (index) {
                                                    var formData = new FormData();
                                                    formData.append("claimCaseId", "${claimCase.id}");
                                                    formData.append("type", type);
                                                    formData.append("isSkip", 1);
                                                    formData.append("description", hangCaseMsg);
                                                    $.ajax({
                                                        url: "${ctx}/insuranceCaseController/markDiffcultCase",
                                                        type: 'POST',
                                                        data: formData,
                                                        async: false,
                                                        cache: false,
                                                        contentType: false,
                                                        processData: false,
                                                        success: function (data) {
                                                            var result = eval("(" + data + ")");
                                                            if (result.ret == "0") {
                                                                layer.msg('案件挂起成功', {
                                                                    icon: 1,
                                                                    time: 1500,
                                                                    offset: scrollTop
                                                                }, function () {
                                                                    window.location.reload();
                                                                });
                                                            } else {
                                                                layer.msg(result.msg, {
                                                                    icon: 2,
                                                                    time: 3000,
                                                                    offset: scrollTop
                                                                }, function (index) {
                                                                    layer.close(index);
                                                                });
                                                            }
                                                        },
                                                        error: function (data) {
                                                            var result = eval("(" + data + ")");
                                                            alert(result.msg);
                                                        }
                                                    })
                                                }
                                            );
                                        } else {
                                            layer.msg(result.msg, {
                                                icon: 2,
                                                time: 3000,
                                                offset: scrollTop
                                            }, function (index) {
                                                layer.close(index);
                                            });
                                        }

                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    alert(result.msg);
                                }
                            });
                        }
                    }
                });
            } else {
                layer.confirm(
                    '是否取消疑难?', {icon: 2, title: '执行', offset: scrollTop}, function (index) {
                        var formData = new FormData();
                        formData.append("claimCaseId", "${claimCase.id}");
                        let type;
                        if (isReportCase) {
                            type = 2;
                        }
                        if (isPayCase) {
                            type = 6;
                        }
                        formData.append("type", type);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/markDiffcultCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg('案件取消挂起成功', {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 3000,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });

                    }
                )
            }


        }

        //再次来电切换
        function changeCustomerTask(e) {
            scrollTop = calculationScrollTop();
            var formData = new FormData();
            formData.append("claimCaseId", "${claimCase.id}");
            if ($(e).text().trim() == "再次去电") {
                formData.append("phoneType", 0);
            }
            if ($(e).text().trim() == "去电完成") {
                formData.append("phoneType", 1);
            }
            let type;
            if (isPayCase) {
                type = 101;
            }
            if (isReportCase) {
                type = 4;
            }
            if (isCaseVerify) {
                type = 102;
            }
            formData.append("type", type);
            layer.closeAll()
            layer.msg("提交中..", {
                icon: 16,
                time: -1,
                shade: [0.5, '#ffffff'],
                offset: scrollTop
            });
            $.ajax({
                url: "${ctx}/insuranceCaseController/againPhone",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");

                    setTimeout(function () {
                        layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function (index) {
                            if (result.ret == '0') {
                                window.location.reload();
                            } else {
                                layer.close(index);
                            }
                        });
                    }, 1000);
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
            if (isCaseVerify) {
                let logText = $("#logTextArea").val();
                if (logText != undefined && logText.trim() != "") {
                    addCaseLog("${claimCase.id}");
                }
            }
        }

        //编辑影像
        function editAttach(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            scrollTop = calculationScrollTop();
            let url = "${ctx}/claimCaseController/editAttach?claimCaseId=" + claimCaseId;
            layer.open({
                type: 2,
                title: '影像编辑',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: url,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //时间格式化
        function timeStamp2String(time) {
            var datetime = new Date();
            datetime.setTime(time);
            var year = datetime.getFullYear();
            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
            var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
            var hour = datetime.getHours() < 10 ? "0" + datetime.getHours() : datetime.getHours();
            var minute = datetime.getMinutes() < 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();
            var second = datetime.getSeconds() < 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();
            return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
        }

        function showDetail(subjectId) {
            window.open("${ctx}/insuranceCaseController/claimSubjectShow?claimCaseSubjectId=" + subjectId);
        }

        //案件关闭
        function closeCase(caseId) {
            scrollTop = calculationScrollTop();
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>';
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area: ['500px', '300px'],
                offset: scrollTop,
                btn: ['确认', '取消'],
                yes: function (index, obj) {
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if (typeof closeCaseMsg != 'string' || closeCaseMsg.trim() == '') {
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000, offset: scrollTop});
                    } else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        let type;
                        if (isCaseVerify) {
                            type = 102;
                        }
                        if (isReportCase) {
                            type = 3;
                        }
                        if (isPayCase) {
                            type = 101;
                        }
                        formData.append("type", type);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/claimCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    setTimeout(function () {
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 3000,
                                            shade: [0.3, '#000000'],
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    }, 1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        function exportTemplates(caseId) {
            if (caseId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            scrollTop = calculationScrollTop();
            layer.open({
                title: "模版导出",
                type: 1,
                area: ['500px', '300px'],
                fixed: false,
                offset: scrollTop,
                closeBtn: 1,
                content: $('#exportTemplateContainer'),
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function exportLossAdvice(id) {
            window.location.href = "${ctx}/claimCaseController/exportLossAdvice?claimCaseId=" + id;
        }

        function exportLegalConveyancing(id) {
            window.location.href = "${ctx}/claimCaseController/exportLegalConveyancing?claimCaseId=" + id;
        }

        function exportMediationAgreement(id) {
            window.location.href = "${ctx}/claimCaseController/exportMediationAgreement?claimCaseId=" + id;
        }

        function exportProcessAgreement(id) {
            window.location.href = "${ctx}/claimCaseController/exportProcessAgreement?claimCaseId=" + id;
        }

        function freshStatistics(type) {
            scrollTop = calculationScrollTop();
            let baseUserId = "${claimCase.baseUserId}";
            let formData = new FormData();
            formData.append("baseUserId", baseUserId)
            $.ajax({
                url: "${ctx}/insuranceCaseController/getBaseUserStatistics",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        if (type == "1") {
                            layerTop.msg("刷新成功！！", {
                                icon: 1,
                                time: 1000
                            });
                        }
                        $("#lastStatisticsData").html(result.lastStatisticsData);
                        $("#times").html(result.times + "次");
                        $("#sumTimesRate").html(result.sumTimesRate + "%");
                        $("#continuousTimes").html(result.continuousTimes + "次");
                        $("#sumNear30TimesRate").html(result.sumNear30TimesRate + "%");
                        $("#near30DayTimes").html(result.near30DayTimes + "次");
                        $("#lastTimePolicyPersonDate").html(result.lastTimePolicyPersonDate);
                        $("#lastPolicyPersonDate").html(result.lastPolicyPersonDate);
                    } else {
                        if (type == "1") {
                            layer.msg(result.msg, {
                                icon: 2,
                                time: 1500,
                                offset: scrollTop
                            });
                        }

                    }
                },
                error: function (data) {
                    if (type == "1") {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                }
            });
        }

        function caseVerify(claimCaseId) {
            window.open("${ctx}/insuranceCaseController/claimVerify?claimCaseId=" + claimCaseId);
        }


        function getHistoryRecord(baseUserId, insCode) {
            scrollTop = calculationScrollTop();
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000, offset: scrollTop});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '历史保单',
                area: [openWindowWidth, '700px'],
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistoryRecordV2?baseUserId=" + baseUserId + "&insCode=" + insCode,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

        function verifyCase(claimCaseId) {
            window.open("${ctx}/insuranceCaseController/claimVerify?claimCaseId=" + claimCaseId);
        }

        //查看人员详情
        function viewPersonDetail(policyPersonId) {
            if (policyPersonId.trim() == "") {
                layer.msg("暂无人员信息", {icon: 2, time: 2000, offset: "100px"}, function (index) {
                    layer.close(index);
                });
                return;
            }
            window.open("${ctx}/personnelManagementController/personnelManagementDetail?policyPersonId=" + policyPersonId);

        }

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        function bigImg(src) {
            imgShow("#outerdiv", "#innerdiv", "#bigimg", src);
        }


        function imgShow(outerdiv, innerdiv, bigimg, src) {
            $(bigimg).attr("src", src);//设置#bigimg元素的src属性

            /*获取当前点击图片的真实大小，并显示弹出层及大图*/
            $(bigimg).attr("src", src).load(function () {
                var windowW = $(window).width();//获取当前窗口宽度
                var windowH = $(window).height();//获取当前窗口高度
                var realWidth = this.width;//获取图片真实宽度
                var realHeight = this.height;//获取图片真实高度
                var imgWidth, imgHeight;
                var scale = 0.8;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放

                if (realHeight > windowH * scale) {//判断图片高度
                    imgHeight = windowH * scale;//如大于窗口高度，图片高度进行缩放
                    imgWidth = imgHeight / realHeight * realWidth;//等比例缩放宽度
                    if (imgWidth > windowW * scale) {//如宽度扔大于窗口宽度
                        imgWidth = windowW * scale;//再对宽度进行缩放
                    }
                } else if (realWidth > windowW * scale) {//如图片高度合适，判断图片宽度
                    imgWidth = windowW * scale;//如大于窗口宽度，图片宽度进行缩放
                    imgHeight = imgWidth / realWidth * realHeight;//等比例缩放高度
                } else {//如果图片真实高度和宽度都符合要求，高宽不变
                    imgWidth = realWidth;
                    imgHeight = realHeight;
                }
                $(bigimg).css("width", imgWidth);//以最终的宽度对图片缩放

                var w = (windowW - imgWidth) / 2;//计算图片与窗口左边距
                var h = $(window.parent).scrollTop();//计算图片与窗口上边距
                var ifm = parent.document.getElementById("contentFrame");
                if (!ifm) {
                    h = 100;
                }
                var maxHeight = windowH - h - 100;
                if (maxHeight < 0) {
                    maxHeight = 100;
                }

                $(innerdiv).css({"top": h, "left": w, "overflow-x": "auto", "max-height": maxHeight});//设置#innerdiv的top和left属性
                $(outerdiv).fadeIn("fast");//淡入显示#outerdiv及.pimg
            });

            var imgTransferDeg = 0;
            $(window).on("keydown", function (event) {
                switch (event.keyCode) {
                    // 放大
                    case 87:
                        imgToSize(100);
                        break;
                    // 缩小
                    case 83:
                        imgToSize(-100);
                        break;
                    // 左旋
                    case 65:
                        imgReverse(imgTransferDeg = imgTransferDeg - 90);
                        break;
                    // 右旋
                    case 68:
                        imgReverse(imgTransferDeg = imgTransferDeg + 90);
                        break;
                }
            });

            $(outerdiv).click(function () {//再次点击淡出消失弹出层
                $(this).fadeOut("fast");
                $(window).off("keydown");
                $("#bigimg").attr("sytle", "border: 5px solid #fff;");
            });
        }

        //放大缩小图片
        function imgToSize(size) {
            var img = $("#bigimg");
            var oWidth = img.width(); //取得图片的实际宽度
            // var oHeight = img.height(); //取得图片的实际高度
            img.width(oWidth + size);
            // img.height((oHeight + size )/ oWidth * oHeight);

            var windowW = $(window).width();//获取当前窗口宽度
            var w = (windowW - oWidth) / 2;//计算图片与窗口左边距
            var h = $(window.parent).scrollTop();//计算图片与窗口上边距
            var ifm = parent.document.getElementById("contentFrame");
            if (!ifm) {
                h = 100;
            }
            $("#innerdiv").css({"top": h, "left": w});//设置#innerdiv的top和left属性
        }

        // 翻转图片
        function imgReverse(arg) {

            var img = $("#bigimg");
            img.css({"-webkit-transform": "rotate(" + arg + "deg)"});
        }

        $(document).ready(function () {
            //添加信息
            $(".estimateInventoryInfo").on("click", "img", function () {
                let code = $(this).attr("code");
                window.open("${ctx}/insuranceCaseController/getEstimateInventoryDetail?claimCaseId=${claimCase.id}&code=" + $(this).attr("code"));
            });

            //估损清单删除
            $(".estimateInventoryInfo").on("click", "span.deleteF", function (e) {
                e.stopPropagation();
                const _this = this;
                let assessmentReprotId = $(_this).attr("assessmentReprotId");
                let scrollTop = calculationScrollTop();
                layer.confirm(
                    '是否删除估损清单?', {icon: 2, title: '执行', offset: scrollTop}, function (index) {
                        delEstimateInventory(assessmentReprotId, _this);
                    }
                )


            })

            $(".estimateInventoryInfo").on("click", "div[isNotice='1']", function (e) {
                let assessmentReprotId = $(this).attr("assessmentReprotId");
                let code = $(this).attr("code");
                getEstimateInventoryDetail(code, assessmentReprotId)
            });

            $("#knightlllegalItemsContainer").on("click", "button", function() {
                $(this).toggleClass("knightlllegalItems-blue");
            })

        });

        //查看估损清单详情
        function getEstimateInventoryDetail(code, id) {
            <@shiro.hasPermission name="ESTIMATE_INVENTORY_DETAIL_EDIT">
            window.open("${ctx}/insuranceCaseController/getEstimateInventoryDetail?claimCaseId=${claimCase.id}&code=" + code + "&assessmentReportId=" + id);
            return;
            </@shiro.hasPermission>
            <@shiro.hasPermission name="ESTIMATE_INVENTORY_DETAIL_VIEW">
            window.open("${ctx}/insuranceCaseController/getEstimateInventoryDetail?type=VIEW&claimCaseId=${claimCase.id}&code=" + code + "&assessmentReportId=" + id);
            </@shiro.hasPermission>
        }

        //删除估损清单
        function delEstimateInventory(id, obj) {
            scrollTop = calculationScrollTop();
            $.ajax({
                url: "${ctx}/insuranceCaseController/delEstimateInventory?id=" + id,
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data1) {
                    var result = eval("(" + data1 + ")");
                    console.log(data1);
                    if (result.ret == "0") {
                        layer.msg("删除成功！！！", {icon: 1, time: 1000, offset: scrollTop}, function () {
                            $(obj).parent().remove();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 3000, offset: scrollTop});
                    }

                },
                error: function (data) {
                    console.log(data)
                }
            });

        }

        function onloadClaimApplyBook(id) {
            scrollTop = calculationScrollTop();
            $.ajax({
                url: "${ctx}/claimCaseController/refreshBook?claimCaseId=" + id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        window.open(result.msg, "_blank");
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        //跳过审核
        function skipClaimVerify(caseId) {
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="skipCaseReason" id="skipCaseReason" autocomplete="off" placeholder="请输入跳过原因"></textarea></div>';
            layer.open({
                type: 1,
                content: content,
                title: '案件审核跳过',
                area: ['500px', '300px'],
                btn: ['确认', '取消'],
                yes: function (index, obj) {
                    var closeCaseMsg = $("#skipCaseReason").val();
                    if (typeof closeCaseMsg != 'string' || closeCaseMsg.trim() == '') {
                        layer.msg("跳过原因不能为空", {icon: 2, time: 3000, offset: 'r'});
                    } else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("skipReason", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/insuranceCaseController/skipClaimVerify",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    setTimeout(function () {
                                        layer.msg('跳过案件成功', {
                                            icon: 1,
                                            time: 1000 //1秒关闭（如果不配置，默认是3秒）
                                        }, function () {
                                            window.location.href = "${ctx}/insuranceCaseController/claimCaseList";
                                        });
                                    }, 1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500 //1秒关闭（如果不配置，默认是3秒）
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        function editDuty(claimCaseId) {
            var openWindowWidth = $(document).width() * 0.8 + "px";
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '影像责任',
                area: [openWindowWidth, '800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                shadeClose: true,
                content: "${ctx}/insuranceCaseController/editDuty?claimCaseId=" + claimCaseId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //点击赔付责任区域
        function clickDutyDetail(claimCaseSubjectId) {
            // 如果是审核过来的，则进入编辑页面，反之只做查看
            if (isCaseVerify) {
                subjectVerify(claimCaseSubjectId);
            } else {
                showDetail(claimCaseSubjectId);
            }

        }

        function subjectVerify(subjectId) {
            var openWindowWidth = $(document).width() + "px";
            var openWindowHeight = $(document).height() + "px";
            layer.open({
                type: 2,
                title: '出险人身份证影像信息',
                area: [openWindowWidth, openWindowHeight],
                offset: 't',
                fix: false, //不固定
                maxmin: true,
                shade: ["0.3", "#000000"],
                shadeClose: true,
                content: "${ctx}/insuranceCaseController/claimSubjectVerify?claimCaseSubjectId=" + subjectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function getDutyAeraInfo(claimCaseSubjectId) {
            var formData = new FormData();
            formData.append("claimCaseSubjectId", claimCaseSubjectId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/getDutyAeraInfo",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    let paramMap = new Map();
                    paramMap.set("subjectId", claimCaseSubjectId);
                    paramMap.set("data", result.msg);
                    if (result.ret == "0") {
                        paramMap.set("type", "verify");
                    }
                    modifySubjectRemark(paramMap);
                },
                error: function (data) {
                    console.log(data);
                }
            });
        }

        function modifySubjectRemark(paramMap) {
            let subjectId = paramMap.get("subjectId");
            let type = paramMap.get("type");
            let data = paramMap.get("data");
            let children = $("div[sujectArea='" + subjectId + "']").children();
            children.html(data);
            switch (type) {
                case "verify":
                    children.attr("class", "notice notice-deal");
                    break;
                case "hangUp":
                    children.attr("class", "notice notice-hangup");
                    break;
            }
        }

        //案件提交
        function subimitAll(caseId) {
            layer.closeAll()
            let indexLoad = layer.msg("提交中..", {
                icon: 16,
                time: -1,
                shade: [0.5, '#ffffff']
            })
            let caseExInfo = {};
            if (localStorage.getItem("firstEventType") != null && localStorage.getItem("firstEventType") != undefined) {
                let item = localStorage.getItem("firstEventType");
                caseExInfo["firstEventType"] = item;
            }
            if (localStorage.getItem("eventRespConfirm") != null && localStorage.getItem("eventRespConfirm") != undefined) {
                let item = localStorage.getItem("eventRespConfirm");
                caseExInfo["eventRespConfirm"] = item;
            }
            if (localStorage.getItem("accidentType") != null && localStorage.getItem("accidentType") != undefined) {
                let item = localStorage.getItem("accidentType");
                caseExInfo["accidentType"] = item;
            }
            if (localStorage.getItem("knightlllegalItems") != null && localStorage.getItem("knightlllegalItems") != undefined) {
                let item = localStorage.getItem("knightlllegalItems");
                caseExInfo["knightlllegalItems"] = item;
            }
            if (localStorage.getItem("exceptionLabel") != null && localStorage.getItem("exceptionLabel") != undefined) {
                let item = localStorage.getItem("exceptionLabel");
                caseExInfo["exceptionLabel"] = item;
            }
            if (localStorage.getItem("callPolice") != null && localStorage.getItem("callPolice") != undefined) {
                let item = localStorage.getItem("callPolice");
                caseExInfo["callPolice"] = item;
            }
            localStorage.removeItem("firstEventType");
            localStorage.removeItem("eventRespConfirm");
            localStorage.removeItem("accidentType");
            localStorage.removeItem("knightlllegalItems");
            localStorage.removeItem("exceptionLabel");
            localStorage.removeItem("callPolice");
            let formData = new FormData();
            formData.append("claimCaseExInfo", JSON.stringify(caseExInfo));
            formData.append("claimCaseId", caseId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/caseAllSubmit",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        let i = setInterval(function () {
                            let formData1 = new FormData();
                            formData1.append("claimCaseId", caseId);
                            $.ajax({
                                url: "${ctx}/insuranceCaseController/getCaseIsVerify",
                                type: 'POST',
                                data: formData1,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data1) {
                                    var result = eval("(" + data1 + ")");
                                    console.log(data1);
                                    if (result.ret == "0" || result.ret == "verifyErrorReason") {
                                        var openWindowWidth = $(document).width() * 0.8 + "px";
                                        var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
                                        layer.open({
                                            type: 2,
                                            title: '查看案件理算结果',
                                            area: openWindowWidth,
                                            offset: offsetH,
                                            fix: false, //不固定
                                            maxmin: true,
                                            content: "${ctx}/insuranceCaseController/getCaseVerifyResult?claimCaseId=" + caseId,
                                            success: function (layero, index) {
                                                layer.iframeAuto(index);
                                                layer.close(indexLoad);
                                            }
                                        });
                                        clearInterval(i);
                                    }
                                    if (result.ret == "errorVerify") {
                                        layer.msg(result.msg, {icon: 2, time: -1});
                                        clearInterval(i);
                                    }

                                },
                                error: function (data) {
                                    console.log(data)
                                    clearInterval(i);
                                }
                            });
                        }, 2000);
                    } else {
                        layerTop.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
            let logText = $("#logTextArea").val();
            if (logText != undefined && logText.trim() != "") {
                addCaseLog(caseId);
            }
        }

        //补充材料页面
        function supplementaryMaterials() {
            let applyMobile = `${claimCase.applyMobile}`;
            $("#supplementaryMobile").val(applyMobile);
            $("#selfBookMobile").val(applyMobile);
            const mobileCheck = /^1[3456789][0-9]\d{8}$/;
            $("#supplementaryMobile").on("blur", function () {
                if (!$(this).val() || !(mobileCheck.test($(this).val()))) {
                    layer.tips("请填写合法的手机号码", $(this), {icon: 2, time: 2000, tips: 1});
                    $(this).val("");
                }
            });
            $("#selfBookMobile").on("blur", function () {
                if (!$(this).val() || !(mobileCheck.test($(this).val()))) {
                    layer.tips("请填写合法的手机号码", $(this), {icon: 2, time: 2000, tips: 1});
                    $(this).val("");
                }
            });
            layer.open({
                title: "补充材料",
                type: 1,
                content: $('#supplementaryMaterialsContainer'),
                area: ['600px', '300px'],
                fixed: false,
                offset: 't',
                closeBtn: 0,
                shadeClose: true,
                yes: function (index, layero) {

                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });

            let logText = $("#logTextArea").val();
            if (logText != undefined && logText.trim() != "") {
                addCaseLog("${claimCase.id}");
            }
        }

        function closeSupplementaryMaterialsContainer() {
            layer.closeAll();
        }

        //编辑标签
        /*function openLabelAction(obj, claimCaseId, labelStr) {
            scrollTop = calculationScrollTop();
            $("#editLabelContainer").find("input").parent().attr("class", "");
            if (labelStr&&labelStr.trim()!='') {
                for (let data of labelStr.split(",")) {
                    $("#editLabelContainer").find("input[code='" + data + "']").parent().attr("class", "checked");
                }
            }
            layer.open({
                title: "编辑标签",
                type: 1,
                content: $('#editLabelContainer'),
                area: ['650px', '300px'],
                fixed: false,
                offset: 't',
                btn: ['确认', '取消'],
                closeBtn: 0,
                shade: [0.8, '#393D49'],
                shadeClose: true,
                offset: scrollTop,
                yes: function (index, layero) {
                    let dataList = [];
                    let check = $('#editLabelContainer').find('span.checked');
                    $.each(check, function (index, obj) {
                        let code = $(this).children().attr("code");
                        if (!(typeof code == "undefined" || code == null)) {
                            dataList.push(code);
                        }
                    })
                    /!*if (dataList.length == 0) {
                        layer.msg("至少选择一个标签类型！！！", {
                            icon: 2,
                            time: 2000,
                            offset: scrollTop
                        });
                        return;
                    }*!/
                    var formData = new FormData();
                    formData.append("claimCaseId", claimCaseId);
                    formData.append("labelCode", dataList.join(","));
                    $.ajax({
                        url: "${ctx}/insuranceCaseController/editCaseLabelAction",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg("修改成功", {
                                    icon: 1,
                                    time: 3000,
                                    offset: scrollTop
                                }, function () {
                                    window.location.reload();
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 2000,
                                    offset: scrollTop
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                },
                btn2: function (index, layero) {
                    layer.closeAll();
                }
            });
        }*/


        function showMsg(e) {
            var title = $(e).attr("title");
            var content = $(e).next().text();
            layer.alert(content, {title: title, offset: calculationScrollTop()});
        }

        // 历史案件
        function getHistroyCaseInfo(baseUserId) {
            if (baseUserId == '') {
                layer.msg("暂无信息！！！", {icon: 2, time: 3000});
                return;
            }
            var openWindowWidth = $(document).width() * 0.8 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 120 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 2,
                title: '查看历史案件',
                area: openWindowWidth,
                offset: offsetH,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/insuranceCaseController/getHistroyCaseInfo?baseUserId=" + baseUserId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 挂起
        function hangUp() {
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '报案挂起',
                area:  ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseHangUpController/getCaseHangUpPage?claimCaseId=${claimCase.id}" + "&hangUpSource=2",
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        function copyLinkToClipboard(link) {

            console.log(link);

            // 创建一个input元素
            const input = document.createElement('input');
            // 设置input元素的value为要复制的链接
            input.value = link;

            // 将input元素添加到body中，以便能够对其调用select方法
            document.body.appendChild(input);

            // 选中input中的所有内容
            input.select();

            // 执行浏览器复制命令
            document.execCommand('copy');

            // 从body中移除input元素
            document.body.removeChild(input);

            // 可选：给用户一个反馈提示
            alert('链接已复制到剪贴板: ' + link);
        }

    </script>
    <style>


        .clear-padding {
            padding: 0px !important;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .widget-thumb .widget-thumb-heading {
            font-size: 16px !important;
        }

        .caption-subject {
            font-weight: 500 !important;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        .btn-bule:hover {
            color: #FFFFFF;
        }

        .btn-light-bule {
            color: #3662EC;
            background-color: #DEEBFF;
            border-color: #DEEBFF;
            margin-left: 3%;
        }

        .btn-light-bule:hover {
            color: #3662EC;
        }

        .container-head {
            border-bottom-width: 3px !important;
            padding-left: 10% !important;
            padding-right: 10% !important;
        }

        .container-boby {
            padding-left: 5% !important;
            padding-right: 5% !important;
        }

        .block-show {
            display: flex;
        }

        .block-head-label {
            margin-bottom: 1.5%;
            font-size: 20px;
        }

        .block-head-label a {
            font-size: 13px;
            margin-left: 10px;
        }

        .block-head-label span {
            font-size: 15px;
        }

        .block-border {
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #EFEFEF;
            border-left: 3px solid #FFFFFF;
            border-right: 3px solid #EFEFEF;
            margin-text-outline: 1.5%;
            margin-bottom: 2%;
        }

        .block-border .col-sm-2,
        .block-border .col-sm-4,
        .block-border .col-sm-8,
        .block-border .col-sm-10,
        .block-border .col-sm-12 {
            padding: 0px 1px 0px 0px !important;
        }


        .left-min-5 {
            width: 41.66666%;
            padding-bottom: 2.5%;
            background-color: #EFEFEF;
            padding-top: 2.5%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-min-5 {
            width: 41.66666%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-min-7 {
            width: 58.33334%;
            padding-bottom: 2.5%;
            background-color: white;
            padding-top: 2.5%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-min-7 {
            width: 58.33334%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }

        .left-mid-5 {
            width: 20.83333%;
            padding-bottom: 1.25%;
            background-color: #EFEFEF;
            padding-top: 1.25%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-mid-5 {
            width: 20.83333%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-mid-7 {
            width: 79.16667%;
            padding-bottom: 1.25%;
            background-color: #FFFFFF;
            padding-top: 1.25%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-mid-7 {
            width: 79.16667%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .left-max-5 {
            width: 13.88888%;
            padding-bottom: 0.833%;
            background-color: #EFEFEF;
            padding-top: 0.833%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-left-max-5 {
            width: 13.88888%;
            border-top: 3px solid #EFEFEF;
            border-bottom: 3px solid #FFFFFF;
        }

        .right-max-7 {
            width: 86.11112%;
            padding-bottom: 0.833%;
            background-color: #FFFFFF;
            padding-top: 0.833%;
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-right-max-7 {
            width: 86.11112%;
            border-top: 3px solid #FFFFFF;
            border-bottom: 3px solid #EFEFEF;
        }


        .attachInfo {
            border: 1px solid #D8D8D8;
            padding: 0px 3.5% 20px;
            margin-bottom: 2%;
        }

        .attachInfo label {
            margin-top: 20px;
        }

        .attachInfo img {
            width: 100%;
            height: 100%;
            border: 1px solid;
        }

        .attachInfo .col-sm-2 {
            margin-top: 20px !important;
        }

        .attachInfo .icon-attach {
            display: block;
            position: absolute;
            background-color: #D8D8D8;
            color: #709BF3;
            width: 20px;
            height: 25px;
            right: 1px;
            top: 1px;
            text-align: center;
            line-height: 25px;
            font-size: 10px;
        }

        .payDutyInfo {
            margin-bottom: 2%;
        }

        .payDutyInfo .col-sm-5 {
            display: block;
            margin-bottom: 20px;;
            font-weight: bold;
            font-size: 17px;
            height: 210px;
        }

        .payDutyInfo .notice {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }

        .notice-no-deal {
            background-color: #EE3232;
        }

        .notice-deal {
            background-color: #3662EC;
        }

        .notice-hangup {
            background-color: #F49929;
        }

        .notice-default {
            background-color: darkgrey;
        }


        .logInfo {
            margin-bottom: 2%;
        }

        .logInfo button {
            margin-top: 20px;
            margin-right: 30px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .logListInfo .detailsInfo > td {
            word-wrap: break-word;
            word-break: break-all;
        }

        .logListInfo .rowInfo:hover {
            cursor: pointer;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        #reSendMessageContainer .radio {
            margin-left: 0px;
        }


        .claim_subject_area:hover {
            cursor: pointer;
        }

        .subject-span {
            display: inline-block;
            margin-right: 40px;
            margin-top: 12px;
        }

        .subject-text-overflow {
            display: inline-block;
            max-width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
        }

        .span-type {
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin: 3px;
            padding: 2px 4px;
            border-color: #b6b5b5;
        }

        .knightlllegalItems-blue {
            background: #0b94ea;
            color: #fff;
            border-color: #ecebeb;
        }

        .estimateInventoryInfo {
            margin-bottom: 2%;
        }

        .estimateInventoryInfo label {
            margin-top: 20px;
            font-size: 20px;
        }

        .estimateInventoryInfo img {
            width: 100%;
            height: 100%;
        }

        .estimateInventoryInfo img:hover {
            cursor: pointer;
        }

        .estimateInventoryInfo .notice:hover {
            cursor: pointer;
        }

        .estimateInventoryInfo .col-sm-2 {
            margin-top: 20px !important;
            margin-right: 10px !important;
            width: 195px;
            height: 195px;
            padding: 0px 0px;
        }

        .estimateInventoryInfo .notice {
            width: 195px;
            height: 195px;
            border-radius: 10px;
            color: white;
            padding: 2% 5% 2% 2%;
        }

        /*默认*/
        .estimateInventoryInfo .default {
            background-color: darkgrey;
            color: white;
        }

        /*驳回*/
        .estimateInventoryInfo .reject {
            background-color: #CA0000;
            color: white;
        }

        /*完成*/
        .estimateInventoryInfo .complete {
            background-color: #1676FF;
            color: white;
        }

        /*暂存*/
        .estimateInventoryInfo .staging {
            background-color: #FF6633;
            color: white;
        }

        /*通过*/
        .estimateInventoryInfo .pass {
            background-color: #009F9F;
            color: white;
        }

        .showDetailMsgBtn {
            color: #337ab7;
            cursor: pointer;
        }

        .dutyInfoArea th,
        .dutyInfoArea td {
            text-align: center;
            border-top: 1px solid #C2C2C2 !important;
            border-bottom: 1px solid #C2C2C2 !important;
        }

        td > a {
            display: inline-block;
            margin: 3px;
        }

        input[class='layui-layer-input'] {
            outline: 0 !important;
            width: 260px !important;
            height: 36px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        .deleteF {
            color: #ffffff;
            position: absolute;
            top: 5px;
            right: 3px;
        }

        .container-head button {
            margin-top: 10px !important;
        }

        textarea[class='layui-layer-input'] {
            outline: 0 !important;
            width: 700px !important;
            height: 350px !important;
            padding-left: 10px !important;
            border: 1px solid #e6e6e6 !important;
            color: #333 !important;
            box-shadow: none !important;
        }

        .blue.select,.blue.setPromptOpen,.blue.knightlllegalItems{
            color: #1676ff;
        }

        .select2-dropdown--below {
            z-index: 999999999;
        }

        .select:hover {
            cursor: pointer;
        }

        .blue.select:hover,.blue.setPromptOpen:hover,.blue.knightlllegalItems:hover {
            cursor: pointer;
        }
    </style>
</head>

<body>

<#--编辑事故类型-->
<div style="display: none;width: 100%;height: 100%" id="accidentType-div">
    <div class="row" style="margin: 20px">
        <div class="col-sm-4 text-right">
            事故类型：
        </div>
        <div class="col-sm-6">
            <select class="form-control select2-multiple" name="accidentType"
                    id="accidentType">

            </select>
            <#--<input type="text" class="form-control select2-multiple" name="accidentType" id="accidentType" list="accidentTypeList" value="${claimCase.accidentType}">
            <datalist id="accidentTypeList">
                <option>单方</option>
                <option>多方</option>
            </datalist>-->
        </div>
    </div>
</div>

<#--编辑事故责任-->
<div style="display: none;width: 100%;height: 100%" id="accidentLiability-div">
    <div class="row" style="margin: 20px">
        <div class="col-sm-4 text-right">
            事故责任：
        </div>
        <div class="col-sm-6">
            <select class="form-control select2-multiple" name="accidentLiability"
                    id="accidentLiability">
                <option value="">请选择</option>
                <#list accidentDutyList as duty>
                    <option value="${duty}">${duty}</option>
                </#list>
            </select>
            <#--<input type="text" class="form-control" name="accidentLiability" id="accidentLiability" list="accidentLiabilityList" value="${claimCase.accidentLiability}">
            <datalist id="accidentLiabilityList">
                <option>全责</option>
                <option>主责</option>
                <option>同责</option>
                <option>次责</option>
                <option>无责</option>
            </datalist>-->
        </div>
    </div>
</div>

<#--编辑事故比例-->
<div style="display: none;width: 100%;height: 100%" id="accidentProportion-div">
    <div class="row" style="margin: 20px">
        <div class="col-sm-4 text-right">
            事故比例：
        </div>
        <div class="col-sm-6">
            <input type="text" autocomplete="off" class="form-control" name="accidentProportion" id="accidentProportion" list="accidentProportionList" value="${claimCase.accidentProportion}">
            <datalist id="accidentProportionList">
                <option>0</option>
                <option>10</option>
                <option>20</option>
                <option>30</option>
                <option>40</option>
                <option>50</option>
                <option>60</option>
                <option>70</option>
                <option>80</option>
                <option>90</option>
                <option>100</option>
            </datalist>
        </div>
    </div>
</div>

<#--编辑车辆信息-->
<div style="display: none;width: 100%;height: 100%" id="carBrand-div">
    <div class="row" style="margin: 20px">
        <div class="col-sm-4 text-right">
            车辆信息：
        </div>
        <div class="col-sm-6">
            <select class="form-control js-data-example-ajax kp-select2" name="carBrand" id="carBrand">
                <option value="">-请选择-</option>
            </select>
        </div>
    </div>
</div>

<#--编辑标签-->
<#--<div id="editLabelContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">请选择标签：</span>
        </div>
        <div class="col-sm-9" style="display:flex;align-items: center;flex-wrap: wrap;">
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                <#list labelShowMap.keySet() as key>
                    <div class="col-sm-1" style="padding-left: 0px;margin-bottom: 10px;display:flex;align-items: center;">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}" id="${key}"
                               type="checkbox"><label for="${key}" class="${key} span-type"  style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></label>
                    </div>
                </#list>
            </#if>
        </div>
    </div>
</div>-->

<#--补充材料-->
<div id="supplementaryMaterialsContainer" style="display: none;width: 100%;height: 100%">
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#supplementaryMaterials" role="tab" data-toggle="tab">影像补材</a></li>
        <li role="presentation"><a href="#bankCard" role="tab" data-toggle="tab">补充理赔申请书</a></li>
    </ul>

    <div class="tab-content">
        <#--        影像补材-->
        <div role="tabpanel" class="tab-pane active" id="supplementaryMaterials">
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">手机号：</span>
                </div>
                <div class="col-sm-9">
                    <input type="text" id="supplementaryMobile" class="form-control">
                </div>
            </div>
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择补充材料类型：</span>
                </div>
                <div class="col-sm-9">
                    <#if supplementaryMaterials?? && (supplementaryMaterials.keySet()?size>0)>
                        <#list supplementaryMaterials.keySet() as key>
                            <div class="col-sm-3" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                                <input name="labelType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                                       type="checkbox">${supplementaryMaterials.get(key)}
                            </div>
                        </#list>
                        <textarea class="form-control" id="supplementaryReason" rows="5" placeholder="请输入补材原因"></textarea>
                    </#if>
                </div>

                <div class="col-sm-12">
                    <button class="btn " style="background-color: #1676FF;color: white" id="submitModifyAppraisalAmount" onclick="supplementaryMaterialsAction()">确认
                    </button>
                    <button class="btn " style="background-color: #1676FF;color: white"
                            onclick="closeSupplementaryMaterialsContainer()">取消
                    </button>
                </div>
            </div>
        </div>

        <#--        银行卡补材-->
        <div role="tabpanel" class="tab-pane" id="bankCard">
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">手机号：</span>
                </div>
                <div class="col-sm-9">
                    <input type="text" id="selfBookMobile" class="form-control">
                </div>
            </div>
            <div class="row" style="padding-top: 30px;margin: 0px 0px">
                <div class="col-sm-3">
                    <span class="pull-right" style="font-size: 10px;color: #7f7f7f">请填写补充理赔申请书原因：</span>
                </div>
                <div class="col-sm-9">
                    <textarea class="form-control" id="selfBookReason" rows="5"></textarea>
                </div>
            </div>
            <div class="col-sm-12">
                <button class="btn " style="background-color: #1676FF;color: white" id="submitModifyAppraisalAmount" onclick="selfBookAction()">确认
                </button>
                <button class="btn " style="background-color: #1676FF;color: white"
                        onclick="closeSupplementaryMaterialsContainer()">取消
                </button>
            </div>
        </div>
    </div>

</div>

<#--事故违规类型-->
<div id="knightlllegalItemsContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">请选择事故违规类型：</span>
        </div>
        <div class="col-sm-9" style="display:flex;align-items: center;flex-wrap: wrap;">
            <#if accidentViolationTypeMap?? && (accidentViolationTypeMap.keySet()?size > 0)>
                <ul class="list-inline">
                    <#list accidentViolationTypeMap.keySet() as key>
                        <li style="margin: 5px 0px;">
                            <button type="button" class="<#if knightlllegalItemsList?contains(key)>knightlllegalItems-blue</#if>" data-value="${key}">${accidentViolationTypeMap.get(key)}</button>
                        </li>
                    </#list>
                </ul>
            </#if>
        </div>
    </div>
</div>

<#--补发短信-->
<div id="reSendMessageContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
        </div>
        <div class="col-sm-9">
            <#if messageType?? && (messageType.keySet()?size>0)>
                <#list messageType.keySet() as key>
                    <div class="col-sm-3 clear-padding" style="padding-left: 0px;margin-bottom: 10px;display: flex">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="radio">${messageType.get(key)}
                    </div>
                </#list>
            </#if>
            <textarea class="form-control" id="reissueReason" rows="5" placeholder="请输入补发原因"></textarea>
        </div>
    </div>
</div>

<#--模版导出-->
<div id="exportTemplateContainer" style="display: none;width: 100%;height: 100%; background-color: white">
    <div style="padding-top: 90px;margin: 0px 0px">
        <button class="btn btn-bule" onclick="exportLossAdvice('${claimCase.id}')">出险通知书
        </button>
        <button class="btn btn-bule" onclick="exportLegalConveyancing('${claimCase.id}')">企业转让
        </button>
        <button class="btn btn-bule" onclick="exportMediationAgreement('${claimCase.id}')">调解协议书
        </button>
        <button class="btn btn-bule" onclick="exportProcessAgreement('${claimCase.id}')">快速处理协议书
        </button>
    </div>
    <div class="col-sm-12">
        <button class="btn " style="background-color: #F0F0F0;color: black;float: right;margin-top: 60px;margin-right: 50px"
                onclick="closeSupplementaryMaterialsContainer()">取消
        </button>
    </div>
</div>



<div id="outerdiv" style="position: fixed; top: 0; left: 0; background: rgba(0,0,0,0.7); z-index: 2; width: 100%; height: 100%; display: none;">
    <div id="innerdiv" style="position: absolute;">
        <img id="bigimg" style="border: 5px solid #fff;" src=""/>
    </div>
</div>

<div class="row">

    <div class="col-sm-12">

        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title container-head">
                <div class="row">
                    <div class="col-sm-4 pull-left">
                        <div class="col-sm-4"><span
                                    style="border-radius:15px;background-color: orange;padding: 5px;display: inline-block;">${claimCase.productName!'暂无产品'}</span>
                        </div>
                        <div class="col-sm-4">${claimCase.treatName!'-'}&nbsp;&nbsp;${claimCase.plateNumber!'-'}</div>
                        <div class="col-sm-4">${(claimCase.treatDate?string('yyyy-MM-dd HH'))!'-'}时出险</div>
                    </div>
                    <div class="col-sm-2 " style="text-align: center;">
                        <#if isClaimVerify?? && isClaimVerify=="1">
                        <#else >
                            <span style="color: #507FBF">${claimStatusMsg}</span>
                            <@shiro.hasPermission name="URGENT_VERIFY">
                                <#if claimCase.status == "abx10">
                                    <button class="btn btn-warning" onclick="verifyCase('${claimCase.id}')">赔案审核</button>
                                </#if>
                            </@shiro.hasPermission>
                        </#if>
                        <@shiro.hasPermission name="PRETREATMENT_HANG_UP">
                            <#if claimCase.status == "aax30" && userId == claimCase.pretreatmentAuditer>
                                <div>
                                    <button class="btn btn-danger" onclick="hangUp()">预处理挂起</button>
                                </div>
                            </#if>
                        </@shiro.hasPermission>
                    </div>
                    <div class="col-sm-6">
                        <@shiro.hasPermission name="ONLOAD_SELF_BOOK">
                            <button class="btn btn-bule" onclick="onloadClaimApplyBook('${claimCase.id}')">下载理赔申请书
                            </button>
                        </@shiro.hasPermission>
                        <@shiro.hasPermission name="ONLOAD_POLICY">
                            <button class="btn btn-bule" onclick="onloadEPolicy('${claimCase.policyPersonId}')">电子凭证
                            </button>
                        </@shiro.hasPermission>
                        <@shiro.hasPermission name="ONLOAD_POLICY">
                            <button class="btn btn-bule" onclick="onloadInteriorPolicy('${claimCase.policyPersonId}')">电子保单
                            </button>
                        </@shiro.hasPermission>

                        <#--理赔审核跳过-->
                        <#if isClaimVerify?? && isClaimVerify=="1">
                            <button class="btn btn-bule" onclick="skipClaimVerify('${claimCase.id}')">跳过</button>
                        </#if>

                        <#--催办记录暂时不处理，后期添加新功能-->
                        <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>
                            <@shiro.hasPermission name="URGING_LOG">
                                <button class="btn btn-light-bule" onclick="pressDoLog()">催办记录（0）</button>
                            </@shiro.hasPermission>
                            <#--<@shiro.hasPermission name="SUPPLEMENTARY_SMS">
                                <button class="btn btn-bule" onclick="reSendMessage('${claimCase.id}')">补发短信</button>
                            </@shiro.hasPermission>-->
                            <@shiro.hasPermission name="CLOSE_CASE">
                                <button class="btn btn-bule" onclick="closeCase('${claimCase.id}')">关闭案件</button>
                            </@shiro.hasPermission>
                        </#if>
                        <@shiro.hasPermission name="ONLOAD_SELF_BOOK">
                            <button class="btn btn-bule" onclick="exportTemplates('${claimCase.id}')">模版导出
                            </button>
                        </@shiro.hasPermission>
                    </div>
                </div>
            </div>
            <div class="portlet-body container-boby">
                <div class="row">
                    <div class="block-head-label">
                        投保频率 &nbsp;&nbsp;&nbsp;<span style="font-size: 10px">最近一次统计时间：</span><span style="font-size: 10px" id="lastStatisticsData"> </span> <a onclick="freshStatistics('1')">刷新</a>

                        <@shiro.hasPermission name="ELM_APPRAISAL_AMOUNT_EDIT">
                        <#--                            <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>-->
                            <a class="pull-right" onclick="modifyAppraisalAmount('${claimCase.id}')">查看</a>
                        <#--                            </#if>-->
                            <span class="pull-right" id="appraisalAmount">估损金额（旧版）：${claimCase.appraisalAmount}元</span><br>
                        </@shiro.hasPermission>

                        <@shiro.hasPermission name="APPRAISAL_AMOUNT_EDIT">
                        <#--                        <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>-->
                            <a class="pull-right" onclick="modifyObjectAppraisalAmount('${claimCase.id}')">查看</a>
                        <#--                        </#if>-->
                            <span class="pull-right" id="objectAppraisalAmount">估损金额（新版）：${objectAppraisalAmount!''}元</span>
                        </@shiro.hasPermission>
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="总投保次数" style="border-bottom: 3px solid #fff;">总投保次数：</div>
                                    <div class="right-min-7" id="times" style="border-bottom: 3px solid #EFEFEF;"></div>
                                    <@shiro.hasPermission name="HIS_POLICY_LOG">
                                        <a href="#" style="position: absolute;right: 1px;top: 30%" onclick="getHistoryRecord('${claimCase.baseUserId!''}', '${claimCase.insCode!'DD'}')">历史保单记录</a>
                                    </@shiro.hasPermission>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="总投保率" style="border-bottom: 3px solid #fff;">总投保率：</div>
                                    <div class="right-min-7" id="sumTimesRate" style="border-bottom: 3px solid #EFEFEF;"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="最近连续投保天数" style="border-bottom: 3px solid #fff;">最近连续投保天数：</div>
                                    <div class="right-min-7" id="continuousTimes" style="border-bottom: 3px solid #EFEFEF;"></div>
                                </div>

                            </div>
                        </div>

                        <#--&lt;#&ndash;                        下划线&ndash;&gt;
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="最近30天投保率" style="border-bottom: 3px solid #fff;">最近30天投保率：</div>
                                    <div class="right-min-7" id="sumNear30TimesRate" style="border-bottom: 3px solid #EFEFEF;"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="近30天投保天数" style="border-bottom: 3px solid #fff;">近30天投保天数：</div>
                                    <div class="right-mid-7" id="near30DayTimes" style="border-bottom: 3px solid #EFEFEF;"></div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                       <#-- <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>-->

                        <div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="left-min-5" title="最近一次投保时间">最近一次投保时间：</div>
                                        <div class="right-min-7" id="lastTimePolicyPersonDate"></div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="left-mid-5" title="最近投保时间">最近投保时间：</div>
                                        <div class="right-mid-7" id="lastPolicyPersonDate"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <div class="row">
                    <div class="block-head-label">
                        出险人信息
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="代驾ID" >代驾ID：</div>
                                    <div class="right-min-7">${policyPerson.customerNumber!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="保险期限">保险期限：</div>
                                    <div class="right-min-7">${(policyPerson.startDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}
                                        至${(policyPerson.endDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="保险产品">保险产品：</div>
                                    <div class="right-min-7" title="${claimCase.productName!'-'}">${claimCase.productName!'-'}</div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险人">出险人：</div>
                                    <div class="right-min-7">${claimCase.treatName!'-'}</div>
                                    <#--待人员详情地址-->
                                    <@shiro.hasPermission name="LOOK_USER_INFO">
                                        <a style="position: absolute;right: 1px;top: 30%" onclick="viewPersonDetail('${claimCase.policyPersonId}')">查看人员信息</a>
                                    </@shiro.hasPermission>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="身份证">身份证：</div>
                                    <div class="right-min-7">
                                        ${claimCase.treatIdNum!'-'}
                                        <a href="#" style="position: absolute;right: 1px;top: 30%"
                                           onclick="seeTreatIdTypeAttach('${claimCase.id}')">查看图片</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                         <@shiro.hasPermission name="EDIT_CLAIM_CASE_TREAT_MOBILE">setPromptOpen</@shiro.hasPermission>
                                    </#if>" data-formType="0" data-fieldName="treatMobile" title="联系电话">联系电话：
                                    </div>
                                    <div class="right-min-7">
                                        ${claimCase.treatMobile!'-'}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_CAR_BRAND">select</@shiro.hasPermission>
                                    </#if>" fieldName="carBrand" title="车辆信息">车辆信息：
                                    </div>
                                    <div class="right-min-7">${claimCase.carBrand!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                         <@shiro.hasPermission name="EDIT_CLAIM_CASE_PLATE_NUMBER">setPromptOpen</@shiro.hasPermission>
                                    </#if>" data-formType="0" data-fieldName="plateNumber" title="车牌号">车牌号：
                                    </div>
                                    <div class="right-min-7">${claimCase.plateNumber!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="历史案件量">历史案件量：</div>
                                    <div class="right-min-7">
                                        ${historyCaseSize!'0'}个
                                        <a href="#" style="position: absolute;right: 1px;top: 30%" onclick="getHistroyCaseInfo('${claimCase.baseUserId}')">查看详情</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5 " title="子商ID">子商ID：
                                    </div>
                                    <div class="right-min-7">${policyPerson.subsidiaryAgentId!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" style="cursor: pointer; color: #1676ff;" title="保单号" onclick="showMsg(this)">保单号：</div>
                                    <div class="right-mid-7">${policyPerson.policyNo!'-'}</div>
                                   <#-- <a style="position: absolute;right: 1px;display: block;width: 60px;"
                                       onclick="seePlanName('${product.id!''}')">查看方案</a>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        报案信息
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="案件号">案件号：</div>
                                    <div class="right-min-7">${claimCase.claimCaseNo!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="报案时间">报案时间：</div>
                                    <div class="right-min-7">
                                        ${(claimCase.startDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}&nbsp;&nbsp;&nbsp;
                                        <span style="color: red" title="出险后${diffTime!''}报案">出险后${diffTime!''}报案</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="报案人姓名">报案人姓名：</div>
                                    <div class="right-min-7">${claimCase.applyName!'-'}</div>
                                </div>
                            </div>
                        </div>

                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="联系电话">联系电话：</div>
                                        <div class="right-max-7">${claimCase.applyMobile!'-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        出险信息
                    </div>
                    <div class="row block-border">
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险原因">出险原因：</div>
                                    <div class="right-min-7">交通事故</div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5" title="出险时间">出险时间：</div>
                                    <div class="right-mid-7">
                                        ${(claimCase.treatDate?string("yyyy-MM-dd HH:mm:ss"))!'-'}&nbsp;&nbsp;&nbsp;
                                        <span style="color: red">保单投保时间：${(policyPerson.startDate?string("yyyy-MM-dd"))!'-'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <#--                        下划线-->
                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="出险省市">出险省市：
                                    </div>
                                    <div class="right-min-7 showDetailMsg" title="${claimCase.province!"-"}-${claimCase.city!"-"}-${claimCase.district!"-"}">
                                        ${claimCase.province!"?"}-${claimCase.city!"?"}-${claimCase.district!"?"} &nbsp;&nbsp;&nbsp;
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_ADDRESS">setPromptOpen</@shiro.hasPermission>
                                    </#if>" data-formType="2" data-fieldName="address" title="出险地点">出险地点：
                                    </div>
                                    <div class="right-mid-7" title="${claimCase.address!"-"}">
                                        ${claimCase.address!"?"}
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5" title="事故类别">事故类别：</div>
                                    <div class="right-min-7">
                                        <#if claimCase.applyType?contains("AA001") || claimCase.applyType?contains("AB001")>
                                            <span class="span-type">门诊</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA002") || claimCase.applyType?contains("AB002")>
                                            <span class="span-type">住院</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA003") || claimCase.applyType?contains("AB003")>
                                            <span class="span-type">死亡</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AA004") || claimCase.applyType?contains("AB004")>
                                            <span class="span-type">伤残</span>
                                        </#if>
                                        <#if claimCase.applyType?contains("AC")>
                                            <span class="span-type">物损</span>
                                        </#if>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_ACCIDENT_TYPE">select</@shiro.hasPermission>
                                    </#if>" fieldName="accidentType" title="事故类型">事故类型：
                                    </div>
                                    <div class="right-mid-7">${claimCase.accidentType!'-'}</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="left-min-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_ACCIDENT_LIABILITY">select</@shiro.hasPermission>
                                    </#if>" fieldName="accidentLiability" title="事故责任">事故责任：
                                    </div>
                                    <div class="right-min-7">
                                        ${claimCase.accidentLiability!'-'}&nbsp;&nbsp;&nbsp;
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="left-mid-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_ACCIDENT_PROPORTION">select</@shiro.hasPermission>
                                    </#if>" fieldName="accidentProportion" title="事故比例">事故比例：
                                    </div>
                                    <div class="right-mid-7">${claimCase.accidentProportion!'-'}%</div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="block-show">
                                    <div class="line-left-min-5"></div>
                                    <div class="line-right-min-7"></div>
                                </div>
                            </div>
                            <div class="col-sm-8">
                                <div class="block-show">
                                    <div class="line-left-mid-5"></div>
                                    <div class="line-right-mid-7"></div>
                                </div>
                            </div>
                        </div>



                        <#--   <div>
                               <div class="col-sm-4">
                                   <div class="block-show">
                                       <div class="left-min-5" title="事故类别">事故类别：</div>
                                       <div class="right-min-7">
                                           <#if claimCase.applyType?contains("AA001") || claimCase.applyType?contains("AB001")>
                                               <span class="span-type">门诊</span>
                                           </#if>
                                           <#if claimCase.applyType?contains("AA002") || claimCase.applyType?contains("AB002")>
                                               <span class="span-type">住院</span>
                                           </#if>
                                           <#if claimCase.applyType?contains("AA003") || claimCase.applyType?contains("AB003")>
                                               <span class="span-type">死亡</span>
                                           </#if>
                                           <#if claimCase.applyType?contains("AA004") || claimCase.applyType?contains("AB004")>
                                               <span class="span-type">伤残</span>
                                           </#if>
                                           <#if claimCase.applyType?contains("AC")>
                                               <span class="span-type">物损</span>
                                           </#if>
                                       </div>
                                   </div>
                               </div>
                               <div class="col-sm-8">
                                   <div class="block-show">
                                       <div class="left-mid-5" title="出险地点">出险地点：</div>
                                       <div class="right-mid-7" title="${claimCase.province!"-"}-${claimCase.city!"-"}-${claimCase.district!"-"}&nbsp;&nbsp;${claimCase.address!"-"}">
                                           ${claimCase.province!"?"}-${claimCase.city!"?"}-${claimCase.district!"?"} &nbsp;&nbsp;&nbsp;
                                           ${claimCase.address!"?"}
                                       </div>
                                   </div>
                               </div>
                           </div>
                           &lt;#&ndash;                        下划线&ndash;&gt;
                           <div>
                               <div class="col-sm-4">
                                   <div class="block-show">
                                       <div class="line-left-min-5"></div>
                                       <div class="line-right-min-7"></div>
                                   </div>
                               </div>
                               <div class="col-sm-8">
                                   <div class="block-show">
                                       <div class="line-left-mid-5"></div>
                                       <div class="line-right-mid-7"></div>
                                   </div>
                               </div>
                           </div>-->

                        <#--    <div>
                                <div>
                                    <div class="col-sm-4">
                                        <div class="block-show">
                                            <div class="left-min-5" title="医院名称">医院名称：</div>
                                            <div class="right-min-7">
                                                ${claimCase.hospitalName!'-'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="block-show">
                                            <div class="left-mid-5" title="索赔金额（元）">索赔金额（元）：</div>
                                            <div class="right-mid-7">${(claimCase.applyMoney?string("0.00"))!'-'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="col-sm-4">
                                    <div class="block-show">
                                        <div class="line-left-min-5"></div>
                                        <div class="line-right-min-7"></div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="block-show">
                                        <div class="line-left-mid-5"></div>
                                        <div class="line-right-mid-7"></div>
                                    </div>
                                </div>
                            </div>-->

                        <div>
                            <div class="col-sm-12">
                                <div class="block-show">
                                    <div class="left-max-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_KNIGHTLLLEGALITEMS">knightlllegalItems</@shiro.hasPermission>
                                    </#if>" title="事故违规类型" >事故违规类型：
                                    </div>
                                    <div class="right-max-7 showDetailMsg">
                                        <#if knightlllegalItemsList??>
                                            <#list knightlllegalItemsList as item >
                                                <span class="span-type">${accidentViolationTypeMap.get(item)}</span>
                                            </#list>
                                        </#if>
                                    </div>
                                </div>
                            </div>
                             <div class="col-sm-12">
                                 <div class="block-show">
                                     <div class="line-left-mid-5" style="width: 13.8%;"></div>
                                     <div class="line-right-mid-7" style="width: 86.2%;"></div>
                                 </div>
                             </div>
                        </div>
                        <div>
                            <div class="col-sm-12">
                                <div class="block-show">
                                    <div class="left-max-5 blue
                                    <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                        <@shiro.hasPermission name="EDIT_CLAIM_CASE_DESCRIPTION">setPromptOpen</@shiro.hasPermission>
                                    </#if>" style="cursor: pointer; color: #1676ff;" data-formType="2" data-fieldName="description" title="事故经过" onclick="showMsg(this)">事故经过：
                                    </div>
                                    <div class="right-max-7 showDetailMsg" title="${claimCase.description!'-'}">
                                        ${claimCase.description!'-'}
                                    </div>
                                </div>
                            </div>
                           <#-- <div class="col-sm-12">
                                <div class="block-show">
                                    <div class="line-left-mid-5" style="width: 13.8%;"></div>
                                    <div class="line-right-mid-7" style="width: 86.2%;"></div>
                                </div>
                            </div>-->
                        </div>
                        <#--<div>
                            <div>
                                <div class="col-sm-12">
                                    <div class="block-show">
                                        <div class="left-max-5" title="出险标签" >出险标签：</div>
                                        <div class="right-max-7 showDetailMsg">
                                            <#if claimCase.label??>
                                                <#list claimCase.label?split(",") as key>
                                                   <span class="span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                                </#list>
                                            </#if>
                                            &lt;#&ndash;<a onclick="openLabelAction(this,'${claimCase.id}','${claimCase.label!''}')" style="float:right;margin-right:10px;">编辑标签</a>&ndash;&gt;
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                    </div>


                </div>

                <div class="row dutyInfoArea">
                    <div class="block-head-label">
                        损失项目
                        <@shiro.hasPermission name="CLAIM_CASE_OBJECT_ADD">
                            <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") && claimCase.status != "acx21">
                                <a onclick="editObject()">新增</a>
                            </#if>
                        </@shiro.hasPermission>
                        <@shiro.hasPermission name="LOSS_ADJUSTING_ADD">
<!--LOSS_ADJUSTING_ADD-->
                            <a onclick="showLossAdjustingObject()">新增公估</a>
                        </@shiro.hasPermission>
                        <a onclick="copyLinkToClipboard('${copyUrl}')">
                            补材链接
                        </a>
                    </div>
                    <div class="col-sm-12 clear-padding">
                        <table class="table" style="border: 1px solid #C2C2C2;">
                            <thead>
                            <tr>
                                <th width="10%">类型</th>
                                <th width="15%">赔付对象名称</th>
                                <th width="10%">手机号</th>
                                <th width="10%">预估金额</th>
                                <th width="10%">医院名称</th>
                                <th width="10%">状态</th>
                                <th width="10%">处理岗人员</th>
                                <th width="15%">功能</th>
                            </tr>
                            </thead>
                            <tbody id="dutyInfoShow">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row">
                    <div class="block-head-label">
                        影像信息
                        <@shiro.hasPermission name="EDIT_CLAIM_ATTACH">
                            <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") >
                                <a  onclick="editAttach('${claimCase.id}')">编辑影像</a>
                            </#if>
                        </@shiro.hasPermission>
                    </div>
                    <div class="col-sm-12 attachInfo">
                        <#if imgInfoMap?? && (imgInfoMap.keySet()?size>0)>
                            <#list imgInfoMap.keySet() as key>
                                <#if attachMap.get(key)?? && (attachMap.get(key)?size>0)>
                                    <div attach-type="${key}">
                                        <label><#if imgInfoMap.get(key)??>${imgInfoMap.get(key)}<#else >${key!'其它'}</#if></label>
                                        <div class="col-sm-12">
                                            <#list attachMap.get(key) as attach>
                                                <div class="col-sm-2" style="display: flex;flex-wrap: wrap;align-items: flex-start">
                                                    <div style="position: relative;width:230px;height:250px">
                                                        <img src="${ctx}/a/ajax-loader.gif" data-url="${attach.fileObjectId}">
                                                        <div style="position:absolute;height: auto;
                                                        width: 100%;bottom: 0;">
                                                        <#if attach.createTime!>
                                                            <span style="font-size: 12px;position: relative;left: 5px;color:#aba8a8;font-weight: bold">${attach.creator!''}</span>
                                                            <span style="font-size: 12px;position: relative;right: 5px;float: right;color:#aba8a8";>${attach.createTime?string('yyyy-MM-dd')}</span>
                                                            </#if>
                                                        </div>
                                                        <#if attach.replenishNub!>
                                                            <div class="icon-attach">${attach.replenishNub!''}</div></#if>
                                                    </div>
                                                </div>
                                            </#list>
                                        </div>
                                    </div>
                                </#if>
                            </#list>
                        </#if>

                        <#--<#if attachMap?? && (attachMap.keySet()?size>0)>
                            <#list attachMap.keySet() as key>
                                <div attach-type="${key}">
                                    <label><#if imgInfoMap.get(key)??>${imgInfoMap.get(key)}<#else >${key!'其它'}</#if></label>
                                    <div class="col-sm-12">
                                        <#list attachMap.get(key) as attach>
                                            <div class="col-sm-2" style="display: flex;flex-wrap: wrap;align-items: flex-start">
                                                <div style="position: relative;width:230px;height:250px">
                                                    <img src="${ctx}/a/ajax-loader.gif" data-url="${attach.fileObjectId}">
                                                    <#if attach.replenishNub!>
                                                        <div class="icon-attach">${attach.replenishNub!''}</div></#if>
                                                </div>
                                            </div>
                                        </#list>
                                    </div>
                                </div>
                            </#list>
                        </#if>-->
                    </div>
                </div>

                <@shiro.hasPermission name="SEE_CASE_PAY_DUTY">
                <div class="row payDutyInfo">
                    <div class="block-head-label">
                        赔付责任
                        <@shiro.hasPermission name="EDIT_CASE_PAY_DUTY">
                            <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex")>
                                <a onclick="editDuty('${claimCase.id}')">编辑责任</a>
                            </#if>
                        </@shiro.hasPermission>
                    </div>
                    <#if claimCaseSubjectList?exists && (claimCaseSubjectList?size>0)>
                    <#list claimCaseSubjectList as claimCaseSubject>
                    <div class="col-sm-5 claim_subject_area" sujectName="${claimCaseSubject.name}" sujectArea="${claimCaseSubject.id}"
                         onclick="clickDutyDetail('${claimCaseSubject.id}')">
                        <#switch claimCaseSubject.status>
                        <#case 0>
                        <div class="notice notice-no-deal">
                            <#break >
                            <#case 1>
                            <div class="notice notice-deal">
                                <#break >
                                <#case -1>
                                <div class="notice notice-hangup">
                                    <#break >
                                    <#default >
                                    <div class="notice notice-default">
                                        </#switch>

                                        <div>${claimCaseSubject.remark!'-'}</div>
                                    </div>
                                </div>
                                </#list>
                                </#if>
                            </div>
                            </@shiro.hasPermission>

                            <@shiro.hasPermission name="ESTIMATE_INVENTORY_DETAIL_LOOK">
                            <div class="row">
                                <div class="block-head-label">
                                    估损清单
                                </div>
                                <div class="col-sm-12 estimateInventoryInfo">
                                    <#if estimateInventoryMap?? && (estimateInventoryMap.keySet()?size>0)>
                                        <#list estimateInventoryMap.keySet() as key>
                                            <div data-estimateInventoryCode="${key}">
                                                <label>${estimateInventoryMap.get(key)}</label>
                                                <div class="col-sm-12">
                                                    <#if assessmentReportMap?exists && (assessmentReportMap.keySet()?size>0) && assessmentReportMap.get(key)?exists>
                                                        <#list assessmentReportMap.get(key) as assessmentReport >
                                                            <div
                                                                    <#switch assessmentReport.status>
                                                                        <#case -1>
                                                                            class="col-sm-2 notice staging"
                                                                            <#break >
                                                                        <#case 0>
                                                                            class="col-sm-2 notice complete"
                                                                            <#break >
                                                                        <#case 1>
                                                                            class="col-sm-2 notice reject"
                                                                            <#break >
                                                                        <#case 2>
                                                                            class="col-sm-2 notice pass"
                                                                            <#break >
                                                                        <#default >
                                                                            class="col-sm-2 notice default"
                                                                    </#switch>

                                                                    isNotice="1" code="${key}" assessmentReprotId="${assessmentReport.id}">
                                                                ${assessmentReport.code!''}
                                                                <#if !claimCase.status?contains("-1") && claimCase.status != "aex20" >
                                                                <@shiro.hasPermission name="ESTIMATE_INVENTORY_DETAIL_DEL">
                                                                    <span class="glyphicon glyphicon-remove deleteF" assessmentReprotId="${assessmentReport.id}"></span>
                                                                </@shiro.hasPermission>
                                                                </#if>
                                                            </div>
                                                        </#list>
                                                    </#if>
                                                </div>
                                            </div>
                                        </#list>
                                    </#if>
                                </div>
                            </div>
                            </@shiro.hasPermission>

                            <#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") && claimCase.status!="abx20">
                                <div class="row logInfo">
                                    <div class="block-head-label">
                                        沟通记录/新增备注
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <div class="col-sm-12" id="labelShowArea" style="margin-bottom: 10px;display: flex;align-items: center;flex-wrap: wrap;">
                                                <div id="editLabelContainer" style="width: 100%;height: 100%">
                                                <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                                                    <#list labelShowMap.keySet() as key>
                                                        <div class="col-sm-1"  style="padding-left: 0px;margin-bottom: 10px;display:flex;align-items: center;<#if labelShowMap.get(key).level!=1>display:none</#if>">
                                                            <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}" id="${key}"
                                                                   type="checkbox" <#if claimCase.label??&&claimCase.label?contains(key)>checked</#if>><label for="${key}" class="${key} span-type"  style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></label>
                                                        </div>
                                                    </#list>
                                                </#if>
                                                </div>
                                            </div>
                                            <textarea class="form-control" id="logTextArea" rows="5" placeholder="请输入"></textarea>
                                            <div class="col-sm-12">
                                                <@shiro.hasPermission name="ADD_CASE_LOG">
                                                    <button class="btn btn-warning" onclick="addCaseLog('${claimCase.id}')" id="addCaseLog">新增备注/提交</button>
                                                </@shiro.hasPermission>
                                                <#if isClaimVerify?? && isClaimVerify=="1">
                                                    <button class="btn btn-orange" onclick="subimitAll('${claimCase.id}')">
                                                        审核通过
                                                    </button>
                                                </#if>

                                                <#--<#if !claimCase.status?contains("-1") && !claimCase.status?contains("aex") && claimCase.policyPersonId != "">
                                                    <@shiro.hasPermission name="RETRY_CALL_PHONE">

                                                        <#if claimCase.label!=null && claimCase.label?contains("Aax002")>
                                                            <button class="btn btn-warning" onclick="changeCustomerTask(this)">去电完成</button><#else >
                                                            <button class="btn btn-bule" onclick="changeCustomerTask(this)">再次去电</button>
                                                        </#if>

                                                    </@shiro.hasPermission>
                                                    <@shiro.hasPermission name="MARK_CASE_DIFFCULT">
                                                        <#if claimCase.isDifficultCase!=1>
                                                            <button class="btn btn-bule" onclick="markDiffcultCase(this)">标记疑难</button><#else >
                                                            <button class="btn btn-warning" onclick="markDiffcultCase(this)">取消疑难</button>
                                                        </#if>

                                                    </@shiro.hasPermission>
                                                </#if>-->

                                                <#if isClaimVerify?? && isClaimVerify=="1">
                                                    <button class="btn btn-bule" onclick="supplementaryMaterials()">
                                                        发起补材
                                                    </button>
                                                    <button class="btn btn-grey">
                                                        启动预赔
                                                    </button>
                                                </#if>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </#if>
                            <@shiro.hasPermission name="LOOK_CLAIM_CASE_LOG">
                            <div class="row logListInfo">
                                <div class="block-head-label">
                                    日志信息
                                    <input type="checkbox" id="checkSystem"> <label for="checkSystem" style="font-size: 8px;font-weight: bold;">是否展示系统日志</label>
                                </div>
                                <div class="col-sm-12">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <td width="10%"></td>
                                            <td width="20%">岗位</td>
                                            <td width="20%">类型</td>
                                            <td width="20%">人员</td>
                                            <td width="30%">时间</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <#if claimCaseLogShows??>
                                            <#list claimCaseLogShows as log>
                                                <tr class="rowInfo" <#if log.creator == "系统"> style="display: none;" </#if>>
                                                    <td width="10%" align="center">
                                                        <div class="icon-plus"></div>
                                                    </td>
                                                    <td width="20%">${log.position}</td>
                                                    <td width="20%">
                                                        ${claimCaseLogTypeEnumMap.get(log.type).msg}
                                                    </td>
                                                    <td width="20%">
                                                        <#if log.creator?contains("-") && log.creator!="-1" >
                                                            ${log.creator?substring(0,log.creator?index_of("-"))}
                                                        <#else>
                                                            ${log.creator}
                                                        </#if>
                                                    </td>
                                                    <td width="30%">${log.createTime?string["yyyy-MM-dd HH:mm:ss"]}</td>
                                                </tr>
                                                <tr class="detailsInfo">
                                                    <td width="10%" align="center"></td>
                                                    <td width="90%" colspan="4" style="overflow-x: visible;">${log.description}</td>
                                                </tr>
                                            </#list>
                                        </#if>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            </@shiro.hasPermission>
                        </div>
                    </div>
                </div>
            </div>
</body>
</html>