<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>创建报案</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />
    <link href="${ctx}/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/js/genProCity.js"></script>
    <script src="${ctx}/js/genVBrand.js"></script>
    <script src="${ctx}/js/idCardCheck.js" type="text/javascript"></script>

    <script src="${ctx}/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const proCityPickerData = new GenProCity().getValue();
        const proBrandPickerData = new GenVBrand().getValue();
        var applyTypeList = [];
        var subjectList = [];
        const loader =  new Loaders({style:"rectangle"});

        var scrollTop;  // 定义滚动高度


        var pageUuid;

        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        $(function () {

            //时间选择器
            laydate.render({
                elem: '#treatHour',
                type: 'time',
                done: function(value, date){
                    console.log(value);
                }
            });

            $("#carBrand").select2({
                placeholder: "请选择",
                width: null
            });

            $("#policyPersonId").select2({
                placeholder: "请选择",
                width: null
            });

            const idCardCheck = new IdCardCheck();
            var searchForm = $("#searchForm");
            searchForm.validate({
                ignore: "hidden",
                errorElement: 'span', //default input error message container
                errorClass: 'help-block help-block-error hidden_span font-span', // default input error message class
                focusInvalid: false, // do not focus the last invalid input
                rules: {
                    treatName: {
                        required: true,
                    },
                    treatIdNum: {
                        required: true
                    },
                    treatDate: {
                        required: true
                    }
                },
                invalidHandler: function (event, validator) { //display error alert on form submit
                    App.scrollTo(searchForm, 0);
                },
                highlight: function (element) { // hightlight error inputs
                    layer.msg("各项值不能为空", {
                        offset: '30px',
                        icon: 2
                    });
                    $(element).closest('.ggLayer').removeClass("has-success").addClass('has-error').addClass('font-span'); // set error class to the control group
                },
                unhighlight: function (element) { // revert the change done by hightlight
                },
                errorPlacement: function (error, element) {
                    error.appendTo(element.parent().next());
                },
                success: function (label, element) {
                    var icon = $(element).parent('.input-icon').children('i');
                    $(element).closest('.ggLayer').removeClass('has-error').addClass('has-success'); // set success class to the control group
                    icon.removeClass("fa-warning").addClass("fa-check");
                },
                submitHandler: function (form) {
                    var treatIdNum = $("#treatIdNum").val();
                    if (idCardCheck.checkIdCard(treatIdNum)) {
                        layer.msg("请填写合法的出险人身份证号", {
                            offset: '30px',
                            icon: 2
                        });
                        return;
                    }
                    let treatDate = new Date($("#treatDate").val());
                    if(new Date().getTime() < treatDate.getTime()){
                        layer.msg("出险时间不能大于当前时间", {
                            offset: '30px',
                            icon: 2
                        });
                        return;
                    }
                    form.submit();
                }
            });

            //初始化 省
            proCityPickerData.forEach(function (item, index) {
                var option = document.createElement("option");
                option.innerText = item['value'];
                option.value = item['id'];
                option.setAttribute("data-idx", index);
                $("#province").append(option);
            });

            //初始化 品牌
            proBrandPickerData.forEach(function (item, index) {
                var childs = item['childs'];
                for (var i in childs) {
                    var value= childs[i];
                    var option = document.createElement("option");
                    option.innerText = value['value'];
                    option.value = value['id'];
                    $("#carBrand").append(option);
                }
            });

            $("#accidentLiability").select2({
                placeholder: "请选择",
                width: null
            });



            // 初始化 事故类型
            $("#accidentType").select2({
                placeholder: "请选择",
                width: null
            });

            //出险类型切换
            $(".apply-type").on("click", "button", function(e){
                var bgCheck = $(this).attr("data-check");
                if (bgCheck == "0") {
                    $(this).attr("data-check","1");
                    applyTypeList.push($(this).attr("value"));
                    $(this).addClass("blue");
                    $(this).closest("dl").find("img").attr("src","${ctx}/static/img/checkBox_1.png");
                    $.ajax({
                        url: "${ctx}/claimCaseController/applyTypeModifyShowAppraisalAmount?applyType=" + $(this).attr("value"),
                        type: 'POST',
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                result.data.forEach(function (item, index) {
                                    let existBool = true;
                                    for (const i in subjectList) {
                                        if (subjectList[i].parentCode == item.parentCode) {
                                            existBool = false;
                                        }
                                    }
                                    if (existBool) {
                                        subjectList.push(item);
                                    }
                                });
                            } else {
                                scrollTop = calculationScrollTop();
                                layer.msg(result.msg, {
                                    icon: 1,
                                    time: 1500, //1秒关闭（如果不配置，默认是3秒）
                                    offset: scrollTop
                                }, function (index) {
                                    layer.close(index);
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }else {
                    let thisVal = $(this).attr("value");
                    $(this).attr("data-check","0");
                    $(this).removeClass("blue");
                    for (var index in applyTypeList) {
                        var type = applyTypeList[index];
                        if (type == $(this).attr("value")) {
                            applyTypeList.splice(index, 1);
                        }
                    }
                    if ($(this).parent().find("button[data-check=1]").length == 0) {
                        $(this).closest("dl").find("img").attr("src","${ctx}/static/img/checkBox_0.png");
                        for(var i = 0; i < subjectList.length; i++) {
                            var code = thisVal.substring(0,2);
                            if (subjectList[i].parentCode.startsWith(code)) {
                                subjectList.splice(i,1);
                                i--;
                            }
                        }
                    } else {
                        var addJson = [];
                        for (var i = 0; i < subjectList.length; i++) {
                            if (subjectList[i].parentCode.includes(thisVal)) {
                                var dataJson = subjectList[i];
                                var delCode = dataJson.parentCode;
                                subjectList.splice(i,1);
                                if (delCode.includes(",")) {
                                    for (let j in subjectList) {
                                        let addCode = subjectList[j].parentCode;
                                        if (delCode != addCode && (delCode.split(",")[0] == addCode || delCode.split(",")[1] == addCode) && addCode != thisVal) {
                                            addJson.push(dataJson);
                                        }
                                    }
                                }
                                i--;
                            }
                        }
                        for (const i in addJson) {
                            subjectList.push(addJson[i]);
                        }
                    }
                }
            });

            $("#knightlllegalItems-content").on("click", "button", function() {
                $(this).toggleClass("blue");
            });


            pageUuid = guid();

            $("span[name='treateLabel']").on("click",function () {
                var classStr=$(this).attr("class");
                if(classStr=="span-type-unClick"){
                    $(this).attr("class","span-type-click");
                }else {
                    $(this).attr("class","span-type-unClick");
                }
            });

            // 初始化赔付信息
            freshDutyInfo();

            //保持长连接
            keepAlive();
        })

        // 校验
        function checkVerifyInput() {
            var applyName = $("#applyName").val().trim();
            if (applyName == "") {
                $("#applyName").focus();
                return "报案人姓名不能为空";
            }
            const mobileCheck = /^1[3456789][0-9]\d{8}$/;

            var applyMobile = $("#applyMobile").val().trim();
            if (applyMobile == "") {
                $("#applyMobile").focus();
                return "报案人手机号不能为空";
            }
            if (!mobileCheck.test(applyMobile)) {
                $("#applyMobile").focus();
                return "报案人手机号不合法";
            }
            var treatMobile = $("#treatMobile").val().trim();
            if (treatMobile == "") {
                $("#treatMobile").focus();
                return "出险人手机号不能为空";
            }
            if (!mobileCheck.test(treatMobile)) {
                $("#treatMobile").focus();
                return "出险人手机号不合法";
            }
            var treatHour = $("#treatHour").val().trim();
            if (treatHour == "") {
                $("#treatHour").focus();
                return "出险时间不能为空";
            }
            let treatDate = new Date($("#treatDate-text").text().trim() + " " + treatHour);
            if(new Date().getTime() < treatDate.getTime()){
                $("#treatHour").focus();
                return "出险时间不能大于当前时间";
            }
            <#if policyPersonList??>
                var startDate = new Date($("#policyPersonId").find("option:selected").attr("data-startDate")).getTime();
                var endDate = new Date($("#policyPersonId").find("option:selected").attr("data-endDate")).getTime();
                if (startDate > treatDate.getTime() || endDate < treatDate.getTime()) {
                    return "当前出险时间不在保单范围内";
                }
            </#if>
           /* if (applyTypeList.length == 0) {
                return "出险类型不能为空";
            }*/
            var carBrand = $("#carBrand").val().trim();
            if (carBrand == "") {
                $("#carBrand").focus();
                return "肇事车辆信息不能为空";
            }
            var plateNumber = $("#plateNumber").val().trim();
            if (plateNumber == "") {
                $("#plateNumber").focus();
                return "肇事车牌不能为空";
            }
            var province = $("#province").val().trim();
            if (province == "") {
                $("#province").focus();
                return "事故发生省不能为空";
            }
            var city = $("#city").val().trim();
            if (city == "") {
                $("#city").focus();
                return "事故发生市不能为空";
            }
            var district = $("#district").val().trim();
            if (district == "") {
                $("#district").focus();
                return "事故发生区不能为空";
            }
            var address = $("#address").val().trim();
            if (address == "") {
                $("#address").focus();
                return "事故发生详细地址不能为空";
            }
            var description = $("#description").val().trim();
            if (description == "") {
                $("#description").focus();
                return "事故经过描述不能为空";
            }

            var accidentType = $("#accidentType").val().trim();
            if (accidentType == "") {
                $("#accidentType").focus();
                return "事故类型不能为空";
            }

            var accidentLiability = $("#accidentLiability").val().trim();
            if (accidentLiability == "") {
                $("#accidentLiability").focus();
                return "事故责任不能为空";
            }



            var accidentProportion = $("#accidentProportion").val().trim();
            if (accidentProportion == "") {
                $("#accidentProportion").focus();
                return "事故比例不能为空";
            }else {
                var reg = /^\d+$/;
                let result = false;
                if(reg.test(accidentProportion) && (accidentProportion>=0 && accidentProportion <=100)){
                    result = true;
                }
                if(!result){
                    $("#accidentProportion").focus();
                    return "事故比列格式错误，只能为0-100的整数！！";
                }
            }
            var logTextArea = $("#logTextArea").val().trim();
            if (logTextArea == "") {
                $("#logTextArea").focus();
                return "沟通备注不能为空";
            }

            var objectLength = $(".tr-object").length;
            if (objectLength == 0) {
                return "必须存在一个损失项目";
            }
        }

        function submitClaimCase() {
            freshDutyInfo();
            var errorVerify = checkVerifyInput();
            scrollTop = calculationScrollTop();
            if (errorVerify) {
                layer.msg(errorVerify, {
                    offset: scrollTop,
                    icon: 2,
                });
                return;
            }

            var claimCase = {
                applyName: $("#applyName").val().trim(),
                applyMobile: $("#applyMobile").val().trim(),
                treatName: $("#treatName-text").text().trim(),
                treatIdNum: $("#treatIdNum-text").text().trim(),
                treatDate: $("#treatDate-text").text().trim()+" "+$("#treatHour").val().trim(),
                treatMobile: $("#treatMobile").val().trim(),
                carBrand: $("#carBrand").val().trim(),
                plateNumber: $("#plateNumber").val().trim(),
                province: $("#province").val().trim(),
                city: $("#city").val().trim(),
                district: $("#district").val().trim(),
                address: $("#address").val().trim().replaceAll("\n", ""),
                description: $("#description").val().trim().replaceAll("\n", ""),
                accidentType: $("#accidentType").val().trim(),
                accidentLiability: $("#accidentLiability").val().trim(),
                accidentProportion: $("#accidentProportion").val().trim(),
                remark: $("#logTextArea").val().trim().replaceAll("\n", ""),
            };
            <#if policyPersonList??>
                claimCase.policyPersonId = $("#policyPersonId").val().trim();
            </#if>
            let label = "";
            $.each($("span[class='span-type-click']"),function () {
                let code = $(this).attr("code");
                label +=","+code;
            });
            if(label!=""){
                label = label.substring(1);
                claimCase["label"] = label;
            }
            let dataObject = localStorage.getItem(pageUuid);
            console.log(dataObject,"赔付项数据");
            if(dataObject){
                claimCase["claimCaseObjectVoList"] = JSON.parse(dataObject);
            }

            // 组装exInfo
            let knightlllegalItems = "";
            $("#knightlllegalItems-content").find("button[class='blue']").each(function() {
                knightlllegalItems += "," + $(this).attr("data-value");
            });
            if (knightlllegalItems) {
                knightlllegalItems = knightlllegalItems.substr(1);
                let exInfo = {"knightlllegalItems" : knightlllegalItems};
                claimCase["exInfo"] = JSON.stringify(exInfo);
            }

            $.ajax({
                url: "${ctx}/claimCaseController/submitClaimCase",
                type: 'POST',
                data: JSON.stringify(claimCase),
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        loader.show();
                        setTimeout(function(){
                            layer.msg(result.msg, {icon: 1, time: 1000, offset: scrollTop}, function (index) {
                                layer.close(index);
                                window.location.href = "${ctx}/claimCaseController/claimCaseList";
                            });
                        }, 1200);
                    } else if (result.ret == "110") {
                        layer.confirm(result.msg, {icon: 0, title:'执行',offset: scrollTop}, function(index){
                            claimCase["reconfirm"] = 1;
                            console.log(claimCase);
                            $.ajax({
                                url: "${ctx}/claimCaseController/submitClaimCase",
                                type: 'POST',
                                data: JSON.stringify(claimCase),
                                async: true,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("(" + data + ")");
                                    if (result.ret == "0") {
                                        loader.show();
                                        setTimeout(function(){
                                            layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function (index) {
                                                layer.close(index);
                                                window.location.href = "${ctx}/claimCaseController/claimCaseList";
                                            });
                                        }, 1200);
                                    } else {
                                        layer.msg(result.msg, {icon: 2, time: 2000, offset: scrollTop}, function (index) {
                                            layer.close(index);
                                        });
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function () {
                                        layer.close(index);
                                    });
                                }
                            });
                        })
                    } else {
                        layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function (index) {
                            layer.close(index);
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    layer.msg(result.msg, {icon: 1, time: 2000, offset: scrollTop}, function () {
                        layer.close(index);
                    });
                }
            });
        }

        function provinceChange() {
            var provinceIdx = $("#province option:selected").attr("data-idx");
            $("#city").html("<option value=''>-市-</option>");
            $("#district").html("<option value=''>-区-</option>");
            if (provinceIdx) {
                var cityList = proCityPickerData[provinceIdx].childs;
                cityList.forEach(function (item, index) {
                    var option = document.createElement("option");
                    option.innerText = item['value'];
                    option.value = item['id'];
                    option.setAttribute("data-idx", index);
                    $("#city").append(option);
                });
            }

        }
        
        function cityChange() {
            var provinceIdx = $("#province option:selected").attr("data-idx");
            var cityIdx = $("#city option:selected").attr("data-idx");
            $("#district").html("<option value=''>-区-</option>");
            if (cityIdx) {
                var districtList = proCityPickerData[provinceIdx].childs[cityIdx].childs;
                districtList.forEach(function (item, index) {
                    var option = document.createElement("option");
                    option.innerText = item['value'];
                    option.value = item['id'];
                    $("#district").append(option);
                });
            }
        }

        //案件关闭
        function closeCase(caseId) {
            scrollTop = calculationScrollTop();
            var content = '<div class="row" style="padding: 0px 30px" ><div class="col-sm-3">&nbsp;</div><textarea rows="5" class="col-sm-6 form-control" name="closeCaseMsg" id="closeCaseMsg" autocomplete="off" placeholder="请输入关闭案件原因"></textarea></div>' ;
            layer.open({
                type: 1,
                content: content,
                title: '关闭案件',
                area:  ['500px', '300px'],
                btn: ['确认','取消'],
                offset: scrollTop,
                yes: function(index,obj){
                    console.log(typeof $("#closeCaseMsg").val());
                    var closeCaseMsg = $("#closeCaseMsg").val();
                    if(typeof closeCaseMsg != 'string' || closeCaseMsg.trim()==''){
                        layer.msg("关闭案件原因不能为空", {icon: 2, time: 3000,offset: scrollTop});
                    }else {
                        var formData = new FormData();
                        formData.append("claimCaseId", caseId);
                        formData.append("type", 3);
                        formData.append("description", closeCaseMsg);
                        $.ajax({
                            url: "${ctx}/claimCaseController/closeCase",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    loader.show();
                                    setTimeout(function(){
                                        layer.msg('关闭案件成功', {
                                            icon: 1,
                                            time: 1000,
                                            offset: scrollTop
                                        }, function () {
                                            window.location.reload();
                                        });
                                    },1200);
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 1500,
                                        offset: scrollTop
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                    }
                }
            });
        }

        //查看详情
        function caseDetail(claimCaseId) {
            window.open("${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId);
        }

        //返回
        function back() {
            window.location.href = "${ctx}/claimCaseController/claimCaseList";
        }

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        function keepAlive() {
            setInterval(function () {
                $.ajax({
                    url: "${ctx}/claimCaseController/keepAlive",
                    type: 'POST',
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        console.log(data);
                    },
                    error: function (data) {
                        console.log(data);
                    }
                });}
                ,60000*5)
        }

        let objectData ;

        //刷新请求后台，责任数据
        function freshDutyInfo() {
            $("#dutyInfoShow").html("");
            let dataObject = localStorage.getItem(pageUuid);
            dataObject = JSON.parse(dataObject);
            console.log(dataObject);

            if(dataObject){
                objectData = new Map();
                for(let index in dataObject){
                    let obj = dataObject[index];
                    console.log(obj);


                    let typeName="-";
                    if(obj.typeName){
                        typeName = obj.typeName;
                    }

                    let name="-";
                    if(obj.name){
                        name = obj.name;
                    }
                    let mobile="";
                    if(obj.mobile){
                        mobile = obj.mobile;
                    }
                    let estimatedOverallLoss="-";
                    if(obj.estimatedOverallLoss){
                        estimatedOverallLoss = obj.estimatedOverallLoss;
                    }
                    let hospitalName = "";
                    if (obj.hospitalName != undefined) {
                        hospitalName = obj.hospitalName;
                    }
                    let init = $('<tr class="tr-object">\n' +
                        '                                <td width="15%">'+typeName+'</td>\n' +
                        '                                <td width="15%">'+name+'</td>\n' +
                        '                                <td width="15%">'+mobile+'</td>\n' +
                        '                                <td width="15%">'+estimatedOverallLoss+'</td>\n' +
                        '                                <td width="15%" style="border-right: 1px solid #e7ecf1">'+hospitalName+'</td>\n' +
                        '                            </tr>');
                    let trInit = $('<td width="15%" >' +
                        '</td>');
                    objectData.set(obj.objectId,JSON.stringify(obj));
                    let editInit = $('<input value="编辑" type="button" name="editInit" class="audit-button btn btn-primary" onclick="editDutyInfo(\''+obj.objectId+'\')" />');

                    let delInit = $('<input value="删除" type="button"  name="delInit" class="audit-button btn btn-danger" onclick="delDutyInfo(this)"  dutyInfoId="'+obj.objectId+'") />');

                    trInit.append(editInit);
                    trInit.append(delInit);
                    init.append(trInit);
                    $("#dutyInfoShow").prepend(init);
                }
            }
            $("#dutyInfoShow").append('<tr>\n' +
                '                                      <td colspan="5" style="border-right: 1px solid #e7ecf1"></td>\n' +
                '                                      <td >\n' +
                '                                          <button type="button" class="audit-button btn btn-primary"\n' +
                '                                                  onclick="addDutyInfo()">新建</button>\n' +
                '                                      </td>\n' +
                '                                  </tr>');

            iframeH();

        }

        //增加赔付信息
        function addDutyInfo() {
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '损失项目',
                area:  ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/claimCaseObject?pageId=" + pageUuid,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //编辑赔付信息
        function editDutyInfo(objectId) {

            scrollTop = calculationScrollTop();
            //编辑的时候将数据放到本地中，open的页面获取数据后移除
            localStorage.setItem(objectId,objectData.get(objectId));

            layer.open({
                type: 2,
                title: '损失项目',
                area:  ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/claimCaseObjectController/claimCaseObject?pageId=" + pageUuid+"&objectId="+objectId,
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        //删除赔付信息
        function delDutyInfo(obj) {
            let objectId = $(obj).attr("dutyInfoId");
            let dataObject = localStorage.getItem(pageUuid);
            dataObject = JSON.parse(dataObject);
            for(let index in dataObject){
                if(dataObject[index].objectId==objectId){
                    console.log("删除成功");
                    dataObject.splice(index,1);
                    break;
                }
            }
            localStorage.setItem(pageUuid,JSON.stringify(dataObject));
            freshDutyInfo();
        }

        //生成uuid
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 50%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .table th {
            text-align: center !important;
        }
        .case-title {
            color: #3662EC;
            font-size: 15px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .form-control{
            padding: 3px 12px !important;
            height: 30px;
        }
        .case-cent .text-right {
            padding-right: 0px !important;
        }
        dd {
            display: inline-block;
            border-color: #BFBFBF;
            background: #F4F4F4;
        }
        .blue {
            background: #0597FF;
            border-color: #ecebeb;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }

        .td-description-overflow {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            border-right: 1px solid #ebebeb;
            text-align: left;
        }
        .logTextArea{
            width: 100%;
            height: 135px;
        }

        .content-title {
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            margin-bottom: 20px;
            padding-left: 0;
            margin-left: 0;
        }

        textarea {
            background: #EFEFEF;
        }
        .clear-margin {
            margin: 0px;
        }
        .clear-padding {
            padding: 0px;
        }
        .span-type-unClick {
            background: #F4F4F4;
            color: black;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
            cursor: pointer;
        }
        .span-type-click {
            background: #0597FF;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
            cursor: pointer;
        }
        .block-show {
            display: flex;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>业务功能</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">报案管理</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">创建报案</span></li>
                </ul>
            </div>
            <div class="portlet-body">
              <div class="row margin-bottom-20">
                  <form id="searchForm" action="/claimCaseController/claimCaseGenerate?TYPE=generate" method="post"  class="col-sm-12">
                      <div class="col-sm-11 margin-bottom-20">
                          <div class="col-sm-4">
                              <div class="form-group">
                                  <label class="control-label col-sm-4" style="padding-right: 0">出险人姓名</label>
                                  <div class="col-sm-8" style="padding-left: 0;">
                                      <input type="text" class="form-control" name="treatName" id="treatName" value="${claimCaseVo.treatName}">
                                  </div>
                              </div>
                          </div>
                          <div class="col-sm-4">
                              <div class="form-group">
                                  <label class="control-label col-sm-4" style="padding-right: 0">出险人证件号</label>
                                  <div class="col-sm-8" style="padding-left: 0;">
                                      <input type="text" class="form-control" name="treatIdNum" id="treatIdNum" value="${claimCaseVo.treatIdNum}">
                                  </div>
                              </div>
                          </div>
                          <div class="col-sm-4">
                              <div class="form-group">
                                  <label class="control-label col-sm-4" style="padding-right: 0">出险时间</label>
                                  <div class="col-sm-8" style="padding-left: 0;">
                                      <input type="text" class="form-control form-control-inline date-picker" autocomplete="off" data-date-format="yyyy-mm-dd" name="treatDate" id="treatDate" value="${claimCaseVo.treatDate}">
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="col-sm-12">
                          <div class="btn-group col-sm-offset-10">
                              <button class="btn btn-warning" type="submit" >创建报案</button>
                          </div>
                      </div>
                  </form>
              </div>
              <div class="row margin-bottom-20" style="padding: 0px 20px">
                  <div class="text-center col-sm-12" style="padding: 18px 0px;font-weight: bold;border: 1px solid #ebebeb;">
                      <span class="font-red" style="margin-right: 20px">此出险人有相关类似案件，请核实</span>
                      <span>无相关类似案件，请新增报案信息</span>
                  </div>
                  <div style="border: 1px solid #e7ecf1;">
                  <table class="table table-striped table-hover table-striped table-header-fixed text-center" style="margin-bottom: 0px;">
                      <thead>
                      <tr>
                          <th width="8%">报案人姓名</th>
                          <th width="10%">报案人手机号</th>
                          <th width="10%">出险人姓名</th>
                          <th width="10%">出险人身份证</th>
                          <th width="10%">出险类型</th>
                          <th width="9%">报案时间</th>
                          <th width="9%">出险时间</th>
                          <th width="9%">状态</th>
                          <th width="10%" style="text-align: left !important;border-right: 1px solid #ebebeb">事故信息描述</th>
                          <th width="15%">功能</th>
                      </tr>
                      </thead>
                      <tbody>
                      <#if claimCaseList?exists && (claimCaseList?size gt 0)>
                      <#list claimCaseList as case>
                          <tr>
                              <td title="${case.applyName}">${case.applyName!"--"}</td>
                              <td title="${case.applyMobile}">${case.applyMobile!"--"}</td>
                              <td title="${case.treatName}">${case.treatName!"--"}</td>
                              <td title="${case.treatIdNum}">${case.treatIdNum!"--"}</td>
                              <td title="${case.applyType}">
                                  <#list applyTypeList as type>
                                      <#if case.applyType?contains("${type.code}")><span class="span-type">${type.parentTypeName+"-"+type.childTypeName}</span></#if>
                                  </#list>
                              </td>
                              <td>${case.startDate?string["yyyyMMdd"]}</td>
                              <td>${case.treatDate?string["yyyyMMdd"]}</td>
                              <td>
                                  <#list claimCaseStatusEumList as type>
                                      <#if case.status == type.code>${type.msg}</#if>
                                  </#list>
                              </td>
                              <td class="td-description-overflow" title="${case.description}">${case.description!"--"}</td>
                              <#--功能-->
                              <td>
                                  <@shiro.hasPermission name="REPORT_CASE_LIST_CLOSE_CASE">
                                      <#if !case.status?contains("-1") && !case.status?contains("aex")>
                                          <button class="btn blue" onclick="closeCase('${case.id}')">关闭报案</button>
                                      </#if>
                                  </@shiro.hasPermission>
                                  <button class="btn blue" onclick="caseDetail('${case.id}')">查看详情</button>
                              </td>
                          </tr>
                      </#list>
                      <#else>
                          <tr>
                              <td colspan="12" class="text-danger text-center"> 暂无数据</td>
                          </tr>
                      </#if>
                      </tbody>
                  </table>
                  </div>
              </div>
              <#if type != "new">
              <div class="row" id="claim-case-input">
                  <form class="row" id="caseForm" method="post" >
                  <div class="col-sm-6">
                      <div class="row" style="margin: 30px 0px">
                          <div class="case-title col-sm-12 col-sm-offset-2" >报案人信息</div>
                          <div class="col-sm-12">
                              <div class="case-cent col-sm-offset-2">
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          报案人姓名：
                                      </div>
                                      <div class="col-sm-6">
                                          <input type="text" class="form-control" name="applyName" id="applyName">
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          报案人手机号：
                                      </div>
                                      <div class="col-sm-6">
                                          <input type="text" class="form-control" name="applyMobile" id="applyMobile">
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="row" style="margin: 30px 0px">
                          <div class="case-title col-sm-12 col-sm-offset-2" >出险人信息</div>
                          <div class="col-sm-12">
                              <div class="case-cent col-sm-offset-2">
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          出险人姓名：
                                      </div>
                                      <div class="col-sm-6" id="treatName-text">
                                          ${claimCaseVo.treatName}
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          出险人身份证：
                                      </div>
                                      <div class="col-sm-6" id="treatIdNum-text">
                                          ${claimCaseVo.treatIdNum}
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          手机号：
                                      </div>
                                      <div class="col-sm-6">
                                          <input type="text" class="form-control" name="treatMobile" id="treatMobile">
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          出险日期：
                                      </div>
                                      <div class="col-sm-6" id="treatDate-text">
                                          ${claimCaseVo.treatDate}
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          出险时间：
                                      </div>
                                      <div class="col-sm-6">
                                          <input type="text" class="form-control" name="treatHour" id="treatHour" placeholder="HH:mm:ss">
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <#--<div class="row" style="margin: 30px 0px">
                          <input type="hidden" name="applyType" id="applyType">
                          <div class="case-title col-sm-12 col-sm-offset-2" >出险类型</div>
                          <div class="col-sm-12">
                              <div class="case-cent col-sm-offset-4">
                                  <#if applyTypeMap?exists && (applyTypeMap?size gt 0)>
                                  <#list applyTypeMap.keySet() as key>
                                      <div class="margin-bottom-15">
                                          <dl>
                                              <dt class="margin-bottom-10">
                                                  <img src="${ctx}/static/img/checkBox_0.png"> ${key}
                                              </dt>
                                              <div style="padding-left: 30px" class="apply-type">
                                                  <#list applyTypeMap.get(key) as value>
                                                      <button type="button" data-check="0" style="margin-right: 10px" value="${value.code}">${value.childTypeName}</button>
                                                  </#list>
                                              </div>
                                          </dl>
                                      </div>
                                  </#list>
                                  </#if >
                              </div>
                          </div>
                      </div>-->
                      <#--<div class="row" id="knightlllegalItems-content" style="margin-bottom: 10px;">
                          <div class="case-title col-sm-12 col-sm-offset-2" >事故违规类型</div>
                          <div class="col-sm-12">
                              <div class="case-cent col-sm-offset-4">
                                  <#if accidentViolationTypeMap?? && (accidentViolationTypeMap.keySet()?size > 0)>
                                      <ul class="list-inline">
                                          <#list accidentViolationTypeMap.keySet() as key>
                                              <li class="col-sm-4" style="margin: 5px 0px;">
                                                  <button type="button" data-value="${key}">${accidentViolationTypeMap.get(key)}</button>
                                              </li>
                                          </#list>
                                      </ul>
                                  </#if >
                              </div>
                          </div>
                      </div>-->
                      <div class="row" style="margin: 30px 0px">
                          <div class="case-title col-sm-12 col-sm-offset-2" >保全信息</div>
                          <div class="col-sm-12">
                              <div class="case-cent col-sm-offset-2">
                                <#if policyPersonList??>
                                      <div class="row margin-bottom-15">
                                          <div class="col-sm-4 text-right">
                                              可用保单选择：
                                          </div>
                                          <div class="col-sm-6">
                                              <select class="form-control js-data-example-ajax kp-select2" name="policyPersonId" id="policyPersonId">
                                                    <option value="">请选择</option>
                                              <#list policyPersonList as p>
                                                    <option value="${p.id}" data-startDate="${p.startDate?string['yyyy-MM-dd HH:mm:ss']}" data-endDate="${p.endDate?string['yyyy-MM-dd HH:mm:ss']}">${p.startDate?string["yyyy-MM-dd HH:mm"]} 至 ${p.endDate?string["yyyy-MM-dd HH:mm"]}</option>
                                              </#list>
                                              </select>
                                          </div>
                                      </div>
                              <#else >
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red; font-size: 20px;">暂无保全</span>
                                      </div>
                                  </div>
                              </#if>
                              </div>
                          </div>
                      </div>
                  </div>
                  <div class="col-sm-6">
                      <div class="row" style="margin: 30px 0px">
                          <div class="case-title col-sm-12 col-sm-offset-1" >事故信息</div>
                          <div class="col-sm-12">
                              <div class="case-cent col-sm-offset-1">
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          出险人肇事车辆信息：
                                      </div>
                                      <div class="col-sm-6">
                                          <select class="form-control js-data-example-ajax kp-select2" name="carBrand" id="carBrand">
                                              <option value="">--请选择--</option>
                                          </select>
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          出险人肇事车牌：
                                      </div>
                                      <div class="col-sm-6">
                                          <input type="text" class="form-control" name="plateNumber" id="plateNumber">
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          事故发生地点：
                                      </div>
                                      <div class="col-sm-6 margin-bottom-10">
                                          <div class="row">
                                              <div class="col-sm-4">
                                                  <select class="form-control" name="province" id="province" onchange="provinceChange()">
                                                      <option value="">-省-</option>
                                                  </select>
                                              </div>
                                              <div class="col-sm-4">
                                                  <select class="form-control" name="city" id="city" onchange="cityChange()">
                                                      <option value="">-市-</option>
                                                  </select>
                                              </div>
                                              <div class="col-sm-4">
                                                  <select class="form-control" name="district" id="district">
                                                      <option value="">-区-</option>
                                                  </select>
                                              </div>
                                          </div>
                                      </div>
                                      <div class="col-sm-8 col-sm-offset-2">
                                          <textarea class="form-control"  placeholder="请输入详细地址" rows="4" style="width: 100%"  name="address" id="address"></textarea>
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-10">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          事故经过描述：
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-30">
                                      <div class="col-sm-8 col-sm-offset-2">
                                          <textarea class="form-control" placeholder="请输入事故经过" rows="7" style="width: 100%"  name="description" id="description"></textarea>
                                      </div>
                                  </div>

                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          事故类型：
                                      </div>
                                      <div class="col-sm-6">
                                          <#--<input type="text" class="form-control" name="accidentType" id="accidentType" list="accidentTypeList">
                                          <datalist id="accidentTypeList">
                                              <option>单方</option>
                                              <option>多方</option>
                                          </datalist>-->
                                          <select class="form-control select2-multiple" name="accidentType"
                                                  id="accidentType">
                                              <option value="">请选择</option>
                                              <option value="单方">单方</option>
                                              <option value="多方">多方</option>
                                          </select>
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>事故责任：
                                      </div>
                                      <div class="col-sm-6">
                                          <#--<input type="text" class="form-control" name="accidentLiability" id="accidentLiability" list="accidentLiabilityList">
                                          <datalist id="accidentLiabilityList">
                                              <option>全责</option>
                                              <option>主责</option>
                                              <option>同责</option>
                                              <option>次责</option>
                                              <option>无责</option>
                                          </datalist>-->
                                          <select class="form-control select2-multiple" name="accidentLiability"
                                                  id="accidentLiability">
                                              <option value="">请选择</option>
                                              <#--<option value="全责">全责</option>
                                              <option value="主责">主责</option>
                                              <option value="同责">同责</option>
                                              <option value="次责">次责</option>
                                              <option value="无责">无责</option>-->
                                              <#list accidentDutyList as duty>
                                                  <option value="${duty}">${duty}</option>
                                              </#list>
                                          </select>
                                      </div>
                                  </div>

                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          <span style="color: red">*</span>
                                          事故比例：
                                      </div>
                                      <div class="col-sm-6">
                                          <input type="text" autocomplete="off" class="form-control" name="accidentProportion" id="accidentProportion" list="accidentProportionList">
                                          <datalist id="accidentProportionList">
                                              <option>0</option>
                                              <option>10</option>
                                              <option>20</option>
                                              <option>30</option>
                                              <option>40</option>
                                              <option>50</option>
                                              <option>60</option>
                                              <option>70</option>
                                              <option>80</option>
                                              <option>90</option>
                                              <option>100</option>
                                          </datalist>
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15" id="knightlllegalItems-content">
                                      <div class="col-sm-4 text-right">事故违规类型：</div>
                                      <div class="col-sm-6">
                                          <#if accidentViolationTypeMap?? && (accidentViolationTypeMap.keySet()?size > 0)>
                                              <ul class="list-inline">
                                                  <#list accidentViolationTypeMap.keySet() as key>
                                                      <li style="margin: 5px 0px;">
                                                          <button type="button" class="<#if knightlllegalItemsList?contains(key)>blue</#if>" data-value="${key}">${accidentViolationTypeMap.get(key)}</button>
                                                      </li>
                                                  </#list>
                                              </ul>
                                          </#if >
                                      </div>
                                  </div>
                                  <div class="row margin-bottom-15">
                                      <div class="col-sm-4 text-right">
                                          出险标签：
                                      </div>
                                      <div class="col-sm-6">
                                          <div class="block-show">
                                              <span name="treateLabel" class="span-type-unClick" code="Aax008">门诊</span>
                                              <span name="treateLabel" class="span-type-unClick" code="Aax009">住院</span>
                                              <span name="treateLabel" class="span-type-unClick" code="Aax010">死亡</span>
                                              <span name="treateLabel" class="span-type-unClick" code="Aax011">ICU</span>
                                              <span name="treateLabel" class="span-type-unClick" code="Aax021">残疾</span>
                                              <span name="treateLabel" class="span-type-unClick" code="Aax022">骨折</span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>

                      <div class="row dutyInfoArea" style="margin: 10px 20px 30px">
                          <div class="content-title col-sm-12">损失项目</div>
                          <div class="col-sm-12 clear-padding margin-bottom-10" style="border: 1px solid #e7ecf1;">
                              <table class="table table-striped table-hover table-striped table-header-fixed text-center clear-margin" >
                                  <thead>
                                  <tr>
                                      <th width="15%">类型</th>
                                      <th width="15%">赔付对象名称</th>
                                      <th width="15%">手机号</th>
                                      <th width="15%" >预估金额</th>
                                      <th width="15%" style="border-right: 1px solid #e7ecf1">医院名称</th>
                                      <th width="15%" >功能</th>
                                  </tr>
                                  </thead>
                                  <tbody id="dutyInfoShow">


                                  </tbody>
                              </table>
                          </div>
                      </div>
                      <div class="row" style="margin: 10px 20px 30px">
                          <div class="content-title col-sm-12" style="margin-bottom: 15px;"> <span style="color: red">*</span>沟通记录/新增备注</div>
                          <div class="col-sm-12 clear-padding margin-bottom-10">
                              <textarea placeholder="请输入" class="logTextArea" id="logTextArea"></textarea>
                          </div>
                      </div>
                      <div class="row text-center">
                          <button class="btn green" type="button" style="border-radius: 0px;margin-right: 20px" onclick="back()">返回</button>
                          <button class="btn btn-info" style="border-radius: 0px" type="button" onclick="submitClaimCase()">完成</button>
                      </div>
                  </form>
              </div>
              </#if>
        </div>
    </div>
</div>
</body>
</html>