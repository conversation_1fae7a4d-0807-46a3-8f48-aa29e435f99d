<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">

        function iframeH() {
            var ifm = parent.document.getElementById("contentFrame");
            if (ifm) {
                var ifmH = $(ifm).height()
                var h = $(document.body).height() + 120;
                if (ifmH < h) {
                    parent.document.getElementById("contentFrame").height = h;
                }
            }
        }

        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        var scrollTop;

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
            <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
            <#list labelShowMap.keySet() as key>
            labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
            </#list>
            </#if>
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
            <#if claimCaseVo.label?exists>
            <#list claimCaseVo.label?split(",") as code>
            nowLabel.push("${code}");
            </#list>
            console.log(nowLabel);
            $("#label").val(nowLabel).trigger('change');
            </#if>

            //导出按钮监控器
            $("#searchForm").on("click", ".exportBtn", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _this = this;
                $(_this).removeClass("exportBtn");
                //提交之后再把class加回来 防止重复提交
                postForm("${ctx}/downloadCenterController/downloadUnsettledCase");
                $(_this).addClass("exportBtn");
            });

            var postForm = function (url) {
                // 创建表单
                var formElement = document.createElement("form");
                formElement.action = url;
                formElement.method = "post";
                // 打开新标签
                formElement.target = '_blank';
                formElement.style.display = "none";
                formElement.appendChild(createInput("status", $.trim($("#status").val())));
                formElement.appendChild(createInput("claimCaseNo", $.trim($("#claimCaseNo").val())));
                formElement.appendChild(createInput("applyName", $.trim($("#applyName").val())));
                formElement.appendChild(createInput("applyMobile", $.trim($("#applyMobile").val())));
                formElement.appendChild(createInput("treatIdNum", $.trim($("#treatIdNum").val())));
                formElement.appendChild(createInput("applyType", $.trim($("#applyType").val())));
                formElement.appendChild(createInput("treatDateStart", $.trim($("#treatDateStart").val())));
                formElement.appendChild(createInput("treatDateEnd", $.trim($("#treatDateEnd").val())));
                formElement.appendChild(createInput("startDateStart", $.trim($("#startDateStart").val())));
                formElement.appendChild(createInput("startDateEnd", $.trim($("#startDateEnd").val())));
                formElement.appendChild(createInput("label", $.trim($("#label").val())));
                formElement.appendChild(createInput("insCode", $.trim($("#insCode").val())));

                document.body.appendChild(formElement);
                formElement.submit();
                formElement.remove();
                return;
            }
        });

        var createInput = function (name, value) {
            var inputElement = document.createElement("input");
            inputElement.type = "hidden";
            inputElement.name = name;
            if (value != null) {
                inputElement.value = value;
            }
            return inputElement;
        }


        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
        }

        function statusSwitch(status) {
            layer.msg("数据查询中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000']});
            $("#status").val(status);
            $("#searchForm").submit();
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        function changeInsCode(obj) {
            var insCode = $(obj).val();
            $("#insCode").val(insCode);
        }

        // 未决推送窗口
        function unsettledPushContainer(claimCaseNo, systemAmount, pushedAmount) {
            $("#unsettledContainer").find("#pushClaimCaseNo").val(claimCaseNo);
            $("#unsettledContainer").find("#systemAmount").val(systemAmount);
            $("#unsettledContainer").find("#pushedAmount").val(pushedAmount);
            $("#unsettledContainer").find("#estimatedAmount").val(systemAmount);
            layer.open({
                title: "未决推送",
                type: 1,
                area: ['600px', '450px'],
                fixed: false,
                offset: scrollTop,
                closeBtn: 1,
                content: $('#unsettledContainer'),
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

        // 单案件推送未决
        function pushUnsettledCase() {
            var claimCaseNo = $("#unsettledContainer").find("#pushClaimCaseNo").val();
            var estimatedAmount = $("#unsettledContainer").find("#estimatedAmount").val();
            var riskCode = "ZHA";       // 默认为财险推送
            <#if claimCaseVo.status == 4>
                var policyNo = $("#unsettledContainer").find("#policyNo").val();
                if (!policyNo) {
                    layer.msg("请输入保单号", {icon: 2, time: 2000, shade: [0.1, '#000']});
                    return;
                }
                if (policyNo.includes("5XA")) {
                    riskCode = "5XA";
                } else {
                    riskCode = "5XB";
                }
            </#if>
            if (!claimCaseNo || !estimatedAmount) {
                layer.msg("案件号或推送金额不存在", {icon: 2, time: 2000, shade: [0.1, '#000']});
                return;
            }
            var formData = new FormData();
            formData.append("claimCaseNo", claimCaseNo);
            formData.append("riskCode", riskCode);
            formData.append("pushAmountStr", estimatedAmount);
            layer.confirm("请确认推送金额是否存在超保额情况，<br/>确认完毕请点击推送", {icon: 3, title: '温馨提示'}, function (index) {
                layer.msg("数据推送中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000']});
                $.ajax({
                    url: "${ctx}/claimCaseController/pushUnsettledCase",
                    data: formData,
                    type: 'POST',
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == "0") {
                            layer.msg(result.msg, {icon: 1, time: 2000, shade: [0.1, '#000']}, function (index) {
                                window.location.reload();
                                layer.close(index);
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000, shade: [0.1, '#000']}, function (index) {
                                layer.close(index);
                            });
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        // 导入案件推送未决
        function pushUnsettledClaimData() {
            if ($("#uploadFile").val() == undefined || $("#uploadFile").val() == "") {
                layer.msg("请选择文件", {icon: 2, time: 1500, shade: [0.1, '#000']});
                return;
            }
            layer.msg("数据推送中，请稍候", {icon: 16, time: 10000, shade: [0.1, '#000']});
            var formData = new FormData($("#searchForm")[0]);
            formData.append("isConfirm", "0");
            var layerTop = top.layer;
            $.ajax({
                url: "${ctx}/claimCaseController/importPushUnsettledClaimCase",
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("("+data+")");
                    if (result.ret == "1001") {
                        layer.confirm("请确认该批次推送金额是否存在超保额情况，<br/>" + result.msg + "，<br/>确认完毕请点击推送？", {icon: 3, title: '温馨提示'}, function (index) {
                            formData.set("isConfirm", "1");
                            $.ajax({
                                url: "${ctx}/claimCaseController/importPushUnsettledClaimCase",
                                type: 'POST',
                                data: formData,
                                async: false,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success: function (data) {
                                    var result = eval("("+data+")");
                                    if (result.ret == "0") {
                                        layerTop.msg(result.msg, {icon: 1, time: 2000, shade: [0.1, '#000']}, function (index) {
                                            window.location.reload();
                                            layer.close(index);
                                        });
                                    } else {
                                        $("#errorInfo").html("");
                                        $("#errorInfo").append("<div style='padding-top: 10px; color: #e34444;' class='errorDiv1'>错误原因如下：</div>");
                                        $("#errorInfo").append("<div style='padding: 10px 20px; color: #e34444;' class='errorDiv1'>" + result.msg + "</div>");
                                        $("#errorInfo").append("<div style='padding-top: 20px; padding-left: 20px;'class='errorDiv1'>"
                                            + 	"<font>备注：如有疑问，请联系客服！</font>"
                                            +  "</div>");
                                        $("#errorDiv").show();
                                        iframeH();      // 重新计算iframe高度
                                        layer.closeAll();
                                    }
                                },
                                error: function (data) {
                                    var result = eval("(" + data + ")");
                                    alert(result.msg);
                                    layer.closeAll();
                                }
                            });
                        });
                    } else {
                        $("#errorInfo").html("");
                        $("#errorInfo").append("<div style='padding-top: 10px; color: #e34444;' class='errorDiv1'>错误原因如下：</div>");
                        $("#errorInfo").append("<div style='padding: 10px 20px; color: #e34444;' class='errorDiv1'>" + result.msg + "</div>");
                        $("#errorInfo").append("<div style='padding-top: 20px; padding-left: 20px;'class='errorDiv1'>"
                            + 	"<font>备注：如有疑问，请联系客服！</font>"
                            +  "</div>");
                        $("#errorDiv").show();
                        iframeH();      // 重新计算iframe高度
                        layer.closeAll();
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                    layer.closeAll();
                }
            });
        }

    </script>
    <style>

        .select2-dropdown {
            z-index: 19891099 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }

        input[type="radio"] {
            position: relative !important;
            margin: 0px !important;
        }
    </style>
</head>
<body>

<#--未决推送-->
<div id="unsettledContainer" style="display: none; width:100%; height:100%; padding: 10px 3%; background-color: white;">
        <div class="row" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">案件号：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="pushClaimCaseNo" id="pushClaimCaseNo"
                       value="" disabled/>
            </div>
        </div>
        <div class="row" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">险种：</div>
            <div class="col-sm-8">
                <input type="radio" <#if claimCaseVo.status != 4>checked</#if> disabled> 财险
                <input type="radio" <#if claimCaseVo.status == 4>checked</#if> disabled> 意险
            </div>
        </div>
        <#if claimCaseVo.status == 4>
        <div class="row" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">保单号：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="policyNo" id="policyNo"
                       value=""/>
            </div>
        </div>
        </#if>
        <div class="row" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">系统未决金额：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="systemAmount" id="systemAmount"
                       value="" disabled/>
            </div>
        </div>
        <div class="row" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">已推未决金额：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="pushedAmount" id="pushedAmount"
                       value="" disabled/>
            </div>
        </div>
        <div class="row" style="margin: 20px;display: flex;align-items: center">
            <div class="col-sm-3 text-right">预计推送金额：</div>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="estimatedAmount" id="estimatedAmount"
                       value="" placeholder="请输入推送未决金额" <#if claimCaseVo.status == 4>disabled</#if>/>
            </div>
        </div>
        <div class="row text-center" style="margin: 20px;display: flex;justify-content: space-evenly;">
            <button class="btn btn-bule margin-right-10"
                    onclick="pushUnsettledCase()">推送
            </button>
            <button class="btn btn-warning"
                    onclick="javascript:layer.closeAll();">取消
            </button>
        </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">未决案件</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseController/unsettledClaimCaseList"
                      method="post" enctype="multipart/form-data">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${claimCaseVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyName" id="applyName"
                                               value="${claimCaseVo.applyName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案人手机号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="applyMobile" id="applyMobile"
                                               value="${claimCaseVo.applyMobile}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <#-- <div class="col-sm-4">
                                 <div class="form-group">
                                     <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>
                                     <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple" multiple name="applyType" id="applyType">
                                            <option value="">请选择</option>
                                            <#list applyTypeList as type>
                                                <option value="${type.code}" <#if claimCaseVo.applyType?contains("${type.code}")>selected</#if>>${type.parentTypeName+"-"+type.childTypeName}</option>
                                            </#list>
                                        </select>
                                     </div>
                                 </div>
                             </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${claimCaseVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人身份证：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${claimCaseVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple" name="applyType"
                                                id="applyType">
                                            <option value=" " selected>请选择</option>
                                            <#if appyTypeNewMap?exists>
                                                <#list appyTypeNewMap.keySet() as key>
                                                    <option value="${key}" <#if claimCaseVo.applyType==key>selected</#if>>${appyTypeNewMap.get(key).msg}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                            <@shiro.hasPermission name="CLAIM_SHOW_INS_SELECT">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="control-label col-sm-3" style="padding-right: 0">保险公司：</label>
                                        <div class="col-sm-8" style="padding-left: 0;">
                                            <select class="form-control select2-multiple" name="insCodeSelect"
                                                    id="insCodeSelect" onchange="changeInsCode(this)">
                                                <option value=" " selected>请选择</option>
                                                <#if insCodeMap?exists>
                                                    <#list insCodeMap.keySet() as key>
                                                        <option value="${key}" <#if claimCaseVo.insCode==key>selected</#if>>${insCodeMap.get(key)}</option>
                                                    </#list>
                                                </#if>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </@shiro.hasPermission>
                            <input type="hidden" name="insCode" id="insCode" value="${claimCaseVo.insCode}">
                        </div>
                        <div class="row">
                            <#--<div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">是否立案：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                       <select class="form-control" name="isRegister" id="isRegister" value="">
                                           <option value="">请选择</option>
                                           <option value="1" <#if claimCaseVo.isRegister==1>selected</#if>>是</option>
                                           <option value="0" <#if claimCaseVo.isRegister==0>selected</#if>>否</option>
                                       </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="treatDateStart" id="treatDateStart" autocomplete="off"
                                                   value="${claimCaseVo.treatDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="treatDateEnd" id="treatDateEnd" autocomplete="off"
                                                   value="${claimCaseVo.treatDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案时间：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <div class="input-group date-picker input-daterange"
                                             data-date-format="yyyy-mm-dd" >
                                            <input type="text" class="form-control" name="startDateStart" id="startDateStart" autocomplete="off"
                                                   value="${claimCaseVo.startDateStart}">
                                            <span class="input-group-addon"><font style="vertical-align: inherit;"><font
                                                            style="vertical-align: inherit;"> 至 </font></font></span>
                                            <input type="text" class="form-control" name="startDateEnd" id="startDateEnd" autocomplete="off"
                                                   value="${claimCaseVo.startDateEnd}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">标签：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control select2-multiple"  name="label"
                                                id="label" multiple>
                                            <option value="">请选择</option>
                                            <#--<#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
                                                <#list labelShowMap.keySet() as key>
                                                    <option <#if (claimCaseVo.label!'-')==key>selected</#if>value="${key}">
                                                        ${labelShowMap.get(key).msg}
                                                    </option>
                                                </#list>
                                            </#if>-->
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12" >
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                                <#if claimCaseVo.status != 4>
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <a href="#" class="btn warning exportBtn" style="margin-bottom: 10px;border: 1px solid #c1baba" >导出</a>
                                </div>
                                </#if>
                            </div>
                            <#if claimCaseVo.status != 4>
                            <div class="col-sm-12" >
                                <div class="pull-right uploadFilebox" style="margin-bottom: 10px;margin-right: 30px;">
                                    <div class="fileUpload btn btn-primary margin-right-10">
                                        <input id="uploadBtn" type="file" name="file" class="upload" accept=".xlsx"/>
                                    </div>
                                    <input id="uploadFile" placeholder="请选择文件" disabled="disabled" type="hidden"/>
                                    <script>
                                        document.getElementById("uploadBtn").onchange = function () {
                                            $("#uploadFile").val(this.value);
                                        };
                                    </script>&nbsp;&nbsp;
                                    <button type="button" id="subBtn" class="btn btn-default"
                                            onClick="pushUnsettledClaimData();">上载推送
                                    </button>
                                </div>
                            </div>
                            </#if>
                            <div class="col-sm-12">
                                <div class="mt-element-list" style="display: none;" id="errorDiv">
                                    <div class="mt-list-container list-news" style="box-shadow: 2px 2px 7px rgba(0, 0, 0, .4);" id="containerUl">
                                        <div class="pull-right" style="margin-right: 10px; margin-top: -5px;">
                                            <a class="btn btn-warning" onclick="javascript:$('#errorDiv').hide();">关闭</a>
                                        </div>
                                        <div class="list-item-content formstyle1 enterset_con_l"
                                             style="padding-top: 10px; font-size: 16px; padding-bottom: 20px;" id="errorInfo">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-active">
                        <div>
                            <input type="hidden" name="status" id="status" value="${claimCaseVo.status}">
                            <ul>
                                <li class="li-default <#if claimCaseVo.status == 1>li-blue</#if>" onclick="statusSwitch(1)">未决案件（财）
                                </li>
                                <li class="li-default <#if claimCaseVo.status == 2>li-blue</#if>" onclick="statusSwitch(2)">已推未决（财）
                                </li>
                                <li class="li-default <#if claimCaseVo.status == 3>li-blue</#if>" onclick="statusSwitch(3)">未推未决（财）
                                </li>
                                <li class="li-default <#if claimCaseVo.status == 4>li-blue</#if>" onclick="statusSwitch(4)">未决案件（意）
                                </li>
                            </ul>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="10%">出险人姓名</th>
                        <th width="10%">出险人身份证</th>
                        <th width="12%">出险类型</th>
                        <th width="10%">标签</th>
                        <th width="7%">已推未决金额</th>
                        <th width="7%">当前未决金额</th>
                        <th width="13%" class="td-overflow">案件状态</th>
                        <th width="13%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as claimCase>
                        <tr>
                            <td title="">${claimCase.claimCaseNo}</td>
                            <td title="">${claimCase.treatName}</td>
                            <td title="">${claimCase.treatIdNum}</td>
                            <td>
                                <#if claimCase.applyType??>
                                    <#list claimCase.applyType?split(",") as name>
                                        <span class="span-type">${name}</span>
                                    </#list>
                                </#if>
                            </td>
                            <td title="" class="labelGroup">
                                <#if claimCase.label??>
                                    <#list claimCase.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
                            <!--已推未决金额-->
                            <td>${claimCase.payAmount!'--'}</td>
                            <!--当前未决金额-->
                            <td>${claimCase.appraisalAmount!'--'}</td>
                            <td class="td-overflow" title="${claimCaseStatusEumMap.get(claimCase.status).msg}">
                                ${claimCaseStatusEumMap.get(claimCase.status).msg}
                            </td>
                            <td>
                                <a href="#" onclick="caseDetail('${claimCase.id}')">查看详情</a>
                                <a href="#" onclick="unsettledPushContainer('${claimCase.claimCaseNo}', '${claimCase.appraisalAmount}', '${claimCase.payAmount}')">推送未决</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>