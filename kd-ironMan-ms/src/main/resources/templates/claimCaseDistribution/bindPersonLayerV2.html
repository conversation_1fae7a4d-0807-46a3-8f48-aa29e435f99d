<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>任务分配</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>

    <script>

        $(function() {
            $("#dataManager").on("click", "button", function(){
                $(this).addClass("active");
                $(this).siblings().removeClass("active");
            });
        })

        function push() {

            var managerId = $("button.active").attr("data-managerId");
            console.log(managerId);

            if (!managerId) {
                layer.msg("请选择分配的人员", {icon: 2, time: 1500});
                return;
            }

            layer.confirm("请再次确认是否分配完成？", {icon: 3}, function (index) {
                    var formData = new FormData();
                    formData.append("claimCaseId", "${claimCase.id}");
                    formData.append("pretreatmentAuditer", managerId);
                    $.ajax({
                        url: "${ctx}/claimCaseDistributionController/bindAuditerV2",
                        type: 'POST',
                        data: formData,
                        async: true,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            var result = eval("(" + data + ")");
                            if (result.ret == "0") {
                                layer.msg(result.msg, {icon: 1, time: 2000}, function(index) {
                                    parent.window.location.reload();
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(result.msg, {
                                    icon: 2,
                                    time: 1500
                                });
                            }
                        },
                        error: function (data) {
                            var result = eval("(" + data + ")");
                            alert(result.msg);
                        }
                    });
                }
            );
        }
    </script>
    <style>
        .row {
            margin-top: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .btn-div {
            text-align: right;
            margin: 10px;
        }

        .btn-div > button {
            margin-right: 20px;
        }
        button.active {
            background: #0597FF;
            border-color: #ecebeb;
            color: #fff !important;
        }
    </style>
</head>
<body>
    <div>

        <div class="row">
            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12">
                        案件号：${claimCase.claimCaseNo}
                    </div>
                </div>

                <div class="col-sm-4" style="padding: 0px;">
                    <div class="col-sm-12" style="padding: 0px;">
                        当前状态：${claimCaseStatusEumMap.get(claimCase.status)}
                    </div>
                </div>

            </div>


            <div class="col-sm-12" style="margin-bottom: 20px;padding-left: 0px;padding-right: 0px;">
                <div class="col-sm-12" style="margin-bottom: 10px;">
                    预处理人员(任务数)：
                </div>
                <div class="col-sm-12" style="margin-bottom: 10px;" id="dataManager">
                    <#if auditerMap?? && (auditerMap.keySet()?size>0)>
                        <div style="display: flex"></div>
                        <#list auditerMap.keySet() as key>
                                <button class="margin-right-10 btn" data-managerId="${key}">${auditerMap.get(key)}</button>
                        </#list>
                    </#if>
                </div>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12 btn-div" >
            <button class="btn btn-primary" onclick="push()">分配</button>
            <button class="btn btn-warning" onclick="parent.layer.closeAll();">取消</button>
        </div>
    </div>
</body>
</html>