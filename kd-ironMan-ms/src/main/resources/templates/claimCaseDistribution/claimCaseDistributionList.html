<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });
        });


        //查看详情
        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
        }


        //分配人员
        function bindPerson(id){
            layer.open({
                title: "人员任务分配",
                type: 2,
                area: ['900px','500px'],
                offset: 'auto',
                fix: false, //不固定
                maxmin: true,
                scrollbar: false,
                content: '${ctx}/claimCaseDistributionController/getBindPersonLayer?id='+id,
                success: function (layero, index) {
                    // layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style>

        .layui-layer-msg {
            z-index: 1989101411 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }

        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }
    </style>
</head>
<body>

<#--<h1 onclick="dj()">点击</h1>-->

<#--补发短信-->
<div id="bindPersonContainer" style="display: none;width: 100%;height: 100%">
    <div class="row" style="padding-top: 30px;margin: 0px 0px">
        <div class="col-sm-3">
            <span class="pull-right" style="font-size: 10px;color: #7f7f7f">选择短信类型：</span>
        </div>
        <div class="col-sm-9">
            <#if messageType?? && (messageType.keySet()?size>0)>
                <#list messageType.keySet() as key>
                    <div class="col-sm-3 clear-padding" style="margin-bottom: 10px;">
                        <input name="messageType" style="left: 0px;right: 0px;margin-left: 0px;" code="${key}"
                               type="radio">${messageType.get(key)}
                    </div>
                </#list>
            </#if>
            <select class="form-control select2" name="primaryAgentId" id="primaryAgentId">
                <option value="">-请选择-</option>
                <#if totalAgentMap?? && (totalAgentMap.keySet()?size>0) >
                    <#list totalAgentMap.keySet() as key>
                        <option value="${key}" <#if baseUserReq.primaryAgentId == key>selected </#if>>${totalAgentMap.get(key)}</option>
                    </#list>
                </#if>
            </select>

        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>业务管理</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">案件分配</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/claimCaseDistributionController/claimCaseDistributionList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">报案号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${caseDistributionVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人姓名：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatName" id="treatName"
                                               value="${caseDistributionVo.treatName}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">出险人证件号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="treatIdNum" id="treatIdNum"
                                               value="${caseDistributionVo.treatIdNum}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="10%">报案号</th>
                        <th width="7%">出险人姓名</th>
                        <th width="8%">出险人身份证</th>
                        <th width="7%">标签</th>
<#--                        <th width="8%">预计赔付金额</th>-->
                        <th width="6%" class="td-overflow">案件状态</th>
                        <th width="10%">功能</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="">${vo.claimCaseNo}</td>
                            <td title="">${vo.treatName}</td>
                            <td title="">${vo.treatIdNum}</td>
                            <td title="" class="labelGroup">
                                <#if vo.label??>
                                    <#list vo.label.split(",") as key>
                                        <#if (key?trim)!="">
                                            <span class="${key} span-type" style="color: ${labelShowMap.get(key).fontColor};background-color: ${labelShowMap.get(key).color}"><#if labelShowMap.get(key)??>${labelShowMap.get(key).msg}<#else>${key}</#if></span>
                                        </#if>
                                    </#list>
                                </#if>
                            </td>
<#--                            <td title="">${vo.estimatedApprovedMoney!'--'}</td>-->
                            <td class="td-overflow" title="${claimCaseStatusEumMap.get(vo.status).msg}">
                                ${claimCaseStatusEumMap.get(vo.status).msg}
                            </td>
                            <#--功能-->
                            <td>
                                <#if !vo.status?starts_with("aax")>
                                    <a onclick="bindPerson('${vo.id}')">分配人员</a>
                                </#if>
                                <a onclick="bindPerson('${vo.id}')">分配人员</a>
                                <a onclick="caseDetail('${vo.id}')">查看详情</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>