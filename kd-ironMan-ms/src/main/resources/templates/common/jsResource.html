<#include "/common/commonContext.html">
<!--[if lt IE 9]>
<script src="${ctx}/metronic/global/plugins/respond.min.js"></script>
<script src="${ctx}/metronic/global/plugins/excanvas.min.js"></script> 
<![endif]-->
<!-- BEGIN CORE PLUGINS -->
<script src="${ctx}/metronic/global/plugins/jquery.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/js.cookie.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/uniform/jquery.uniform.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
<!-- END CORE PLUGINS -->

<!-- BEGIN COMMON PAGE LEVEL PLUGINS -->
<script src="${ctx}/metronic/global/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/jquery-validation/js/additional-methods.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/jquery-validation/js/localization/messages_zh.min.js" type="text/javascript"></script>
<!-- END COMMON PAGE LEVEL PLUGINS -->

<!-- BEGIN THEME GLOBAL SCRIPTS -->
<script src="${ctx}/metronic/global/scripts/app.min.js" type="text/javascript"></script>
<!-- END THEME GLOBAL SCRIPTS -->

<!-- BEGIN THEME LAYOUT SCRIPTS -->
<!-- <script src="${ctx}/metronic/layouts/layout4/scripts/layout.min.js" type="text/javascript"></script> -->
<!-- <script src="${ctx}/metronic/layouts/layout4/scripts/demo.min.js" type="text/javascript"></script> -->
<!-- <script src="${ctx}/metronic/layouts/global/scripts/quick-sidebar.min.js" type="text/javascript"></script> -->
<!-- END THEME LAYOUT SCRIPTS -->

<script src="${ctx}/metronic/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/pages/scripts/components-date-time-pickers.min.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
<script src="${ctx}/plugins/layer/layer.js" type="text/javascript"></script>
<script src="${ctx}/plugins/laydate/laydate.js"></script>

<script src="${ctx}/js/custom/loaders.min.js" type="text/javascript"></script>
<script src="${ctx}/js/custom/calculateSize.min.js" type="text/javascript"></script>

<script src="${ctx}/js/BigDecimal.js" type="text/javascript"></script>
<script>
try {
	document.domain="ldschuzhou.com"
}catch (e) {
	
}
$(document).ready(function () {
	try {
		parent.iFrameHeight();
	}catch (e) {
		
	}
});
if (!window.JSON) {
	window.JSON = {
		stringify: function(obj) {
			var result = "";
			for (var key in obj) {
				if (typeof obj[key] == "string") {
					/* 如果属性值是String类型，属性值需要加上双引号*/
					result += "\"" + key + "\":\"" + obj[key] + "\",";
				} else if (obj[key] instanceof RegExp) {
					/* 如果属性是正则表达式，属性值只保留一对空大括号{}*/
					result += "\"" + key + "\":{},";
				} else if (typeof obj[key] == "undefined" || obj[key] instanceof Function) {
					/* 如果属性值是undefined, 该属性被忽略。忽略方法。*/
				} else if (obj[key] instanceof Array) {
					/* 如果属性值是数组*/
					result += "\"" + key + "\":[";
					var arr = obj[key];
					for (var item in arr) {
						if (typeof arr[item] == "string") {
							/* 如果数组项是String类型，需要加上双引号*/
							result += "\"" + arr[item] + "\",";
						} else if (arr[item] instanceof RegExp) {
							/* 如果属数组项是正则表达式，只保留一对空大括号{}*/
							result += "{},";
						} else if (typeof arr[item] == "undefined" || arr[item] instanceof Function) {
							/*	 如果数组项是undefined, 则显示null。如果是函数，则显示null?。*/
							result += null + ",";
						} else if (arr[item] instanceof Object) {
							/*如果数组项是对象(非正则，非函数，非null)，调用本函数处理*/
							result += this.stringify(arr[item]) + ",";
						} else {
							result += arr[item] + ",";
						}
					}
					result = result.slice(0, -1) + "],"

				} else if (obj[key] instanceof Object) {
					/* 如果属性值是对象(非null，非函数，非正则)，调用本函数处理*/
					result += "\"" + key + "\":" + this.stringify(obj[key]) + ",";
				} else {
					result += "\"" + key + "\":" + obj[key] + ",";
				}
			}
			/* 去除最后一个逗号,两边加{}*/
			return "{" + result.slice(0, -1) + "}";
		}
	};
};
// document.domain = "localhost"; //此句不可删除，解决跨域问题
</script>
