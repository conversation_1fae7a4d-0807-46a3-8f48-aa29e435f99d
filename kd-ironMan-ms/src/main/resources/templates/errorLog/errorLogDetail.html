<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <!-- Custom styles for this template -->
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/static/css/labelColor.css" rel="stylesheet" type="text/css"/>
    <#--widget-->
    <script src="${ctx}/metronic/global/plugins/counterup/jquery.waypoints.min.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/counterup/jquery.counterup.min.js" type="text/javascript"></script>
    <#--echarts-->
    <script src="${ctx}/a/echarts.min.js"></script>
    <script type="text/javascript">
        function startAudit2(id) {
            $.ajax({
                url: "${ctx}/errorLogController/updateErrorLogStatus?errorLogId=" + id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function startAudit3(id) {
            $.ajax({
                url: "${ctx}/errorLogController/repush?errorLogId="+id,
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }
    </script>
    <style>
        .form-group {
            margin-bottom: 10px !important;
        }

        .widget-thumb .widget-thumb-heading {
            font-size: 16px !important;
        }

        .caption-subject {
            font-weight: 500 !important;
        }

    </style>
</head>
<body>
<!-- BEGIN PAGE BASE CONTENT -->
<div class="row">
    <div class="col-sm-12">
        <!-- BEGIN EXAMPLE TABLE PORTLET-->
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <div class="page-bar">
                    <ul class="page-breadcrumb breadcrumb">

                        <li><a href="${ctx}/claimCaseController/serviceClaimCaseList">错误日志</a> <i
                                    class="fa fa-circle"></i></li>
                        <li><span class="active">查看</span></li>
                    </ul>
                </div>
            </div>
            <div class="portlet-body">

                <div class="row">
                    <div class="col-sm-12 form-group">
                        <h4>
                            <i class="fa fa-bookmark"></i>
                            <span class="caption-subject uppercase">错误信息</span>
                        </h4>
                    </div>
                    <div class="col-sm-12 form-group">
                        <div class="portlet light bordered">
                            <div class="row">
                                <div class="col-sm-12 ">
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        业务描述：

                                        ${errorLog.businessName}
                                    </div>
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        方法/接口/mq名称：
n
                                        ${errorLog.methodName}
                                    </div>
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        请求来源：

                                        <#if errorLog.comeFrom == 1>
                                            我方
                                        <#else >
                                            他方
                                        </#if>
                                    </div>
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        请求类型：
                                        <#if errorLog.reqType == 1>
                                            内部请求
                                        <#else >
                                            外部请求
                                        </#if>
                                    </div>
                                </div>
                                <div class="col-sm-12 ">
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        状态：

                                        <#if errorLog.status == -1>
                                            已撤销
                                        <#elseif errorLog.status == 0>
                                            未处理
                                        <#else >
                                            已处理
                                        </#if>
                                    </div>
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        备注：
                                        ${errorLog.remark}
                                    </div>
                                    <div class="col-sm-3 form-group label-hide-overflow">
                                        发生时间：
                                        ${errorLog.createTime?string('yyyy-MM-dd HH:mm:ss')}
                                    </div>

                                </div>
                                <div class="col-sm-12 ">
                                    <div class="col-sm-12 form-group " >
                                        描述：
                                        ${errorLog.description}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12 form-group">
                        <h4>
                            <div>
                                <i class="fa fa-bookmark"></i>
                                <span class="caption-subject uppercase">错误信息</span>
                            </div>

                        </h4>

                    </div>
                    <div class="col-sm-12 form-group">
                        <div class="portlet light bordered">
                            <div class="row">
                                <div class="col-sm-12 ">
                                    <div class="col-sm-12 form-group">
                                        ${errorLog.reqData}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <div class="row">
                        <div class="col-sm-1">
                        </div>
                        <div class="col-sm-3">
<#--                            <button type="button" class="btn green" onclick="repush('${errorLog.id}')">重新推送</button>-->
                        </div>
                        <div class="col-sm-3">
                            <#if errorLog.status == 0>
                                <#if errorLog.methodName == '/api/ins/batchRepushInsByBatchNo' || errorLog.methodName == '/api/ins/batchInsPolicyConfirm'>
                                    <button type="button" class="btn green" onclick="startAudit3('${errorLog.id}')">重新推送</button>
                                <#else >
                                    <button type="button" class="btn green" onclick="startAudit2('${errorLog.id}')">已处理</button>
                                </#if>
                            </#if>
                        </div>
                        <div class="col-sm-3">
                             <button type="button" class="btn default" onclick="javascript :history.back(-1);">返回</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>