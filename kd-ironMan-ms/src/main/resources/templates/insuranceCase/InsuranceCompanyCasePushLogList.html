<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            calculationScrollTop();

            $("table").on("click", "td:not(:last-child)", function(e) {
                var $input = $("<input>");
                $("body").append($input);
                $input.val($(this).html().trim()).select();
                document.execCommand("copy");
                $input.remove();
                layer.tips("复制成功", $(this), {icon: 1, time: 500, tips: 1});
            });
        });


        var scrollTop;  // 定义滚动高度

        function page(n,s){
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        function caseDetail(claimCaseId) {
            window.location.href="${ctx}/claimCaseController/caseDetail?caseId="+claimCaseId;
        }

        function casePushELM(id,payAmountStr) {
            $("#payTable").find("#paytime").val('');
            $("#payTable").find("#payAmount").val(payAmountStr);

            var openWindowWidth = $(document).width() * 0.5 + "px";
            var offsetH = ($(window).height() / 5 - 20 > 360 ? 120 : $(window).height() / 5 - 20) + "px";
            layer.open({
                type: 1,
                title: '支付时间',
                area: openWindowWidth,
                offset: offsetH,
                shadeClose: true,
                fix: false, //不固定
                btn: ['确定', '取消'], //按钮
                maxmin: true,
                btn1: function (index, layero) {
                    //当点击‘确定'按钮的时候，获取弹出层返回的值
                    var res = {"paytime":$("#payTable").find("#paytime").val(),"payAmount":$("#payTable").find("#payAmount").val()}
                    if (res.paytime.trim() && res.payAmount.trim()) {
                        //打印返回的值，看是否有我们想返回的值。
                        var formData = new FormData();
                        formData.append("id", id);
                        formData.append("payTimeStr", res.paytime);
                        formData.append("payAmountStr", res.payAmount);
                        $.ajax({
                            url: "${ctx}/claimCaseController/casePushELM",
                            type: 'POST',
                            data: formData,
                            async: false,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (data) {
                                var result = eval("(" + data + ")");
                                if (result.ret == "0") {
                                    layer.msg(result.msg, {
                                        icon: 1,
                                        time: 2000
                                    },function () {
                                        window.location.reload();
                                    });
                                } else {
                                    layer.msg(result.msg, {
                                        icon: 2,
                                        time: 2500 //1秒关闭（如果不配置，默认是3秒）
                                    });
                                }
                            },
                            error: function (data) {
                                var result = eval("(" + data + ")");
                                alert(result.msg);
                            }
                        });
                        //最后关闭弹出层
                        layer.close(index);
                    } else {
                        alert("支付时间与支付金额不能为空");
                    }

                },
                content: $("#payTable")
            });
        }


        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }


    </script>
    <style>
        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
        }

        .form-active > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
        }

        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 22px;
            font-size: 16px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
        }

        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }

        .li-default:nth-of-type(2) {
            border-left: 1px solid #e7ecf1;
            border-right: 1px solid #e7ecf1;
        }

        .li-default:nth-of-type(3) {
            border-right: 1px solid #e7ecf1;
        }

        .li-blue {
            background: #0b94ea;
            color: #fff;
        }

        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .applyTypeGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #c9c8c8;
            border-radius: 2px;
        }
        .applyTypeGroup .AA001,.AA002,.AA003,.AA004{
            background-color: #1676ff !important;
            color: white;
        }
        .applyTypeGroup .AB001,.AB002,.AB003,.AB004{
            background-color: #ff6633 !important;
            color: white;
        }
        .applyTypeGroup .AC001,.AC002,.AC003,.AC004{
            background-color: #CA0000 !important;
            color: white;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        td:hover {
            cursor: pointer;
        }

        td > a , td > span {
            display: inline-block;
            margin: 3px;
        }

        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }

        .logListInfo {
            margin-bottom: 2%;
        }

        .logListInfo table {
            border: 1px solid #C2C2C2;
        }

        .logListInfo .detailsInfo {
            display: none;
            padding-left: 8%;
        }

        .minus {
            border: 1px solid black;
            border-radius: 50%;
            width: 13px;
            height: 13px;
            position: relative;
            margin-top: 3%;
        }

        .minus::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 5px;
            width: 7px;
            border-top: 1px solid;
        }

        .logListInfo .detailsInfo > td {
            word-wrap:break-word;
            word-break:break-all;
            text-align: left;
        }
        .logListInfo .rowInfo:hover{
            cursor: pointer;
        }

        .label-title {
            font-size: 18px !important;
        }
    </style>
</head>
<body>

<div id="payTable" style="display: none">
    <div class="block-show">
        <div class="form-group" style="margin-top: 20px;display: flex;align-items: center;">
            <label class="control-label col-sm-2 " style="padding-right: 0;text-align: right;">支付时间：</label>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="paytime" id="paytime" autocomplete="off" placeholder="yyyy-MM-dd HH:mm:ss">
            </div>
        </div>
        <div class="form-group" style="margin-top: 20px;display: flex;align-items: center;">
            <label class="control-label col-sm-2 " style="padding-right: 0;text-align: right;">支付金额：</label>
            <div class="col-sm-8">
                <input type="text" class="form-control" name="payAmount" id="payAmount" autocomplete="off" placeholder="$.$$">
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">保司案件推送日志查看</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/insuranceCaseController/insuranceCompanyCasePushLogList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">平台案件号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="claimCaseNo" id="claimCaseNo"
                                               value="${pushLogVo.claimCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保司案件号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="insuranceCompanyCaseNo" id="insuranceCompanyCaseNo"
                                               value="${pushLogVo.insuranceCompanyCaseNo}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">保司code：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="insCode" id="insCode"
                                               value="${pushLogVo.insCode}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">案件类型：</label>

                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="type" id="type">
                                            <option value="">-请选择-</option>
                                            <option value=1 <#if pushLogVo.type==1> selected </#if>>财产险</option>
                                            <option value=2 <#if pushLogVo.type==2> selected </#if>>意健险</option>
                                            <option value=3 <#if pushLogVo.type==3> selected </#if>>超赔险</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">推送方向：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="direction" id="direction">
                                            <option value="">-请选择-</option>
                                            <option value=1 <#if pushLogVo.direction==1> selected </#if>>我司推送</option>
                                            <option value=2 <#if pushLogVo.direction==2> selected </#if>>保司回推</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">状态：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">

                                        <select class="form-control" name="status" id="status">
                                            <option value="">-请选择-</option>
                                            <#if pushStatusMap?? && (pushStatusMap.keySet()?size>0) >
                                                <#list pushStatusMap.keySet() as key>
                                                    <option value="${key}" <#if key == pushLogVo.status> selected </#if> >${pushStatusMap.get(key)}</option>
                                                </#list>
                                            </#if>
                                        </select>
                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;">
                                    <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;">查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th width="12%">平台案件号</th>
                        <th width="10%">保司案件号</th>
                        <th width="5%">案件类型</th>
                        <th width="5%">推送方向</th>
                        <th width="15%">请求数据</th>
                        <th width="10%">返回数据</th>
                        <th width="5%">赔付金额</th>
                        <th width="5%">支付金额</th>
                        <th width="5%">备注</th>
                        <th width="7%">状态</th>
                        <th width="10%">创建时间</th>
                        <th width="8%">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as vo>
                        <tr>
                            <td title="${vo.claimCaseNo}">${vo.claimCaseNo}</td>
                            <td title="${vo.insuranceCompanyCaseNo}">${vo.insuranceCompanyCaseNo}</td>
                            <td title="${typeMap.get(vo.type)!'--'}">${typeMap.get(vo.type)!'--'}</td>
                            <td title="">
                                <#if vo.direction == 1 >
                                    我司推送
                                <#else>
                                    保司回推
                                </#if>
                            </td>
                            <td class="td-overflow" title='${vo.reqData!'--'}'>${vo.reqData!'--'}</td>
                            <td class="td-overflow" title='${vo.resData!'--'}'>${vo.resData!'--'}</td>
                            <td title="${vo.approveFee!'--'}">${vo.approveFee!'--'}</td>
                            <td title="${vo.payFee!'--'}">${vo.payFee!'--'}</td>
                            <td title="${vo.remark!'--'}">${vo.remark!'--'}</td>
                            <td title="${pushStatusMap.get(vo.status)!'--'}">${pushStatusMap.get(vo.status)!'--'} </td>
                            <td title="${vo.createTime?string('yyyy-MM-dd HH:mm:ss')}">${vo.createTime?string('yyyy-MM-dd HH:mm:ss')} </td>
                            <#--功能-->
                            <td>
                                <a onclick="caseDetail('${vo.claimCaseId}')">查看详情</a>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>