<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>产品展示</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <link href="${ctx}/metronic/global/plugins/jquery-nestable/jquery.nestable.css" rel="stylesheet" type="text/css" />
    <script src="${ctx}/metronic/global/plugins/jquery-nestable/jquery.nestable.js" type="text/javascript"></script>
    <script type="text/javascript">

        var curFiles = [];

        var uploadAttachType;

        $(document).ready(function () {


            // activate Nestable for list 1
            $('#nestable_list_1').nestable({
                group: 1,
                maxDepth: 1
            }).on("change", function () {
                $.each($(".deleteF"), function (index, obj) {
                    $(this).remove();
                });
                $.each($(".dd-handle"), function (index, obj) {
                    $(this).parent().after(`<span class="glyphicon glyphicon-remove deleteF"></span>`);
                });
            });

            $("body").on("change", "#files", function () {
                var _file = this.files;
                // 存储更新所选文件
                console.log(_file);

                if (_file.length > 0) {
                    for (let i = 0; i < _file.length; i++) {
                        var reader = new FileReader();
                        let fileName = _file[i].name;
                        reader.readAsDataURL(_file[i]);
                        reader.onload = function () {
                            let imgURl = this.result;

                            $("#" + uploadAttachType + "-addAttach").before(`
                        <li class="dd-item" newAttach="1" attachFileNmae="` + fileName + `">
                            <div class="dd-handle">
                                <img  src="` + imgURl + `" onerror="javascript:this.src='/a/job_done.png'">
                            </div>
                        </li>
                        <span class="glyphicon glyphicon-remove deleteF"></span>`);
                        }
                    }
                }

                if (_file && _file.length) {
                    Array.prototype.push.apply(curFiles, _file);
                }

                $("#files").val('');
            });


            //删除文件
            $('body').on('click', '.deleteF', function (e) {
                e.preventDefault();
                e.stopPropagation();

                let parentDdItem = $(this).prev();
                let newAttach = parentDdItem.attr("newAttach");
                if (newAttach == "1") {
                    let name = parentDdItem.attr("attachFileNmae");
                    console.log(name);
                    // 去除该文件
                    var index = -1;
                    for (let i = 0; i < curFiles.length; i++) {
                        let file = curFiles[i];

                        if (file.name == name) {
                            console.log("进入");
                            index = i;
                            break;
                        }
                    }
                    console.log(index);
                    if (index != -1) {
                        curFiles.splice(index, 1)
                        console.log(curFiles);
                        parentDdItem.remove();
                        $(this).remove();
                    }
                } else {
                    parentDdItem.addClass("detelAttach");
                    parentDdItem.attr("isDelete", "1");
                    $(this).remove();
                }
            });
        });

        function uploadImage(imgaeType) {
            uploadAttachType = imgaeType;
            $("#files").click();
        }

        function closeAttachList() {
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index); //再执行关闭
        }

        function submitAttachList() {
            //是否删除，id，type，file
            let sendData = [];
            let initData = $(".dd-handle");
            let newAttachNum = 0;

            let fileMap = {};
            for (let i = 0; i < curFiles.length; i++) {
                let file = curFiles[i];
                fileMap[file.name] = file;
            }

            let errorMsg = "";
            $.each(initData, function (index, obj) {
                let ddItem = $(this).parent();
                let isNewAttach = ddItem.attr("newAttach");
                let attachType = ddItem.closest("div[name='attachTypeArea']").attr("attach-type");
                console.log(attachType);
                if (isNewAttach == '1') {
                    let attachFileNmae = ddItem.attr("attachFileNmae");
                    newAttachNum++;
                    if (!(attachFileNmae in fileMap)) {
                        errorMsg = "文件上传错误！！！";
                    } else {
                        sendData.push({
                            "isDelete": "0",
                            "id": "",
                            "attachType": attachType,
                            "fileName": attachFileNmae
                        })
                    }
                } else {
                    let isDelete = ddItem.attr("isDelete");
                    let id = ddItem.attr("id");
                    if (isDelete == "1") {
                        //判断老影像是否删除
                        sendData.push({
                            "isDelete": "1",
                            "id": id,
                            "attachType": attachType
                        })
                    } else {
                        //判断未删除的老影像是否修改的影像类型
                        let oldAttahType = ddItem.attr("oldAttahType");
                        if (oldAttahType != attachType) {
                            sendData.push({
                                "isDelete": "0",
                                "id": id,
                                "attachType": attachType
                            })
                        }
                    }
                }
            });

            console.log(typeof  JSON.stringify(sendData));
            console.log(JSON.stringify(sendData));
            if(errorMsg!=""){
                layer.msg(errorMsg,{icon: 2,time: 200});
                return;
            }

            var formData = new FormData();
            // for(let i=0;i<sendData.length;i++){
            //     formData.append("sendData",sendData[i]);
            // }
            formData.append("sendData",JSON.stringify(sendData));
            for (let i = 0; i < curFiles.length; i++) {
                formData.append("files[]",curFiles[i]);
            }
            let claimCaseId = `${claimCaseId}`;
            formData.append("claimCaseId",claimCaseId);
            $.ajax({
                url: "${ctx}/insuranceCaseController/modifyAttach",
                type: 'POST',
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                data:formData,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg(result.msg, {
                            icon: 1,
                            time: 2000,
                            offset: '1500px'
                        },function () {
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 5000,
                            offset: '1500px'
                        });
                    }
                },
                error: function (data) {
                    $("#confirmBtn").prop('disabled', false);
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

    </script>
    <style type="text/css">

        #eaitAttachHtml{
            height: 700px !important;
            overflow: scroll;
        }

        #eaitAttachHtml::-webkit-scrollbar {
            display: none; /* Chrome Safari */
        }

        #nestable_list_1 .dd-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            flex-grow: 0;
        }

        #nestable_list_1 .dd-item {
            padding: 1% 1%;
            width: 16.6%;
            height: 150px !important;
        }

        #nestable_list_1 .dd-handle {
            height: 100%;
        }

        #nestable_list_1 img {
            width: 100%;
            height: 100%;
        }

        .portlet-body {
            padding: 0px 5%;
        }

        .deleteF {
            color: #CA0000;
            position: relative;
            top: 5px;
            right: 3px;
            z-index: 9999999999
        }

        .detelAttach {
            display: none;
        }
    </style>
</head>
<body>
<!-- BEGIN PAGE BASE CONTENT -->
<div class="row" id="eaitAttachHtml">
    <div class="col-sm-12">
        <!-- BEGIN EXAMPLE TABLE PORTLET-->
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="dd" id="nestable_list_1">
                            <#if imgInfoMap?exists>
                                <#list imgInfoMap.keySet() as key>
                                    <div name="attachTypeArea" attach-type="${key}">
                                        <span style="width: 100%">${imgInfoMap.get(key)}</span>
                                        <ol class="dd-list">
                                            <#list attachCollect.get(key) as attach>
                                                <li class="dd-item" newAttach="0" oldAttahType="${key}"
                                                    id="${attach.id}">
                                                    <div class="dd-handle">
                                                        <img src="${attach.fileObjectId}"
                                                             onerror="javascript:this.src='/a/job_done.png'">
                                                        <#--                                                        <button class="btn btn-danger deleteF">删除</button>-->
                                                    </div>
                                                </li>
                                                <span class="glyphicon glyphicon-remove deleteF"></span>
                                            </#list>
                                            <div class="dd-item" id="${key}-addAttach">
                                                <img src="${attach.fileObjectId}"
                                                     onerror="javascript:this.src='/a/addImgIcon.png'"
                                                     onclick="uploadImage('${key}')">
                                            </div>
                                        </ol>
                                    </div>
                                </#list>
                            </#if>
                            <#--<#if attachCollect.get("未知")??>
                                <span style="width: 100%">未知</span>
                                <ol class="dd-list">
                                    <#list attachCollect.get("未知") as attach>
                                        <li class="dd-item" newAttach="0" oldAttahType="未知"
                                            id="${attach.id}">
                                            <div class="dd-handle">
                                                <img src="${attach.fileObjectId}"
                                                     onerror="javascript:this.src='/a/job_done.png'">
                                                &lt;#&ndash;                                                        <button class="btn btn-danger deleteF">删除</button>&ndash;&gt;
                                            </div>
                                        </li>
                                        <span class="glyphicon glyphicon-remove deleteF"></span>
                                    </#list>
                                </ol>
                            </#if>-->
                        </div>
                    </div>
                    <input multiple="multiple" accept="image/*" type="file" id="files" name="file" style="display:none">
                </div>

                <div class="row pull-right" style="margin: 40px;margin-bottom: 20px;">
                    <button class="btn" style="background-color: #1676FF;color: white"
                            onclick="submitAttachList()">确认
                    </button>
                    <button class="btn" style="background-color: #1676FF;color: white"
                            onclick="closeAttachList()">取消
                    </button>
                </div>
            </div>
        </div>
        <!-- END EXAMPLE TABLE PORTLET-->
    </div>
</div>
<!-- END PAGE BASE CONTENT -->
</body>
</html>