<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>查看估损金额</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link rel="stylesheet" type="text/css" href="${ctx}/static/static/css/claimAttachVerifyV2.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/static/plugins/adGallery/jquery.ad-gallery.css">

    <script type="text/javascript" src="${ctx}/static/plugins/adGallery/jquery.ad-gallery.js?rand=995"></script>
    <script type="text/javascript" src="${ctx}/static/plugins/adGallery/e-smart-zoom-jquery.js?rand=997"></script>
    <script type="text/javascript" src="${ctx}/static/plugins/adGallery/js2.js?rand=998"></script>
    <script type="text/javascript" src="${ctx}/static/static/js/claimAttachVerifyV2.js"></script>
    <script type="text/javascript">

        function closeModifyAppraisalAmount() {
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index); //再执行关闭
        }

        let oldAppraislAmount = new Map();

        let oldClaimCaseSubjects = [];

        $(document).ready(function () {
            <#if claimCaseObjectList?? && (claimCaseObjectList?size>0)>
            <#list claimCaseObjectList as subject>
                oldAppraislAmount.set('${subject.id}',"${subject.estimatedOverallLoss!'0'}");
                oldClaimCaseSubjects.push({"subjectId":"${subject.id}","subjectName":"${subject.name}","appraisalAmount":"${subject.estimatedOverallLoss!'0'}"});
            </#list>
            </#if>

            $("input[name=subjectMoney]").on('blur',function () {
                let isNotChange = true;
                $.each($("input[name=subjectMoney]"), function (index, obj) {
                    let subjectId = $(this).attr("subjectId");
                    let money = $(this).val();
                    if(oldAppraislAmount.get(subjectId)!= money){
                        isNotChange = false;
                    }
                });
                if(isNotChange){
                    $("#submitModifyAppraisalAmount").attr("disabled","");
                }else {
                    $("#submitModifyAppraisalAmount").removeAttr("disabled");
                }
            });
        });

        function submitModifyAppraisalAmount() {
            let subjectData = [];

            let reason = $.trim($("#logTextArea").val());
            if(reason == undefined || reason == ''){
                layer.alert("修改原因不能为空！！");
                return;
            }
            let sumMoney = 0;
            $.each($("input[name=subjectMoney]"), function (index, obj) {
                let subjectId = $(this).attr("subjectId");
                let subjectName = $(this).attr("subjectName");
                let money = $(this).val();
                subjectData.push({"subjectId":subjectId,"subjectName":subjectName,"appraisalAmount":money});
            });
            console.log(JSON.stringify(subjectData));
            var formData = new FormData();
            formData.append("jsonStr", JSON.stringify(subjectData));
            formData.append("oldJsonStr", JSON.stringify(oldClaimCaseSubjects));
            formData.append("claimCaseId","${claimCase.id}");
            formData.append("reason",reason);
            $("#submitModifyAppraisalAmount").attr("disabled","");
            $.ajax({
                url: "${ctx}/insuranceCaseController/modifyObjectAppraisalAmountAction",
                type: 'POST',
                async: true,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == "0") {
                        layer.msg("修改成功总估损金额为："+result.msg, {
                            icon: 1,
                            time: 2000
                        },function () {
                            parent.changeObjectAppraisalAmount(result.msg);
                            closeModifyAppraisalAmount();
                        });
                    } else {
                        layer.msg(result.msg, {
                            icon: 2,
                            time: 2000
                        });
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });

        }
    </script>
    <style>
        body {
            background: #FFF none repeat scroll 0 0 !important;
            color: #34495e;
            font-size: 14px;
            margin: 0 !important;
            padding: 0 !important;
            font-family: "Open Sans", sans-serif;
            line-height: 1.42857;
        }


        .row {
            margin-left: -15px;
        }

    </style>
</head>
<body id="qc-Body">
<div class="container-fluid">
    <div class="row" style="padding-top: 30px;padding-bottom: 30px;margin: 0px 0px">
        <div class="col-sm-8">
            <#if claimCaseObjectList?? && (claimCaseObjectList?size>0)>
                <#list claimCaseObjectList as subject>
                    <div class="col-sm-12 " style="display: flex;margin-bottom: 10px">
                        <#--                        修改获取字段-->
                        <label class="col-sm-3"
                               style="padding-right: 0;line-height: 34px">${subject.name}</label>
                        <div class="col-sm-9" style="display: flex">
                            <input class="form-control" <#if claimCase.reportCaseStatus != 2 || claimCase.status?contains("-1") || claimCase.status=="abx20" || claimCase.status?contains("aex") || subject.status == "BAX99">disabled</#if> subjectId="${subject.id}" subjectName="${subject.name}" type="text" name="subjectMoney" value="${subject.estimatedApprovedMoney!'0'}"><span
                                    style="line-height: 34px">（元）</span>
                        </div>
                    </div>
                </#list>


            <div class="col-sm-12 " style="display: flex;margin-bottom: 10px">
                <label class="col-sm-3"
                       style="padding-right: 0;line-height: 34px">修改原因：</label>
                <div class="col-sm-9" style="display: flex">
                    <textarea class="form-control" id="logTextArea" rows="5" placeholder="请输入修改原因"></textarea>
                </div>
            </div>

            <div class="col-sm-12 " style="display: flex;margin-bottom: 10px">
                <label class="col-sm-3"
                       style="padding-right: 0;line-height: 34px">估损金额：</label>
                <div class="col-sm-9" style="display: flex">
                    <input type="text" class="form-control" value="${sumAppraisalAmount}" disabled><span style="line-height: 34px">（元）</span>
                </div>
            </div>

            <#else >
                <h3 style="text-align: center">暂无数据！！！</h3>
            </#if>

            <div class="col-sm-12">
                <#if claimCase.reportCaseStatus == 2 && !claimCase.status?contains("-1") && claimCase.status!="abx20" && !claimCase.status?contains("aex")>
                    <button class="btn " style="background-color: #1676FF;color: white" id="submitModifyAppraisalAmount"
                            <#if claimCaseObjectList?? && (claimCaseObjectList?size>0)>onclick="submitModifyAppraisalAmount()" disabled <#else >disabled</#if>>确认
                    </button>
                </#if>
                <button class="btn " style="background-color: #1676FF;color: white"
                        onclick="closeModifyAppraisalAmount()">取消
                </button>
            </div>
        </div>
    </div>
</div>

</body>
</html>