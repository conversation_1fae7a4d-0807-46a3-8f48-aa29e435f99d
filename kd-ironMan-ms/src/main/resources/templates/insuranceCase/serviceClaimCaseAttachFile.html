<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>影像查看</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link rel="stylesheet" type="text/css" href="${ctx}/static/css/claimAttachVerifyV2.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/plugins/adGallery/jquery.ad-gallery.css">

    <script type="text/javascript" src="${ctx}/plugins/adGallery/jquery.ad-gallery.js?rand=995"></script>
    <script type="text/javascript" src="${ctx}/plugins/adGallery/e-smart-zoom-jquery.js?rand=997"></script>
    <script type="text/javascript" src="${ctx}/plugins/adGallery/js2.js?rand=998"></script>
    <script type="text/javascript" src="${ctx}/static/js/claimAttachVerifyV2.js"></script>
    <script type="text/javascript">
        var contentTypeObj = {};
        <#if contentTypeMap?? && (contentTypeMap.keySet()?size>0) >
        <#list contentTypeMap.keySet() as key>
        contentTypeObj["${key}"] = "${contentTypeMap.get(key)}";
        </#list>
        </#if>
        $(document).ready(function () {
            document.onkeydown = function(w) {
                w = (w) ? w : ((window.event) ? window.event : "");
                var key = w.keyCode ? w.keyCode : w.which;
                // w=87;s=83;a=65;d=68;q=81;e=69;<-=37;->=39
                if (key == 37) {
                    // 上一张
                    galleries[0].prevImage(null);
                    changeAttachInfo(false);
                }
                if (key == 39) {
                    // 下一张
                    galleries[0].nextImage(null);
                    changeAttachInfo(false);
                }
                if (key == 65) {
                    // 上一张
                    galleries[0].prevImage(null);
                    changeAttachInfo(false);
                }
                if (key == 68) {
                    // 下一张
                    galleries[0].nextImage(null);
                    changeAttachInfo(false);
                }
                if (key == 81) {
                    // 左旋转
                    $("#rotateLeft90").click();
                }
                if (key == 69) {
                    // 右旋转
                    $("#rotateRight90").click();
                }
                if (key == 87) {
                    // 放大
                    $("#zoomInButton").click();
                }
                if (key == 83) {
                    // 缩小
                    $("#zoomOutButton").click();
                }
            };


            $(".ad-pre").click(function () {
                changeAttachInfo(false);
            });

            $(".ad-next").click(function () {
                changeAttachInfo(false);
            });

            changeAttachInfo(true);

            function formatRepo(repo) {
                if(repo.id != undefined){
                    var markup = "<div class='select2-result-repository clearfix specClass4Select2' >" +
                        "<div class='select2-result-repository__title'>" + repo.name + "</div>";
                    return markup;
                }
                return "";
            }

            function formatRepoSelection(repo) {
                if(repo.id){
                    $(".attachInfo").each(function () {
                        var $this = $(this);
                        if(!$this.is(':hidden')){
                            $this.find(".select2-selection__rendered").attr("data-baiduContentTypeId", repo.id);
                            $this.find(".select2-selection__rendered").attr("data-change", "true");
                        }
                    });
                    return repo.name;
                }
                return "";
            }

            $(".js-data-example-ajax").select2({
                width: "off",
                ajax: {
                    type: 'POST',
                    url: "${ctx}/attachInfoController/baiduTypeList",
                    dataType: 'json',
                    delay: 250,
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    data: function (params) {
                        return {
                            name: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.baiduTypeList
                        };
                    },
                },
                escapeMarkup: function (markup) {
                    return markup;
                }, // let our custom formatter work
                placeholder: "影像类型",
                minimumInputLength: 1,
                templateResult: formatRepo,
                templateSelection: formatRepoSelection
            });

            <#list claimOrderAttachList as attach>
            $("#select2-${attach_index}select2-container").text("${attach.baiduContentTypeStr!''}");
            $("#select2-${attach_index}select2-container").attr("data-attachId", "${attach.id}");
            $("#select2-${attach_index}select2-container").attr("data-baiduContentTypeId", "${attach.baiduContentTypeId}");
            </#list>

            $(".ad-thumb-list li a").click(function () {
                var fileId = $(".ad-image-wrapper .ad-image img").attr("src");
                $(".attachInfo").each(function () {
                    var url = $(this).attr("data-url");
                    if(fileId == url){
                        $(this).siblings().hide();
                        $(this).show();
                    }
                });
            });
        });

        function changeAttachInfo(firstLoad) {
            if(firstLoad){
                $("#attachInfoDiv .attachInfo:eq(0)").show();
            }else{
                var fileId = $(".ad-image-wrapper .ad-image img").attr("src");
                $(".attachInfo").each(function () {
                    var url = $(this).attr("data-url");
                    if(fileId == url){
                        $(this).siblings().hide();
                        $(this).show();
                    }
                });
            }
        }


    </script>
    <style>
        body {
            background: #FFF none repeat scroll 0 0 !important;
            color: #3165D2;
            font-size: 14px;
            margin: 0 !important;
            padding: 0 !important;
            font-family: "Open Sans", sans-serif;
            line-height: 1.42857;
        }

        #gallery {
            padding: 0 30px;
        }

        .kp-right {
            background: none repeat scroll 0 0;
            width: 25%;
            padding: 30px;
        }

        .kp-left {
            width: 100%;
            height: 85% !important;
        }

        .input_radio {
            line-height: 3;
        }

        /*.form{
            padding: 50px !important;
        }*/
        .row {
            margin-left: -15px;
        }

        #bootn {
            padding-left: 40px;
        }

        .hideCheck {
            display: none;
        }

        .ad-gallery .ad-nav .ad-thumbs {
            overflow: hidden;
            width: 100%;
            margin-bottom: 15px;
        }

        .ad-gallery {
            width: 100%;
            height: 85%;
        }

        a img {
            display: block;
            height: 65px;
            border: 3px solid #CCC;
        }

        .ad-gallery .ad-image-wrapper {
            height: 80%;
            min-height:500px !important;
        }

        /*html,body{
            height: 100% !important;
        }*/
    </style>
</head>
<body id="qc-Body">
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12" style="padding: 0; background: none;">
            <div class="kp-left">
                <div id="gallery" class="ad-gallery">
                    <input type="hidden" id="rotateLeft90"/>
                    <input type="hidden" id="rotateRight90"/>
                    <input type="hidden" id="zoomInButton"/>
                    <input type="hidden" id="zoomOutButton"/>
                    <div class="ad-image-wrapper" style="height: 20%;"></div>
                    <#--<div class="ad-controls"></div>-->
                    <div class="ad-nav">
                        <div class="ad-thumbs">
                            <ul class="ad-thumb-list">
                                <ul class="ad-thumb-list">
                                    <#list claimCaseAttachList as claimCaseAttach>
                                        <li><a href="${aliossPublicUrl}${claimCaseAttach.fileObjectId}">
                                                <img title="${claimCaseAttach.fileName}" src="${claimCaseAttach.fileObjectId}" onerror="javascript:this.src='/a/job_done.png'"/>
                                            </a></li>
                                        <#--<li><a href="${aliossPublicUrl}kd_aquarius/prdAggUpgradeFile/0c43fb6e830e44a6aee85d0bd1a497be">-->
                                                <#--<img title="${claimOrderAttach.fileName}" src="${aliossPublicUrl}kd_aquarius/prdAggUpgradeFile/0c43fb6e830e44a6aee85d0bd1a497be" />-->
                                            <#--</a></li>-->
                                    </#list>
                                </ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>