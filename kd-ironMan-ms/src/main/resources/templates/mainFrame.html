<!DOCTYPE html>
<!-- 
Template Name: Metronic - Responsive Admin Dashboard Template build with Twitter Bootstrap 3.3.5
Version: 4.5.2
Author: KeenThemes
Website: http://www.keenthemes.com/
Contact: <EMAIL>
Follow: www.twitter.com/keenthemes
Like: www.facebook.com/keenthemes
Purchase: http://themeforest.net/item/metronic-responsive-admin-dashboard-template/4021469?ref=keenthemes
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
    <!--<![endif]-->
    <!-- BEGIN HEAD -->

    <head>
        <meta charset="utf-8" />
        <title>代驾业务</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta content="width=device-width, initial-scale=1" name="viewport" />
        <meta content="" name="description" />
        <meta content="" name="author" />

        <#include "/common/cssResource.html">
        <#include "/common/jsResource.html">
        <!-- BEGIN THEME LAYOUT SCRIPTS -->
        <script src="${ctx}/metronic/layouts/layout4/scripts/layout2.js" type="text/javascript"></script>
        <!-- END THEME LAYOUT SCRIPTS -->

        <script type="text/javascript">
            // document.domain = "localhost"; //此句不可删除，解决跨域问题
			function iFrameHeight() {
				var ifm = document.getElementById("contentFrame");
                ifm.contentWindow.postMessage(window.location.protocol + "//"+window.location.host+"${ctx}","*");

				var winH = $(window).height();
				ifm.height = winH;
				var ifmHeight = ifm.contentWindow.document.documentElement.scrollHeight;
				if (ifmHeight && ifmHeight > winH) {
					ifm.height = ifmHeight;
				}
			}

            document.domain="ldschuzhou.com"
            //解决 跨域ifream  计算高度问题
            window.addEventListener('message',function(e){
                var ifmHeight= e.data;
                var ifm = document.getElementById("contentFrame");
                var winH = $(window).height();
                ifm.height = winH;
                if (ifmHeight > winH) {
                    ifm.height = ifmHeight;
                }
            });
			$(document).ready(function () {
			    var src = "";
                if(!window.localStorage){
                    var strCookie=document.cookie;
                    var arrCookie=strCookie.split("; ");
                    for(var i=0;i<arrCookie.length;i++){
                        var arr=arrCookie[i].split("=");
                        //找到名称为userId的cookie，并返回它的值
                        if("src"==arr[0]){
                            src=unescape(arr[1]);
                            break;
                        }
                    }
                    //获取当前时间
                    var date=new Date();
                    //将date设置为过去的时间
                    date.setTime(date.getTime()-1);
                    //将userId这个cookie删除
                    document.cookie="src="+"; expire="+date.toGMTString()+";path=/mainFrame";
                }else{
                    src = window.localStorage.getItem("src");
                    window.localStorage.removeItem("src");
                }
                if(src){
                    $("a[href='"+src+"']")[0].click();
                    $(".page-sidebar-menu .parentNav").removeClass("open active");
                    $("a[href='"+src+"']").parent().parent().closest("li.nav-item").addClass("open active");
                }else{
                    $(".page-sidebar-menu .parentNav:first-child>a ")[0].click();
                }

                //==========================START 返回页面顶部=========================================
                var offset = 100;   //向下100px展示
                var duration = 500;  //动画效果0.5s

                if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {  // ios supported
                    $(window).bind("touchend touchcancel touchleave", function(e){
                        if ($(this).scrollTop() > offset) {
                            $('.scroll-to-top').fadeIn(duration);
                        } else {
                            $('.scroll-to-top').fadeOut(duration);
                        }
                    });
                } else {  // general
                    $(window).scroll(function() {
                        if ($(this).scrollTop() > offset) {
                            $('.scroll-to-top').fadeIn(duration);
                        } else {
                            $('.scroll-to-top').fadeOut(duration);
                        }
                    });
                }

                $('.scroll-to-top').click(function(e) {
                    e.preventDefault();
                    $('html, body').animate({scrollTop: 0}, duration);
                    return false;
                });
                //==========================END 返回页面顶部=========================================
            })

            //页面元素定位
            function IframeAnimate(top) {
                $('html, body').animate({scrollTop: top}, 500);
                return false;
            }

		</script>
        <style type="text/css">
            .menu-toggler {

                background-image: url("./metronic/layouts/layout/img/sidebar_toggler_icon_grey.png")!important;
            }

        </style>
	</head>
    <!-- END HEAD -->

    <body class="page-container-bg-solid page-header-fixed page-sidebar-closed-hide-logo page-sidebar-fixed" style="background-color:#3165d2;">
        <!-- BEGIN HEADER -->
        <div id="header" class="page-header navbar navbar-fixed-top" style="background-color: #1E50B8 !important;">
            <!-- BEGIN HEADER INNER -->
            <div class="page-header-inner ">
                <!-- BEGIN LOGO -->
                <div class="page-logo">
                    <a href="mainFrame" style="align-items: center;line-height:46px;"><span style="color: white;vertical-align: bottom">代驾业务</span> </a>
                    <div class="menu-toggler sidebar-toggler">
                        <!-- DOC: Remove the above "hide" to enable the sidebar toggler button on header -->
                    </div>
                </div>
                <!-- END LOGO -->
                <!-- BEGIN RESPONSIVE MENU TOGGLER -->
                <a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse"> </a>
                <!-- END RESPONSIVE MENU TOGGLER -->
                <!-- BEGIN PAGE TOP -->
                <div class="page-top">
                    <!-- BEGIN TOP NAVIGATION MENU -->
                    <div class="top-menu">
                        <ul class="nav navbar-nav pull-right">
                            <li class="separator hide"> </li>
                            <!-- BEGIN NOTIFICATION DROPDOWN -->
                            <!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
                            <!--
                            <li class="dropdown dropdown-extended dropdown-notification dropdown-dark" id="header_notification_bar">
                                <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                                    <i class="icon-bell"></i>
                                    <span class="badge badge-success"> 7 </span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li class="external">
                                        <h3>
                                            <span class="bold">12 pending</span> notifications</h3>
                                        <a href="page_user_profile_1.html">view all</a>
                                    </li>
                                    <li>
                                        <ul class="dropdown-menu-list scroller" style="height: 250px;" data-handle-color="#637283">
                                            <li>
                                                <a href="javascript:;">
                                                    <span class="time">just now</span>
                                                    <span class="details">
                                                        <span class="label label-sm label-icon label-success">
                                                            <i class="fa fa-plus"></i>
                                                        </span> New user registered. </span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:;">
                                                    <span class="time">3 mins</span>
                                                    <span class="details">
                                                        <span class="label label-sm label-icon label-danger">
                                                            <i class="fa fa-bolt"></i>
                                                        </span> Server #12 overloaded. </span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                             -->
                            <!-- END NOTIFICATION DROPDOWN -->
                            <li class="separator hide"> </li>
                            <!-- BEGIN INBOX DROPDOWN -->
                            <!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
                            <!--
                            <li class="dropdown dropdown-extended dropdown-inbox dropdown-dark" id="header_inbox_bar">
                                <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                                    <i class="icon-envelope-open"></i>
                                    <span class="badge badge-danger"> 4 </span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li class="external">
                                        <h3>You have
                                            <span class="bold">7 New</span> Messages</h3>
                                        <a href="app_inbox.html">view all</a>
                                    </li>
                                    <li>
                                        <ul class="dropdown-menu-list scroller" style="height: 275px;" data-handle-color="#637283">
                                            <li>
                                                <a href="#">
                                                    <span class="photo">
                                                        <img src="${ctx}/metronic/layouts/layout3/img/avatar2.jpg" class="img-circle" alt=""> </span>
                                                    <span class="subject">
                                                        <span class="from"> Lisa Wong </span>
                                                        <span class="time">Just Now </span>
                                                    </span>
                                                    <span class="message"> Vivamus sed auctor nibh congue nibh. auctor nibh auctor nibh. </span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <span class="photo">
                                                        <img src="${ctx}/metronic/layouts/layout3/img/avatar3.jpg" class="img-circle" alt=""> </span>
                                                    <span class="subject">
                                                        <span class="from"> Richard Doe </span>
                                                        <span class="time">16 mins </span>
                                                    </span>
                                                    <span class="message"> Vivamus sed congue nibh auctor nibh congue nibh. auctor nibh auctor nibh. </span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                            -->
                            <!-- END INBOX DROPDOWN -->
                            <li class="separator hide"> </li>
                            <!-- BEGIN TODO DROPDOWN -->
                            <!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
                            <!--
                            <li class="dropdown dropdown-extended dropdown-tasks dropdown-dark" id="header_task_bar">
                                <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                                    <i class="icon-calendar"></i>
                                    <span class="badge badge-primary"> 3 </span>
                                </a>
                                <ul class="dropdown-menu extended tasks">
                                    <li class="external">
                                        <h3>You have
                                            <span class="bold">12 pending</span> tasks</h3>
                                        <a href="?p=page_todo_2">view all</a>
                                    </li>
                                    <li>
                                        <ul class="dropdown-menu-list scroller" style="height: 275px;" data-handle-color="#637283">
                                            <li>
                                                <a href="javascript:;">
                                                    <span class="task">
                                                        <span class="desc">New release v1.2 </span>
                                                        <span class="percent">30%</span>
                                                    </span>
                                                    <span class="progress">
                                                        <span style="width: 40%;" class="progress-bar progress-bar-success" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="sr-only">40% Complete</span>
                                                        </span>
                                                    </span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:;">
                                                    <span class="task">
                                                        <span class="desc">Application deployment</span>
                                                        <span class="percent">65%</span>
                                                    </span>
                                                    <span class="progress">
                                                        <span style="width: 65%;" class="progress-bar progress-bar-danger" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="sr-only">65% Complete</span>
                                                        </span>
                                                    </span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                             -->
                            <!-- END TODO DROPDOWN -->
                            <!-- BEGIN USER LOGIN DROPDOWN -->
                            <!-- DOC: Apply "dropdown-dark" class after below "dropdown-extended" to change the dropdown styte -->
                            <li class="dropdown dropdown-user dropdown-dark">
                                <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                                    <span class="username username-hide-on-mobile"> ${SESSION_MANAGER.realName} </span>
                                    <!-- DOC: Do not remove below empty space(&nbsp;) as its purposely used -->
                                    <img alt="" class="img-circle" src="${ctx}/metronic/layouts/layout4/img/avatar9.jpg" />
                                </a>
                                <ul class="dropdown-menu dropdown-menu-default">
                                	<!--
                                    <li>
                                        <a href="page_user_profile_1.html">
                                            <i class="icon-user"></i> 个人信息 </a>
                                    </li>
                                    <li class="divider"> </li>
                                    <li>
                                        <a href="page_user_lock_1.html">
                                            <i class="icon-lock"></i> Lock Screen </a>
                                    </li>
                                     -->
                                    <li>
                                        <a href="${ctx}/oauthController/modification"><i class="icon-key"></i> 修改密码 </a>
                                        <a href="${ctx}/oauthController/logout">
                                            <i class="icon-key"></i> 退出 </a>
                                    </li>
                                </ul>
                            </li>
                            <!-- END USER LOGIN DROPDOWN -->
                        </ul>
                    </div>
                    <!-- END TOP NAVIGATION MENU -->
                </div>
                <!-- END PAGE TOP -->
            </div>
            <!-- END HEADER INNER -->
        </div>
        <!-- END HEADER -->
        <!-- BEGIN HEADER & CONTENT DIVIDER -->
        <div class="clearfix"> </div>
        <!-- END HEADER & CONTENT DIVIDER -->
        <!-- BEGIN CONTAINER -->
        <div class="page-container">
        	<#include "/common/sidebar.html">
            <!-- BEGIN CONTENT -->
            <div class="page-content-wrapper" style="background-color: #3165D2!important;">
                <!-- BEGIN CONTENT BODY -->
                <div id="contentDiv" class="page-content">
               		<iframe id="contentFrame" name="contentFrame" src="" scrolling="no" frameborder="no" width="100%" height="100%" ></iframe>
                </div>
                <!-- END CONTENT BODY -->
            </div>
            <!-- END CONTENT -->
        </div>
        <!-- END CONTAINER -->
        <!-- BEGIN FOOTER -->
        <div id="footer" class="page-footer">
            <div class="page-footer-inner"> 2019 ©
            </div>
            <div class="scroll-to-top">
                <i class="icon-arrow-up"></i>
            </div>
        </div>
        <!-- END FOOTER -->

    </body>

</html>