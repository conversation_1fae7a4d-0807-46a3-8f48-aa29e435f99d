<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>分配角色</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<!-- BEGIN PAGE LEVEL PLUGINS -->
<link href="${ctx}/metronic/global/plugins/jquery-multi-select/css/multi-select.css" rel="stylesheet" type="text/css" />
<!-- END PAGE LEVEL PLUGINS -->

<#include "/common/jsResource.html">
<!-- BEGIN PAGE LEVEL PLUGINS -->
<script src="${ctx}/metronic/global/plugins/jquery-multi-select/js/jquery.multi-select.js" type="text/javascript"></script>
<script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->

<script type="text/javascript">

	$(document).ready(function() {
		$("#grant_role_select").multiSelect({
			selectableFooter : "<div class='custom-header'>未授权角色</div>",
			selectionFooter : "<div class='custom-header'>授权角色</div>"
		});
	});

	function grantRole() {
		var roleIds = "";
		$("#grant_role_select option:selected").each(function() {
			roleIds += $(this).val() + ",";
		});
		$.ajax({
			url : "${ctx}/managerController/grantRole",
			type : "POST",
			data : {
				managerId : '${managerId}',
				roleIds : roleIds
			},
			dataType : "json",
			success : function(data) {
				var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
				parent.layer.close(index);
			}
		});
	}
</script>

</head>
<body>
	<div class="portlet light bordered">
		<div class="portlet-body form">
			<!-- BEGIN FORM-->
			<form id="inputForm" class="form-horizontal form-row-seperated">
				<div class="form-body">
					<select multiple="multiple" class="multi-select"
						id="grant_role_select" name="grant_role_select[]">
						<#list roleGrantList as roleGrant>
							<option value="${roleGrant.id}"<#if roleGrant.selected == 1> selected </#if>>${roleGrant.name}</option>
						</#list>
					</select>
				</div>
				<div class="form-actions">
					<div class="row">
						<div class="col-sm-3">
						</div>
						<div class="col-sm-9">
							<button type="button" class="btn green" onclick="grantRole()">提交</button>
							<button type="button" class="btn default" onclick="parent.layer.closeAll();">取消</button>
						</div>
					</div>
				</div>
			</form>
		</div>
		<!-- END FORM-->
	</div>
</body>
</html>