<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
    <!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>用户管理</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<#include "/common/jsResource.html">
<script type="text/javascript">

function page(n,s){
	$("#pageNum").val(n);
	$("#pageSize").val(s);
	$("#searchForm").submit();
	return false;
}

function openNew(){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/managerController/form?operationType=new',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function openEdit(managerId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/managerController/form?id='+managerId+'&operationType=edit',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function grantRolePage(managerId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var openWindowHeight = $(document).height() * 0.8 + "px";
	layer.open({
		type: 2,
		area: [openWindowWidth, openWindowHeight],
		offset : '60px',
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/managerController/grantRolePage?managerId=' + managerId
	});
}

function changeStatus(managerId) {

			$.ajax({
				url : "${ctx}/managerController/changeStatus?id="+managerId,
				type : 'POST',
				async : true,
				cache : false,
				contentType : false,
				processData : false,
				success : function(data) {
					var result = eval("(" + data + ")");
					if (result.ret != "0") {
						layer.msg(result.msg, {
							icon: 2,
							time: 2000,//1秒关闭（如果不配置，默认是3秒）
						}, function (index) {
							layer.close(index);
						});
					}else {
						layer.msg(result.msg, {
							icon: 1,
							time: 2000,//1秒关闭（如果不配置，默认是3秒）
						}, function (index) {
							layer.close(index);
							window.location.reload();
						});
					}

				},
				error : function(data) {
					var result = eval("(" + data + ")");
					alert(result.msg);
				}
			});
}

function resetPassword(managerId, userName, email) {
	layer.confirm("请确认是否需要重置"+userName+"密码，重置后的密码将发送至管理员的邮箱中？", {icon: 3, title: '温馨提示', offset: 'auto'}, function (index) {
		var formData = new FormData();
		formData.append("id", managerId);
		formData.append("userName", userName);
		formData.append("email", email);
		$.ajax({
			url: "${ctx}/managerController/resetPassword",
			type: 'POST',
			data: formData,
			async: true,
			cache: false,
			contentType: false,
			processData: false,
			success: function (data) {
				layer.close(index);
				var result = eval("(" + data + ")");
				if (result.ret != "0") {
					layer.msg(result.msg, {
						icon: 2,
						time: 2000,//1秒关闭（如果不配置，默认是3秒）
					}, function (index1) {
						layer.close(index1);
					});
				}else {
					layer.msg(result.msg, {
						icon: 1,
						time: 2000,//1秒关闭（如果不配置，默认是3秒）
					}, function (index1) {
						layer.close(index1);
					});
				}
			},
			error: function (data) {
				layer.close(index);
				var result = eval("(" + data + ")");
				alert(result.msg);
			}
		});
	});
}

</script>

</head>
<body>
	<!-- BEGIN PAGE BASE CONTENT -->
	<div class="row">
		<div class="col-sm-12">
			<!-- BEGIN EXAMPLE TABLE PORTLET-->
			<div class="portlet light portlet-fit bordered">
				<div class="portlet-title">
					<ul class="page-breadcrumb breadcrumb">
						<li><a>企业服务台</a> <i class="fa fa-circle"></i></li>
						<li><a>系统管理</a> <i class="fa fa-circle"></i></li>
						<li><span class="active">系统人员管理</span></li>
					</ul>
				</div>
				<div class="portlet-body">
					<div class="table-toolbar">
						<div class="row">
							<form id="searchForm" action="${ctx}/managerController/managerList" method="post">
								<input id="pageNum" name="pageNum" type="hidden" value="1" />
								<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}" />

								<div class="col-sm-12">
									<div class="col-sm-4">
										<div class="form-group">
											<label class="control-label col-sm-2" style="padding-right: 0">用户名：</label>
											<div class="col-sm-8" style="padding-left: 0;">
												<input type="text" class="form-control" name="userName"
													   value="${manager.userName}" />
											</div>
										</div>
									</div>
								</div>

								<div class="col-sm-12">
									<div class="btn-group pull-right">
										<button type="submit" class="btn green" >查询</button><div class="col-sm-1"></div>
										<button type="button"  class="btn green" onclick="openNew()">新增</button>
									</div>
								</div>

							</form>


						</div>
					</div>
					<table class="table table-striped table-bordered table-hover table-header-fixed"
						id="managerTable">
						<thead>
							<tr>
								<th>用户名</th>
								<th>真实姓名</th>
								<th>手机</th>
								<th>邮箱</th>
								<th>状态</th>
								<th>创建时间</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list page.list as manager>
								<tr>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${manager.userName}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">${manager.realName}</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${manager.mobile}</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${manager.email}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;"><#if manager.status = 1> 有效 <#else> 无效 </#if></td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${(manager.createTime?string("yyyy-MM-dd HH:mm:ss"))!''}</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">
										<a href="#" onclick="openEdit('${manager.id}')">编辑</a>
										<a href="#" onclick="grantRolePage('${manager.id}')">授权</a>
										<@shiro.hasPermission name="USER_DELETE">
											<a href="${ctx}/managerController/deleteManager?id=${manager.id}" onclick="return confirm('确认要执行 删除 操作吗？', this.href)">删除</a>
										</@shiro.hasPermission>
										<a href="#" onclick="changeStatus('${manager.id}')">切换状态</a>
										<a href="#" onclick="resetPassword('${manager.id}','${manager.userName}','${manager.email}')">重置密码</a>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
				</div>
			</div>
			<!-- END EXAMPLE TABLE PORTLET-->
		</div>
	</div>
	<!-- END PAGE BASE CONTENT -->
	<@sc.pagination page=page />


</body>
</html>