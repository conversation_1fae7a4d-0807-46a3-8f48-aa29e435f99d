<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>错误订单查询</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script type="text/javascript">

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

        });

        /* 推送 */
        function repushData(id, reqUrl) {
            layer.prompt({
                formType: 2,
                value: reqUrl,
                title: '请确认请求地址',
            }, function (value, index, elem) {
                var reqUrl = value;
                var formData = new FormData();
                formData.append("id", id);
                formData.append("reqUrl", reqUrl);
                $.ajax({
                    url: "${ctx}/pushTaskController/rePushData",
                    type: 'POST',
                    data: formData,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == '0') {
                            layer.close(index);
                            alert("推送成功");
                            $("#searchForm").submit();
                        } else {
                            alert(result.msg);
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        function cancelPush(id) {
            var formData = new FormData();
            formData.append("id", id);
            $.ajax({
                url: "${ctx}/pushTaskController/cancelPush",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == '0') {
                        alert("撤销成功");
                        $("#searchForm").submit();
                    } else {
                        alert(result.msg);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        /* 展示数据 */
        function showData(data) {
            layer.open({
                type: 1,
                area: ['800px', '400px'],
                shadeClose: true,
                content: '<div style="padding: 10px; width: 100%; height: 100%;"><textarea style="width: 100%; height: 100%;">' + data + '</textarea></div>'
            });
        }

    </script>
    <style>
        .table-scrollable{
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #e7ecf1;
            margin: 10px 0!important;
            table-layout: fixed;
        }

        .label-hide-overflow{
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body id="qc-Body">
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><a href="#">企业服务台</a> <i class="fa fa-circle"></i></li>
                    <li><a href="#">基础查询</a> <i class="fa fa-circle"></i></li>
                    <li><span class="active">错误订单查询</span></li>
                </ul>
            </div>
            <ul class="nav nav-tabs">
                <li id="tab0"><a data-toggle="tab" onclick="window.location.href='${ctx}/pushTaskController/pushTaskList';"> 全部 </a></li>
                <li id="tab1" class="active"><a data-toggle="tab" onclick="submitForm(0);"> 异常列表 </a></li>
            </ul>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal"
                      action="${ctx}/pushTaskController/pushTaskFailureList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">订单号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="flowNo" id="flowNo"
                                               value="${pushTask.flowNo}"
                                               placeholder="请输入流水号"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">请求地址：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="reqUrl" id="reqUrl"
                                               value="${pushTask.reqUrl}"
                                               placeholder="请输入请求地址"/>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="btn-group pull-right">
                                            <button id="query" type="submit" class="btn green"
                                                    style="margin-bottom: 10px;"
                                                    onclick="submitForm();">查询
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="row">
                    <table class="table table-striped table-scrollable table-hover">
                        <thead>
                        <tr>
                            <th width="10%" class="label-hide-overflow">流水号</th>
                            <th width="20%" class="label-hide-overflow">请求地址</th>
                            <th width="5%">推送类别</th>
                            <th width="20%" class="label-hide-overflow">返回数据</th>
                            <th width="10%">创建时间</th>
                            <th width="15%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list page.list as pushTask>
                        <tr>
                            <td class="label-hide-overflow" title="${pushTask.flowNo}">${pushTask.flowNo}</td>
                            <td class="label-hide-overflow" title="${pushTask.reqUrl}">${pushTask.reqUrl}</td>
                            <td>
                                <#if pushTask.reqType == 0>内部请求
                                <#elseif pushTask.reqType == 1>外部请求
                                </#if>
                            </td>
                            <td class="label-hide-overflow" title="${pushTask.resData?html}">${pushTask.resData}</td>
                            <td>${pushTask.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <#if pushTask.status == 0>
                                    <span onclick='repushData("${pushTask.id}", "${pushTask.reqUrl}")' style="margin-right: 4px; cursor: pointer; color: #337ab7;">推送</span>
                                    <span onclick='cancelPush("${pushTask.id}")' style="margin-right: 4px; cursor: pointer; color: #337ab7;">撤销</span>
                                </#if>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                    </table>
                </div>
                <@sc.pagination page=page />
            </div>
        </div>
    </div>
</div>
</body>
</html>
