<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>订单重新推送</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">

    <script type="text/javascript">

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        $(document).ready(function () {

        });

        /* 推送 */
        function rePushData(id, reqUrl) {
            layer.prompt({
                formType: 2,
                value: reqUrl,
                title: '请确认请求地址',
            }, function(value, index, elem){
                var reqUrl = value;
                var formData = new FormData();
                formData.append("id", id);
                formData.append("reqUrl", reqUrl);
                $.ajax({
                    url: "${ctx}/pushTaskController/rePushData",
                    type: 'POST',
                    data:formData,
                    async: true,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var result = eval("(" + data + ")");
                        if (result.ret == '0'){
                            layer.close(index);
                            alert("推送成功");
                            $("#searchForm").submit();
                        } else {
                            alert(result.msg);
                        }
                    },
                    error: function (data) {
                        var result = eval("(" + data + ")");
                        alert(result.msg);
                    }
                });
            });
        }

        function cancelPush(id) {
            var formData = new FormData();
            formData.append("id", id);
            $.ajax({
                url: "${ctx}/pushTaskController/cancelPush",
                type: 'POST',
                data:formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == '0'){
                        alert("撤销成功");
                        $("#searchForm").submit();
                    } else {
                        alert(result.msg);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        function selectNearestLog(id) {
            var formData = new FormData();
            formData.append("id", id);
            $.ajax({
                url: "${ctx}/pushTaskController/selectNearestLog",
                type: 'POST',
                data: formData,
                async: true,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    var result = eval("(" + data + ")");
                    if (result.ret == '0'){
                        layer.open({
                            type: 1,
                            area: ['800px', '400px'],
                            shadeClose: true,
                            content: '<div style="padding: 10px; width: 100%; height: 100%;"><textarea style="width: 100%; height: 100%;">' + result.data + '</textarea></div>'
                        });
                    } else {
                        alert(result.msg);
                    }
                },
                error: function (data) {
                    var result = eval("(" + data + ")");
                    alert(result.msg);
                }
            });
        }

        /* 展示数据 */
        function showData(data) {
            layer.open({
                type: 1,
                area: ['800px', '400px'],
                shadeClose: true,
                content: '<div style="padding: 10px; width: 100%; height: 100%;"><textarea style="width: 100%; height: 100%;">' + data + '</textarea></div>'
            });
        }

        function pushTaskViewForm(id){
            layer.load();
            window.location.href = "${ctx}/pushTaskController/form?operationType=view&id="+ id +"";
        }
    </script>
    <style>
        .table-scrollable{
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #e7ecf1;
            margin: 10px 0!important;
            table-layout: fixed;
        }

        .label-hide-overflow{
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body id="qc-Body">
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><a href="#">企业服务台</a> <i class="fa fa-circle"></i></li>
                    <li><a href="#">基础查询</a> <i class="fa fa-circle"></i></li>
                    <li><span class="active">订单重新推送</span></li>
                </ul>
            </div>
            <ul class="nav nav-tabs">
                <li id="tab0" class="active"><a data-toggle="tab" onclick="submitForm(0);"> 全部 </a></li>
                <li id="tab1"><a data-toggle="tab" onclick="window.location.href='${ctx}/pushTaskController/pushTaskFailureList'"> 异常列表 </a></li>
            </ul>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal" action="${ctx}/pushTaskController/pushTaskList"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">订单号：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="flowNo" id="flowNo"
                                               value="${pushTask.flowNo}"
                                               placeholder="请输入流水号"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">状态：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="status" id="status">
                                            <option value="">-请选择-</option>
                                            <option value="0" <#if pushTask.status == 0>selected</#if>>待推送
                                            </option>
                                            <option value="1" <#if pushTask.status == 1>selected</#if>>推送成功
                                            </option>
                                            <option value="-1" <#if pushTask.status == -1>selected</#if>>撤销
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">请求类型：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <select class="form-control" name="reqType" id="reqType">
                                            <option value="">-请选择-</option>
                                            <option value="0" <#if pushTask.reqType == 0>selected</#if>>内部请求
                                            </option>
                                            <option value="1" <#if pushTask.reqType == 1>selected</#if>>外部请求
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">请求地址：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="reqUrl" id="reqUrl"
                                               value="${pushTask.reqUrl}"
                                               placeholder="请输入请求地址"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="btn-group pull-right">
                                        <button id="query" type="submit" class="btn green" style="margin-bottom: 10px;"
                                                onclick="submitForm();">查询
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-scrollable table-hover">
                    <thead>
                    <tr>
                        <th width="25%" class="label-hide-overflow">订单号</th>
                        <th width="20%" class="label-hide-overflow">请求地址</th>
                        <th width="10%">请求类型</th>
                        <th width="10%">推送状态</th>
                        <th width="10%">创建时间</th>
                        <th width="25%">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as ptd>
                        <tr>
                            <td title="${ptd.flowNo}" class="label-hide-overflow">${ptd.flowNo}</td>
                            <td title="${ptd.reqUrl}" class="label-hide-overflow">${ptd.reqUrl}</td>
                            <td>
                                <#if ptd.reqType == 0>内部请求
                                <#elseif ptd.reqType == 1>外部请求
                                </#if>
                            </td>
                            <td>
                                <#if ptd.status == 0>待推送
                                <#elseif ptd.status == 1>推送成功
                                <#elseif ptd.status == -1>撤销
                                </#if>
                            </td>
                            <td>${ptd.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                            <td>
                                <span style="margin-right: 4px; cursor: pointer; color: #337ab7;" onclick="pushTaskViewForm('${ptd.id}')">详情</span>
                                <#if ptd.reqData??>
                                    <span onclick="showData('${ptd.reqData?html}')"
                                          style="margin-right: 4px; cursor: pointer; color: #337ab7;">查看请求数据</span>
                                </#if>
                                <span style="margin-right: 4px; cursor: pointer; color: #337ab7;" onclick="selectNearestLog('${ptd.id}')">查询最近返回信息</span>
                                <#if ptd.status == 0>
                                    <span onclick='rePushData("${ptd.id}", "${ptd.reqUrl}")' style="margin-right: 4px; cursor: pointer; color: #337ab7;">推送</span>
                                    <span onclick='cancelPush("${ptd.id}")' style="margin-right: 4px; cursor: pointer; color: #337ab7;">撤销</span>
                                 </#if>

                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>
