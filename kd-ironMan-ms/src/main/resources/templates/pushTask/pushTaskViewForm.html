<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="description" content="">
    <meta name="author" content="">
    <title>推送详情页</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script type="text/javascript">

        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        /* 展示数据 */
        function showData(data){
            layer.open({
                type: 1,
                area: ['800px', '400px'],
                shadeClose: true,
                content: '<div style="padding: 10px; width: 100%; height: 100%;"><textarea style="width: 100%; height: 100%;">'+data+'</textarea></div>'
            });
        }

        $(document).ready(function () {


        })
    </script>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><a href="#">企业服务台</a> <i class="fa fa-circle"></i></li>
                    <li><a href="#">基础查询</a> <i class="fa fa-circle"></i></li>
                    <li><a href="${ctx}/pushTaskController/pushTaskList">推送查询</a> <i class="fa fa-circle"></i></li>
                    <li><span class="active">推送详情页</span></li>
                </ul>
            </div>
            <div class="portlet-body">

                <div class="col-sm-12 col-lg-offset-1 form-group">
                    <div class="col-sm-2 form-group text-right">
                        任务id：
                    </div>
                    <div class="col-sm-4 form-group">
                        ${pushTask.id}
                    </div>
                    <div class="col-sm-2 form-group text-right">
                        流水号：
                    </div>
                    <div class="col-sm-4 form-group">
                        ${pushTask.flowNo}
                    </div>
                </div>
                <div class="col-sm-12 col-lg-offset-1 form-group">
                    <div class="col-sm-2 form-group text-right">
                        期待返回状态字符：
                    </div>
                    <div class="col-sm-4 form-group">
                        ${pushTask.callParam}
                    </div>
                    <div class="col-sm-2 form-group text-right">
                        期待返回状态编码：
                    </div>
                    <div class="col-sm-4 form-group">
                        ${pushTask.callStatus}
                    </div>
                </div>
                <div class="col-sm-12 col-lg-offset-1 form-group">
                    <div class="col-sm-2 form-group text-right">
                        创建时间：
                    </div>
                    <div class="col-sm-4 form-group">
                        ${pushTask.createTime?string('yyyy-MM-dd HH:mm:ss')!'--'}
                    </div>
                    <div class="col-sm-2  form-group text-right">
                        发送数据：
                    </div>
                    <div class="col-sm-4 form-group">
                        <span style="margin-right: 4px; cursor: pointer; color: #337ab7;" onclick="showData('${pushTask.reqData?html}')">查看</span>
                    </div>
                </div>

                <form id="searchForm" class="form-horizontal" action="${ctx}/pushTaskController/form?operationType=view&id=${pushTask.id}"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="col-sm-12 form-group text-right">
                        <a href="#" onClick="javascript :history.back(-1);" class="btn green">返回</a>
                    </div>
                </form>

                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                    <tr>
                        <th>接收时间</th>
                        <th>状态</th>
                        <th>推送时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list page.list as pushTaskLog>
                    <tr>
                        <td>${pushTaskLog.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                        <td>
                            <#if pushTaskLog.status == '1'>
                                <font style="color: green">成功</font>
                            <#elseif pushTaskLog.status == '0'>
                                <font style="color: red">失败</font>
                            </#if>
                        </td>
                        <td>
                            ${pushTaskLog.createTime?string('yyyy-MM-dd HH:mm:ss')!"--"}
                        </td>
                        <td>
                            <span style="margin-right: 4px; cursor: pointer; color: #337ab7;" data-aa="${pushTaskLog.reqData}" onclick="showData('${pushTaskLog.reqData?html}')">查看请求数据</span>
                            <span style="margin-right: 4px; cursor: pointer; color: #337ab7;" onclick="showData('${pushTaskLog.resData?html}')">查看返回数据</span>
                        </td>
                    </tr>
                    </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>