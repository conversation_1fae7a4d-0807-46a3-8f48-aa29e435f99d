<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
    <!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>管理端资源管理</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<link href="${ctx}/metronic/global/plugins/jquery-treetable/css/jquery.treetable.css" rel="stylesheet" type="text/css" />
<link href="${ctx}/metronic/global/plugins/jquery-treetable/css/jquery.treetable.theme.default.css" rel="stylesheet" type="text/css" />
<#include "/common/jsResource.html">
<script src="${ctx}/metronic/global/plugins/jquery-treetable/jquery.treetable.js" type="text/javascript"></script>
<script type="text/javascript">

$(document).ready(function() {
	$("#resourceTable").treetable({clickableNodeNames : true, expandable : true}).treetable("expandAll");
	$("#tab${platform}").attr("class", "active");

});

function openNew(obj){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/resourceController/form?operationType=new&platform='+obj,
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function openEdit(resourceId){
	var openWindowWidth = $(document).width() * 0.8 + "px";
	var offsetH = ($(window).height()/5 - 20 > 120 ? 120 : $(window).height()/5 - 20) + "px";
	layer.open({
		type: 2,
		area: openWindowWidth,
		offset : offsetH,
		fix: false, //不固定
		maxmin: true,
		content: '${ctx}/resourceController/form?id='+resourceId+'&operationType=edit&platform=0',
		success: function(layero, index){
		    layer.iframeAuto(index);
		}
	});
}

function submitForm(tabIdx) {
	if(tabIdx == 0){
		$("#tab").val("0");
	}
	$('#searchForm').submit();
}

</script>

</head>
<body>
	<!-- BEGIN PAGE BASE CONTENT -->
	<div class="row">
		<div class="col-sm-12">
			<!-- BEGIN EXAMPLE TABLE PORTLET-->
			<div class="portlet light portlet-fit bordered">
				<div class="portlet-title">
					<ul class="page-breadcrumb breadcrumb">
						<li><a>系统管理</a> <i class="fa fa-circle"></i></li>
						<li><span class="active">资源管理</span></li>
					</ul>
				</div>
				
				
				<div class="portlet-body">
					<form id="searchForm" method="post" action="${ctx}/resourceController/resourceList" class="form-horizontal">
						<input id="tab" name="platform" type="hidden" value="${platform}" />
					</form>
					<div class="table-toolbar">
						<div class="row">
							<div class="col-sm-12">
								<div class="btn-group pull-right">
									<button id="sample_editable_1_new" class="btn green" onclick="openNew('0')">新增</button>
								</div>
							</div>
						</div>
					</div>
					<ul class="nav nav-tabs">
						<li id="tab0"><a data-toggle="tab" onclick="submitForm(0);"> 系统端 </a></li>
					</ul>
					<table class="table table-striped table-bordered table-hover table-header-fixed"
						id="resourceTable">
						<thead>
							<tr>
								<th>资源名称</th>
								<th>类型</th>
								<th>链接</th>
								<th>排序</th>
								<th>显示</th>
								<th>shiro权限代码</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list resourceList as resource>
								<tr data-tt-id="${resource.id}" <#if resource.parentId != null>data-tt-parent-id="${resource.parentId}"</#if>>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${resource.name}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">
										<#if resource.type == 0>
											目录
										<#elseif resource.type == 1>
											菜单
										<#elseif resource.type == 2>
											权限
										</#if>
									</td>
									<td class="col-sm-3" style="word-wrap:break-word;word-break:break-all;">${resource.link}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">${resource.idx}</td>
									<td class="col-sm-1" style="word-wrap:break-word;word-break:break-all;">
										<#if resource.isShow == 1>
											显示
										<#else>
											隐藏
										</#if>
									</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">${resource.shiroPermission}</td>
									<td class="col-sm-2" style="word-wrap:break-word;word-break:break-all;">
										<a href="#" onclick="openEdit('${resource.id}')">编辑</a>
										<a href="${ctx}/resourceController/deleteResource?id=${resource.id}&platform=0" onclick="return confirm('包含删除连带子菜单，确认要执行 删除 操作吗？', this.href)">删除</a>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
				</div>
			</div>
			<!-- END EXAMPLE TABLE PORTLET-->
		</div>
	</div>
	<!-- END PAGE BASE CONTENT -->
</body>
</html>