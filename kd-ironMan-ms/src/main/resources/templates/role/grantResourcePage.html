<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
<meta charset="utf-8" />
<title>分配资源</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1" name="viewport" />
<meta content="" name="description" />
<meta content="" name="author" />
<#include "/common/cssResource.html">
<!-- BEGIN PAGE LEVEL PLUGINS -->
<link href="${ctx}/metronic/global/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet" type="text/css" />
<!-- END PAGE LEVEL PLUGINS -->

<#include "/common/jsResource.html">
<!-- BEGIN PAGE LEVEL PLUGINS -->
<script src="${ctx}/metronic/global/plugins/jstree/dist/jstree.min.js" type="text/javascript"></script>
<!-- END PAGE LEVEL PLUGINS -->

<script type="text/javascript">

	$(document).ready(function() {
		
		$('#resourceGrantTree').jstree({
			'plugins' : [ "wholerow", "checkbox" ],
			'checkbox' : {  
				// 级联选中  
                'three_state' : true,
                // 有三个选项：up, down, undetermined
                'cascade' : 'undetermined'
            },  
			'core' : {
				"themes" : {
					"responsive" : false
				},
				'data' : [ 
				<#list resourceGrantList as rg>
					{	"id" : "${rg.id}", 
						<#if rg.parentId != null>
							"parent" : "${rg.parentId}",
						<#else>
							"parent" : "#", 
						</#if>
						<#if rg.type == 0>
							"state" : {
								"opened" : true
								,"selected" : <#if rg.isLeaf && rg.selected == 1>true<#else>false</#if>
							},
							"icon" : "fa fa-folder icon-state-warning icon-lg",
						<#elseif rg.type == 1>
							"state" : {
								"opened" : true
								<#if rg.isLeaf && rg.selected == 1>
								,"selected" : true
								</#if>
							},
							"icon" : "fa fa-file icon-state-success",
						<#else>
							"state" : {
								<#if rg.isLeaf && rg.selected == 1>
								"selected" : true
								</#if>
							},
							"icon" : "fa fa-leaf icon-state-danger",
						</#if>
						"text" : "${rg.name}" 
					},
				</#list>
				]
			}
		});
	});

	function grantResource() {
		var undeterminedIds = "";
		$("#resourceGrantTree li").has("i[class*='jstree-undetermined']").each(function(){
			undeterminedIds += $(this).attr("id") + ",";
    		
    	});
		
		// 获取选中的叶子节点 -- get_bottom_checked 
		// 获取所有选中的节点 -- get_checked
		var checkedIds = $('#resourceGrantTree').jstree().get_checked();
		
		var resourceIds = undeterminedIds + checkedIds;
		
		$.ajax({
			url : "${ctx}/roleController/grantResource",
			type : "POST",
			data : {
				roleId : '${roleId}',
				resourceIds : resourceIds
			},
			dataType : "json",
			success : function(data) {
				var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
				parent.layer.close(index);
			}
		});
	}
</script>

</head>
<body>
	<div class="portlet light bordered">
		<div class="portlet-body form">
			<!-- BEGIN FORM-->
			<form id="inputForm" class="form-horizontal form-row-seperated">
				<div class="form-body">
					<div id="resourceGrantTree" class="tree"> </div>
				</div>
				<div class="form-actions">
					<div class="row">
						<div class="col-sm-3">
						</div>
						<div class="col-sm-9">
							<button type="button" class="btn green" onclick="grantResource()">提交</button>
							<button type="button" class="btn default" onclick="parent.layer.closeAll();">取消</button>
						</div>
					</div>
				</div>
			</form>
		</div>
		<!-- END FORM-->
	</div>
</body>
</html>