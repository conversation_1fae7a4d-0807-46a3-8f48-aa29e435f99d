<!--[if IE 8]>
<html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]>
<html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" xmlns="http://www.w3.org/1999/html">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <title>查看任务日志</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <script type="text/javascript">
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }
    </script>
    <style>
        .isEffective {
            color: green;
        }

        .unEffective {
            color: red;
        }

        .th-overflow {
            max-width: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="portlet light bordered">
        <div class="portlet-body">
            <form id="searchForm" action="${ctx}/scheduledController/log" method="post">
                <input id="pageNum" name="pageNum" type="hidden" value="${page.pageNum}"/>
                <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                <input id="scheduledId" name="scheduledId" type="hidden" value="${scheduledId}"/>
            </form>
            <table class="table table-bordered table-striped table-hover">
                <thead>
                <tr>
                    <th style="width:60%;">日志信息</th>
                    <th>执行状态</th>
                    <th>创建时间</th>
                </tr>
                </thead>
                <tbody>
                <#list page.list as scheduledLog>
                    <tr>
                        <td title="${scheduledLog.msg?html}" class="th-overflow">
                            ${scheduledLog.msg}
                        </td>
                        <#if scheduledLog.status == 1>
                            <td class="status isEffective">成功</td>
                        <#elseif scheduledLog.status == 0>
                            <td class="status unEffective">失败</td>
                        <#else>
                            <td>未知</td>
                        </#if>
                        <td>${scheduledLog.createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
                    </tr>
                </#list>
                </tbody>
            </table>
        </div>
    </div>
    <!-- END PAGE BASE CONTENT -->
    <@sc.pagination page=page />
</div>
</body>
</html>