<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/cssResource.html">
    <#include "/common/jsResource.html">
    <link href="${ctx}/metronic/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"/>
    <link href="${ctx}/metronic/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/>

    <script src="${ctx}/metronic/global/plugins/select2/js/select2.js" type="text/javascript"></script>
    <script src="${ctx}/metronic/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        const loader =  new Loaders({style:"rectangle"});
        function page(n, s) {
            $("#pageNum").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }

        var scrollTop;

        $(document).ready(function () {

            // 业务标签 select2初始化
            $("#applyType").select2({
                placeholder: "请选择",
                width: null
            });

            $("#log-claimCaseNo").select2({
                placeholder: "请选择",
                width: null
            });

            // 业务标签 select2初始化
            var labelList = [];
        <#if labelShowMap?? && (labelShowMap.keySet()?size>0)>
            <#list labelShowMap.keySet() as key>
            labelList.push({id:'${key}',text:'${labelShowMap.get(key).msg}'})
            </#list>
            </#if>
            console.log(labelList);
            $("#label").select2({
                placeholder: "请选择",
                width: null,
                data: labelList
            });
            var nowLabel =[];
        <#if claimCaseVo.label?exists>
                <#list claimCaseVo.label?split(",") as code>
                nowLabel.push("${code}");
        </#list>
            console.log(nowLabel);
            $("#label").val(nowLabel).trigger('change');
        </#if>
        });

        // 计算滚动高度
        function calculationScrollTop() {
            var ifm = parent.document.getElementById("contentFrame");
            scrollTop = $(window.parent).scrollTop() + 150;
            if (!ifm) {
                scrollTop = 300;
            }
            return scrollTop + "px";
        }

        function statusSwitch(status) {
            $("#status").val(status);
            $("#searchForm").submit();
        }
        function goInsert() {
            scrollTop = calculationScrollTop();
            layer.open({
                type: 2,
                title: '赔付信息',
                area: ['800px'],
                offset: scrollTop,
                fix: false, //不固定
                maxmin: true,
                content: "${ctx}/dictController/addSubsidiaryLabel",
                success: function (layero, index) {
                    layer.iframeAuto(index);
                }
            });
        }

    </script>
    <style>

        .select2-dropdown {
            z-index: 19891099 !important;
        }

        /*loader需要的遮罩层css start */
        #screen {
            height: 100%;
            width: 100%;
            z-index: 198910141;
            position: fixed;
            background: rgba(0,0,0,0.3);
            top:0;
        }

        #screen #screenLoading{
            margin: 0 auto;
            top: 40%;
            transform: translateY(-50%);
            background: greenyellow;
        }
        /*loader需要的遮罩层css end*/

        .clear-padding {
            padding: 0px !important;
        }

        .form-active > div {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border: 1px solid #e7ecf1;
            border-bottom: 0;
            border-right: 0;
            overflow: hidden;
            vertical-align: bottom;
            padding: 0px;
            display: inline-block;
        }
        .form-active > div > ul {
            margin: 0px;
            padding: 0px;
            list-style: none;
            display: flex;
        }
        .li-default {
            display: table-cell;
            border-bottom: 0;
            padding: 6px 70px 6px 30px;
            font-size: 17px;
            font-weight: bold;
            color: #3D3D3D;
            cursor: pointer;
            border-right: 1px solid #e7ecf1;
        }
        .li-default:hover {
            background: #0b94ea;
            color: #fff;
        }
        .li-blue {
            background: #0b94ea ;
            color: #fff;
        }
        .span-type {
            display: inline-block;
            background: #0b94ea;
            color: #fff;
            font-size: 10px;
            border-radius: 5px;
            margin:3px;
            padding: 2px;
        }
        td > a {
            display: inline-block;
            margin: 3px;
        }

        .td-overflow {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .labelGroup span{
            margin-left: 5px;
            margin-right: 5px;
            background-color: #1676FF;
            color: white;
            border-radius: 2px;
        }

        .btn-bule {
            color: #FFFFFF;
            background-color: #1676FF;
            border-color: #1676FF;
            margin-left: 3%;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-sm-12">
        <div class="portlet light portlet-fit bordered">
            <div class="portlet-title">
                <ul class="page-breadcrumb breadcrumb">
                    <li><span>饿了么</span> <i class="fa fa-circle"></i></li>
                    <li><span class="active">渠道特殊标签管理</span></li>
                </ul>
            </div>
            <div class="portlet-body">
                <!-- BEGIN FORM-->
                <form id="searchForm" class="form-horizontal"
                      method="post">
                    <input id="pageNum" name="pageNum" type="hidden" value="1"/>
                    <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="control-label col-sm-3" style="padding-right: 0">子商名称：</label>
                                    <div class="col-sm-8" style="padding-left: 0;">
                                        <input type="text" class="form-control" name="dlabel"
                                               value="${dictReq.dlabel}"
                                               placeholder="请输入"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions" action="${ctx}/dictController/dictSubsidiaryLabelList" method="post">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group pull-right" style="margin-bottom: 10px;margin-right: 30px;">
                                    <button id="query" type="submit" class="btn btn-bule" style="margin-bottom: 10px;margin-right: 10px;">查询</button>
                                    <button id="insert" type="button" class="btn yellow" style="margin-bottom: 10px;" onclick="goInsert()">新增</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <table class="table table-striped table-bordered table-hover table-header-fixed">
                    <thead>
                        <tr>
                            <th width="10%">子商名称</th>
                            <th width="10%">省</th>
                            <th width="10%">市</th>
                            <th width="12%">区</th>
                            <th width="13%">功能</th>
                        </tr>
                    </thead>
                    <tbody>
                        <#list list as vo>
                            <tr>
                                <td title="">${vo.subsidiaryName}</td>
                                <td title="">${vo.province}</td>
                                <td title="">${vo.city}</td>
                                <td title="">${vo.district}</td>
                                <td title="">
                                    <a href="${ctx}/dictController/deleteSubsidiaryLabelDict?id=${vo.dictId}" onclick="return confirm('确认要执行 删除 操作吗？', this.href)">删除</a>
                                </td>
                            </tr>
                        </#list>
                    </tbody>
                </table>
                <div class="modal fade" id="saveBtnModal" tabindex="-1" role="basic" aria-hidden="true">
                    <div class="modal-dialog">
                        <img src="${ctx}/images/load.gif">
                    </div>
                </div>
            </div>
            <!-- END FORM-->
        </div>
    </div>
</div>
<@sc.pagination page=page />
</body>
</html>