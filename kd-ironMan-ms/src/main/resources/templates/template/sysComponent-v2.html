<#macro pagination searchForm>

<#assign searchForm="searchForm">
<#assign message="">

<div class="dataTables_paginate">
<ul class="pagination">

<#if page.isFirstPage>
<li class="disabled"><a href="javascript:">&#171; 上一页</a></li>
<#else>
<li><a href="javascript:" onclick="page(${page.prePage},${page.pageSize});">&#171; 上一页</a></li>
</#if>

<#if (page.firstPage == 2)>
<li><a href="javascript:" onclick="page(1,${page.pageSize});">1</a></li>
</#if>

<#if (page.firstPage > 2)>
<li><a href="javascript:" onclick="page(1,${page.pageSize});">1</a></li>
<li class="disabled"><a href="javascript:">...</a></li>
</#if>

<#list page.navigatepageNums as n>
<#if n == page.pageNum>
<li><a href="javascript:" onclick="page(${n},${page.pageSize});" style="background-color:#f2f6f9"><b>${n}</b></a></li>
<#else>
<li><a href="javascript:" onclick="page(${n},${page.pageSize});">${n}</a></li>
</#if>
</#list>

<#if (page.lastPage + 1 == page.pages)>
<li><a href="javascript:" onclick="page(${page.pages},${page.pageSize});">${page.pages}</a></li>
</#if>

<#if (page.lastPage + 1 < page.pages)>
<li class="disabled"><a href="javascript:">...</a></li>
<li><a href="javascript:" onclick="page(${page.pages},${page.pageSize});">${page.pages}</a></li>
</#if>

<#if page.isLastPage>
<li class="disabled"><a href="javascript:">下一页 &#187;</a></li>
<#else>
<li><a href="javascript:" onclick="page(${page.nextPage},${page.pageSize});">下一页 &#187;</a></li>
</#if>

<li class="disabled controls"><a href="javascript:">当前 
<input type="text" value="${page.pageNum}" onkeypress="var e=window.event||this;var c=e.keyCode||e.which;if(c==13)page(this.value,${page.pageSize});" onclick="this.select();"/> / 
<input type="text" value="${page.pageSize}" onkeypress="var e=window.event||this;var c=e.keyCode||e.which;if(c==13)page(${page.pageNum},this.value);" onclick="this.select();"/> 条，共 ${page.total} 条${message!}</a></li>

</ul>
</div>
<script type="text/javascript">
	var oldAction = $("#${searchForm}").attr("action");
	var newAction = oldAction;
	if (!oldAction.match("[\?]")) {
		newAction = oldAction + "?t=1";
	}

	function page(n,s){
		$("#${searchForm}").attr("action", newAction + "&pageNum=" + n + "&pageSize=" + s);
		$("#${searchForm}").submit();
		return false;
	}

</script>

</#macro>